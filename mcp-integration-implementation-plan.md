# MCP Integration Implementation Plan

## Overview

This document outlines the atomic, testable implementation steps for integrating the MCP (Model Context Protocol) server into the voice connector application for customer identification via ANI (Automatic Number Identification).

## Architecture Analysis

Based on the existing codebase patterns:
- **Service Pattern**: Following LiteLLM provider pattern with HTTP client integration
- **Event-Driven**: Integration with existing state machine architecture
- **Error Handling**: Graceful fallback when MCP server unavailable
- **Configuration**: Environment-based configuration following existing patterns

## Implementation Phases

### Phase 1: Foundation & Types
**Goal**: Establish MCP service foundation with proper TypeScript interfaces

#### Step 1.1: Create MCP Types Interface
- [ ] Create `src/services/mcp/types.ts`
- [ ] Define `MCPRequest`, `MCPResponse`, `MCPError` interfaces
- [ ] Define `CustomerInfo` interface with ANI, name, metadata fields
- [ ] Define `CustomerLookupResult` interface with success/error states
- [ ] **Test**: TypeScript compilation passes
- [ ] **Success Criteria**: All types properly exported and importable

#### Step 1.2: Create MCP Client Foundation
- [ ] Create `src/services/mcp/mcp-client.ts` with basic structure
- [ ] Implement constructor with configuration
- [ ] Add connection test method (returns boolean)
- [ ] **Test**: Unit test for client instantiation
- [ ] **Success Criteria**: Client can be created and configured

#### Step 1.3: Create MCP Service Foundation
- [ ] Create `src/services/mcp/mcp-service.ts` with basic structure
- [ ] Implement initialization method
- [ ] Add availability check method
- [ ] **Test**: Unit test for service instantiation and initialization
- [ ] **Success Criteria**: Service follows existing service patterns

### Phase 2: MCP Protocol Implementation
**Goal**: Implement core MCP protocol communication

#### Step 2.1: Implement Tool Discovery
- [ ] Add `listTools()` method to MCPClient
- [ ] Implement MCP JSON-RPC request/response handling
- [ ] Add error handling for connection failures
- [ ] **Test**: Mock MCP server responses for tool discovery
- [ ] **Success Criteria**: Can discover available tools from MCP server

#### Step 2.2: Implement Tool Execution
- [ ] Add `callTool()` method to MCPClient
- [ ] Implement parameter passing and response parsing
- [ ] Add timeout and retry logic
- [ ] **Test**: Mock tool execution with various response formats
- [ ] **Success Criteria**: Can execute tools and parse responses

#### Step 2.3: Add Customer Lookup Logic
- [ ] Implement `lookupCustomer(ani: string)` in MCPService
- [ ] Add automatic tool discovery for customer lookup
- [ ] Implement response parsing to CustomerInfo format
- [ ] **Test**: Unit tests with mocked MCP responses
- [ ] **Success Criteria**: ANI lookup returns structured customer data

### Phase 3: Caching & Performance
**Goal**: Add intelligent caching and performance optimizations

#### Step 3.1: Implement Customer Cache
- [ ] Add in-memory cache for customer lookups
- [ ] Implement cache key strategy (ANI-based)
- [ ] Add cache hit/miss logging
- [ ] **Test**: Verify cache behavior with repeated lookups
- [ ] **Success Criteria**: Subsequent lookups use cache, reducing MCP calls

#### Step 3.2: Add Error Handling & Fallbacks
- [ ] Implement graceful degradation when MCP unavailable
- [ ] Add retry logic with exponential backoff
- [ ] Ensure session continues without customer data if MCP fails
- [ ] **Test**: Simulate MCP server failures and verify fallback behavior
- [ ] **Success Criteria**: Application remains functional when MCP is down

### Phase 4: Session Integration
**Goal**: Integrate MCP service into existing session lifecycle

#### Step 4.1: Extend Session Interface
- [ ] Add customer information fields to session interface
- [ ] Add MCP service dependency to session constructor
- [ ] Implement customer data getter methods
- [ ] **Test**: Session unit tests with customer data
- [ ] **Success Criteria**: Session can store and retrieve customer information

#### Step 4.2: Integrate with OpenMessageHandler
- [ ] Modify OpenMessageHandler to trigger customer lookup when ANI available
- [ ] Add customer lookup after `setClientAni(ani)` call
- [ ] Implement async customer lookup without blocking session initialization
- [ ] **Test**: Integration test with mock ANI data
- [ ] **Success Criteria**: Customer lookup triggered during session setup

#### Step 4.3: Add State Machine Integration
- [ ] Create `CustomerLookupAction` for state machine
- [ ] Add customer lookup to INITIALIZING state actions
- [ ] Emit events for successful/failed customer identification
- [ ] **Test**: State machine tests with customer lookup scenarios
- [ ] **Success Criteria**: Customer lookup integrated into session flow

### Phase 5: Bot Service Integration
**Goal**: Provide customer context to LLM for personalized responses

#### Step 5.1: Extend Bot Service Context
- [ ] Modify bot service to accept customer information
- [ ] Add customer context to LLM system messages
- [ ] Implement customer-aware prompt templates
- [ ] **Test**: Bot service tests with customer context
- [ ] **Success Criteria**: LLM receives customer information for personalization

#### Step 5.2: Add Customer-Aware Responses
- [ ] Implement customer greeting with name (if available)
- [ ] Add customer status awareness to bot responses
- [ ] Handle missing customer data gracefully
- [ ] **Test**: End-to-end tests with various customer scenarios
- [ ] **Success Criteria**: Bot provides personalized responses when customer data available

### Phase 6: Configuration & Environment
**Goal**: Proper configuration management and environment setup

#### Step 6.1: Environment Configuration
- [ ] Add MCP_SERVER_URL environment variable
- [ ] Add optional timeout and retry configuration
- [ ] Support both local (127.0.0.1:6274) and production URLs
- [ ] **Test**: Configuration loading tests
- [ ] **Success Criteria**: Service configurable via environment variables

#### Step 6.2: Service Registration
- [ ] Create singleton pattern for MCP service (following existing patterns)
- [ ] Add service initialization to application startup
- [ ] Implement graceful service disposal
- [ ] **Test**: Application startup/shutdown tests
- [ ] **Success Criteria**: MCP service properly managed in application lifecycle

### Phase 7: Testing & Documentation
**Goal**: Comprehensive testing and documentation

#### Step 7.1: Unit Test Coverage
- [ ] MCPClient unit tests with mocked HTTP responses
- [ ] MCPService unit tests with various scenarios
- [ ] Error handling and edge case tests
- [ ] **Test**: Achieve >90% code coverage
- [ ] **Success Criteria**: All components thoroughly tested

#### Step 7.2: Integration Tests
- [ ] End-to-end tests with mock MCP server
- [ ] Session integration tests with customer lookup
- [ ] Bot service integration tests with customer context
- [ ] **Test**: Full conversation flow with customer identification
- [ ] **Success Criteria**: Complete integration verified

#### Step 7.3: Documentation
- [ ] Create `src/services/mcp/mcp-service.md` with usage examples
- [ ] Update main README.md with MCP integration details
- [ ] Add configuration documentation
- [ ] **Test**: Documentation review and validation
- [ ] **Success Criteria**: Clear documentation for developers

## Testing Strategy

### Unit Tests
- Mock MCP server responses for predictable testing
- Test error conditions and edge cases
- Verify caching behavior and performance optimizations

### Integration Tests
- Use test MCP server or mock server for integration testing
- Test complete session flow with customer identification
- Verify bot service receives customer context correctly

### Manual Testing
- Test with actual MCP server running locally
- Verify MCP Inspector shows correct tool discovery
- Test various ANI scenarios and customer data formats

## Success Criteria

### Functional Requirements
- [x] Customer identification via ANI during session initialization
- [x] Graceful fallback when MCP server unavailable
- [x] Customer context provided to bot service for personalization
- [x] Caching to improve performance and reduce server load

### Non-Functional Requirements
- [x] Follows existing service architecture patterns
- [x] Comprehensive error handling and logging
- [x] Configurable via environment variables
- [x] >90% test coverage
- [x] Documentation collocated with code

### Integration Requirements
- [x] Seamless integration with existing state machine
- [x] No disruption to existing session flow
- [x] Compatible with current logging and metrics systems
- [x] Maintains application performance standards

## Risk Mitigation

### MCP Server Availability
- **Risk**: MCP server downtime affects customer identification
- **Mitigation**: Graceful fallback, caching, retry logic

### Performance Impact
- **Risk**: Customer lookup adds latency to session initialization
- **Mitigation**: Async lookup, caching, timeout configuration

### Data Privacy
- **Risk**: Customer information in logs or errors
- **Mitigation**: Sanitized logging, secure error handling

## Rollout Strategy

1. **Development**: Implement with local MCP server
2. **Testing**: Comprehensive testing with mock and real servers
3. **Staging**: Deploy to staging environment with production MCP server
4. **Production**: Gradual rollout with monitoring and fallback capabilities
