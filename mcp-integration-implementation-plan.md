# MCP Integration Refactoring Plan - Official SDK Migration

## Overview

This document outlines the atomic, testable implementation steps for **refactoring** the existing MCP implementation to use the official MCP TypeScript SDK. The current implementation has good architecture but lacks MCP protocol compliance. This migration will maintain existing service patterns while ensuring proper MCP standards compliance.

## MCP Server Context

Based on previous requirements and analysis:

### MCP Server Details
- **Local Development**: `http://127.0.0.1:6274`
- **Production**: `https://aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2/mcp`
- **Primary Purpose**: Customer identification (ANI) and information for voice connector sessions, connection to other information systems
- **Protocol**: Standard MCP implementation with tool-based architecture

### Current Status
- MCP server is currently running locally
- MCP Inspector shows available tools (e.g., "GetDateTime" visible in screenshot)
- Server follows standard MCP protocol for tool discovery and execution
- Integration needed for seamless customer identification during voice sessions

### Integration Requirements
- **ANI Processing**: Customer lookup triggered when ANI is available during session initialization
- **Barge-in Preservation**: Must not interfere with existing barge-in functionality
- **Event-Driven Architecture**: Integration with existing state machine without disrupting current flow
- **Graceful Degradation**: Application must continue functioning when MCP server unavailable

## Current Implementation Analysis

### Existing Strengths to Preserve
- **Error Handling**: Custom error types and comprehensive logging
- **Service Architecture**: Clean separation following existing patterns
- **Test Coverage**: Good unit test foundation
- **Environment Configuration**: Clean env-based setup
- **Graceful Degradation**: Proper fallback behavior

### Critical Issues to Fix
- **Protocol Non-Compliance**: Custom JSON-RPC instead of MCP standard
- **Missing MCP Features**: No proper initialization, capability negotiation
- **Type Safety**: Loose typing with `any` and generic types
- **Limited Functionality**: Only basic tool calling implemented

## Migration Strategy

**Approach**: Incremental refactoring maintaining existing interfaces while replacing internals with official SDK

## TDD Refactoring Phases - Official SDK Migration

**Test Driven Development (TDD) Paradigm:**
For every atomic step, follow this workflow:
- [ ] **TDD Step:** Write a failing test (unit/integration/E2E as appropriate) before implementing the feature
- [ ] Implement the feature to make the test pass
- [ ] Add at least one negative/edge case test for each integration point
- [ ] Organize all new test files in `__tests__` folders alongside implementation files
- [ ] Use mocking libraries for external dependencies
- [ ] Use descriptive test names and `describe`/`it` blocks
- [ ] Refactor only with all tests passing (green-to-green refactoring)
- [ ] Add regression tests for any discovered bugs

### Phase 1: Official SDK Installation & Setup
**Goal**: Install official MCP SDK and prepare for migration

#### Step 1.1: Install Official MCP SDK
- [ ] **TDD Step:** Write failing test that imports official SDK types
- [ ] Install `@modelcontextprotocol/sdk` via yarn
- [ ] Verify TypeScript compatibility and imports
- [ ] **Test:** Import test passes without compilation errors
- [ ] **Success Criteria:** Official SDK available and importable

#### Step 1.2: Create SDK Client Wrapper
- [ ] **TDD Step:** Write failing test for SDK client instantiation
- [ ] Create `src/services/mcp/sdk-client.ts` with official SDK client
- [ ] Implement basic connection using official SDK
- [ ] Add proper MCP initialization handshake
- [ ] **Test:** Unit test for SDK client creation and connection
- [ ] **Success Criteria:** Official SDK client can connect to MCP server

#### Step 1.3: Update Type Definitions for SDK
- [ ] **TDD Step:** Write failing test for updated types compatibility
- [ ] Update `src/services/mcp/types.ts` to use official SDK types
- [ ] Import and re-export relevant types from `@modelcontextprotocol/sdk/types.js`
- [ ] Keep existing `CustomerInfo` and `CustomerLookupResult` interfaces
- [ ] Remove custom `MCPRequest`/`MCPResponse` (use SDK types)
- [ ] **Test:** TypeScript compilation passes with SDK types
- [ ] **Success Criteria:** Types are compatible with official SDK

### Phase 2: Migrate MCPClient to Official SDK
**Goal**: Replace custom MCPClient with official SDK client

#### Step 2.1: Create SDK-Based Client Implementation
- [ ] **TDD Step:** Write failing test for tool discovery using official SDK
- [ ] Create new `src/services/mcp/mcp-client-sdk.ts` using official SDK
- [ ] Implement `listTools()` using SDK's `client.listTools()`
- [ ] Implement `callTool()` using SDK's `client.callTool()`
- [ ] Add connection management and error handling
- [ ] **Test:** Unit tests with mocked SDK client
- [ ] **Success Criteria:** SDK-based client implements same interface as current client

#### Step 2.2: Add Transport Configuration
- [ ] **TDD Step:** Write failing test for HTTP transport configuration
- [ ] Configure HTTP transport for official SDK client
- [ ] Support both local and production MCP server URLs
- [ ] Add timeout and retry configuration
- [ ] **Test:** Connection tests with different configurations
- [ ] **Success Criteria:** SDK client can connect to MCP server with proper configuration

#### Step 2.3: Implement Error Handling Wrapper
- [ ] **TDD Step:** Write failing tests for various error scenarios
- [ ] Wrap SDK client with custom error handling
- [ ] Map SDK errors to existing custom error types
- [ ] Preserve existing logging and retry behavior
- [ ] **Test:** Error handling tests with mocked SDK failures
- [ ] **Success Criteria:** Error handling maintains existing behavior

### Phase 3: Migrate MCPService to Use SDK Client
**Goal**: Update MCPService to use official SDK while maintaining existing interface

#### Step 3.1: Update MCPService Implementation
- [ ] **TDD Step:** Write failing test for MCPService using SDK client
- [ ] Replace custom client with SDK-based client in MCPService
- [ ] Update `initialize()` method to use SDK initialization
- [ ] Update `isAvailable()` to use SDK connection status
- [ ] Maintain existing caching and graceful degradation
- [ ] **Test:** Unit tests for updated MCPService
- [ ] **Success Criteria:** MCPService maintains same public interface


#### Step 3.2: Update Customer Lookup Logic
- [ ] **TDD Step:** Write failing test for customer lookup using SDK
- [ ] Update `lookupCustomer()` method to use SDK tool calling
- [ ] Implement proper tool discovery for customer lookup tools
- [ ] Maintain existing response parsing and caching
- [ ] **Test:** Customer lookup tests with mocked SDK responses
- [ ] **Success Criteria:** Customer lookup works with SDK client

#### Step 3.3: Preserve Existing Service Patterns
- [ ] **TDD Step:** Write failing test for service factory pattern
- [ ] Update `createMCPServiceFromEnv()` to use SDK client
- [ ] Maintain singleton pattern and lifecycle management
- [ ] Preserve existing configuration and environment handling
- [ ] **Test:** Service creation and lifecycle tests
- [ ] **Success Criteria:** Service patterns remain unchanged

### Phase 4: Integration Testing & Validation
**Goal**: Ensure SDK migration doesn't break existing functionality

#### Step 4.1: Replace Implementation Files
- [ ] **TDD Step:** Write comprehensive integration tests before replacement
- [ ] Backup current `mcp-client.ts` as `mcp-client-legacy.ts`
- [ ] Replace `mcp-client.ts` with SDK-based implementation
- [ ] Update imports and dependencies
- [ ] **Test:** All existing tests pass with new implementation
- [ ] **Success Criteria:** Drop-in replacement works without breaking changes

#### Step 4.2: Run Regression Tests
- [ ] **TDD Step:** Execute all existing MCP tests with SDK implementation
- [ ] Verify all unit tests pass without modification
- [ ] Run integration tests with real MCP server
- [ ] Check performance benchmarks and error handling
- [ ] **Test:** Full test suite passes with SDK implementation
- [ ] **Success Criteria:** No regressions in functionality or performance

#### Step 4.3: Clean Up Legacy Code
- [ ] **TDD Step:** Write test to ensure legacy code is not imported
- [ ] Remove legacy `mcp-client-legacy.ts` file
- [ ] Remove custom error types if SDK provides equivalents
- [ ] Update documentation and comments
- [ ] **Test:** No references to legacy implementation remain
- [ ] **Success Criteria:** Clean codebase with only SDK-based implementation

### Phase 5: Enhanced SDK Features (Optional)
**Goal**: Leverage additional SDK features beyond basic tool calling

#### Step 5.1: Implement Resource Support
- [ ] **TDD Step:** Write failing test for resource discovery and reading
- [ ] Add support for MCP resources using SDK
- [ ] Implement resource-based customer data exposure
- [ ] Add resource caching and management
- [ ] **Test:** Resource discovery and reading tests
- [ ] **Success Criteria:** Can discover and read MCP resources

#### Step 5.2: Add Prompt Support
- [ ] **TDD Step:** Write failing test for prompt discovery and execution
- [ ] Add support for MCP prompts using SDK
- [ ] Implement prompt-based customer interaction templates
- [ ] Add prompt parameter handling
- [ ] **Test:** Prompt discovery and execution tests
- [ ] **Success Criteria:** Can discover and execute MCP prompts

### Phase 6: Performance & Production Readiness
**Goal**: Ensure SDK implementation meets production requirements

#### Step 6.1: Performance Optimization
- [ ] **TDD Step:** Write performance benchmark tests
- [ ] Optimize SDK client configuration for voice connector use case
- [ ] Implement connection pooling if needed
- [ ] Add request/response caching strategies
- [ ] **Test:** Performance tests meet existing benchmarks
- [ ] **Success Criteria:** SDK implementation performs as well as custom implementation

#### Step 6.2: Production Configuration
- [ ] **TDD Step:** Write failing test for production configuration
- [ ] Update environment configuration for SDK client
- [ ] Add SDK-specific timeout and retry settings
- [ ] Configure transport options (HTTP vs stdio)
- [ ] **Test:** Configuration tests with production settings
- [ ] **Success Criteria:** Production-ready configuration available

### Phase 7: Final Testing & Documentation
**Goal**: Comprehensive testing and documentation for SDK migration

#### Step 7.1: Comprehensive Test Suite
- [ ] **TDD Step:** Ensure all SDK features have corresponding tests
- [ ] Update all unit tests to use SDK-based implementation
- [ ] Add integration tests with real MCP server using SDK
- [ ] Create end-to-end tests for voice connector with SDK
- [ ] **Test:** Achieve >95% code coverage with SDK implementation
- [ ] **Success Criteria:** Complete test coverage for SDK migration

#### Step 7.2: Migration Documentation
- [ ] **TDD Step:** Write test for documentation examples
- [ ] Update `src/services/mcp/mcp-service.md` with SDK usage examples
- [ ] Document migration process and differences from custom implementation
- [ ] Add troubleshooting guide for SDK-specific issues
- [ ] **Test:** Documentation examples work as written
- [ ] **Success Criteria:** Clear migration and usage documentation

#### Step 7.3: Performance Validation
- [ ] **TDD Step:** Write performance regression tests
- [ ] Compare SDK performance with legacy implementation
- [ ] Validate voice connector latency requirements
- [ ] Test under load with multiple concurrent sessions
- [ ] **Test:** Performance meets or exceeds legacy implementation
- [ ] **Success Criteria:** No performance regressions from SDK migration

### Phase 8: Production Deployment & Monitoring
**Goal**: Deploy SDK-based implementation to production with proper monitoring

#### Step 8.1: Deployment Preparation
- [ ] **TDD Step:** Write deployment validation tests
- [ ] Create deployment checklist for SDK migration
- [ ] Update CI/CD pipeline for SDK dependencies
- [ ] Prepare rollback plan to legacy implementation
- [ ] **Test:** Deployment tests pass in staging environment
- [ ] **Success Criteria:** Ready for production deployment

#### Step 8.2: Production Monitoring
- [ ] **TDD Step:** Write tests for monitoring and alerting
- [ ] Add SDK-specific metrics and logging
- [ ] Configure alerts for MCP connection issues
- [ ] Monitor performance impact of SDK migration
- [ ] **Test:** Monitoring tests verify alert conditions
- [ ] **Success Criteria:** Production monitoring in place

#### Step 8.3: Post-Migration Validation
- [ ] **TDD Step:** Write post-deployment validation tests
- [ ] Validate production functionality with SDK
- [ ] Monitor error rates and performance metrics
- [ ] Collect feedback from voice connector usage
- [ ] **Test:** Production validation tests pass
- [ ] **Success Criteria:** Successful production migration completed

## Testing Strategy

### TDD Workflow
- For every new feature or bugfix, write a failing test before implementation.
- Add at least one negative/edge case test for each integration point.
- Organize all new test files in `__tests__` folders alongside implementation files.
- Use mocking libraries (e.g., `nock`, `msw`) for MCP server and event emitter/state machine integration.
- Use descriptive test names and `describe`/`it` blocks.
- Refactor only with all tests passing (green-to-green refactoring).
- Add regression tests for any discovered bugs.
- Ensure all tests are run in CI for every commit/merge.

### Unit Tests
- Mock MCP server responses for predictable testing
- Test error conditions and edge cases
- Verify caching behavior and performance optimizations

### Integration Tests
- Use test MCP server or mock server for integration testing
- Test complete session flow with customer identification (success, failure, fallback)
- Verify bot service receives customer context correctly

### Manual Testing
- Start MCP server locally: `cd ~/Dev/aidcc-ccczautomationmcpserver && npm start`
- Verify MCP server running on `http://127.0.0.1:6274`
- Use MCP Inspector to verify tool discovery and execution
- Test various ANI scenarios and customer data formats
- Verify customer lookup tools are available (beyond GetDateTime shown in screenshot)

## Success Criteria for SDK Migration

### Functional Requirements
- [ ] **MCP Protocol Compliance**: Full adherence to MCP specification using official SDK
- [ ] **Backward Compatibility**: All existing functionality preserved during migration
- [ ] **Customer Identification**: ANI-based customer lookup works with SDK implementation
- [ ] **Graceful Degradation**: Application continues functioning when MCP server unavailable
- [ ] **Performance Parity**: SDK implementation performs as well as custom implementation

### Technical Requirements
- [ ] **Official SDK Integration**: Complete replacement of custom implementation with official SDK
- [ ] **Type Safety**: Full TypeScript support with proper SDK types
- [ ] **Error Handling**: Existing error handling patterns preserved and enhanced
- [ ] **Test Coverage**: >95% test coverage for all SDK-based code
- [ ] **Documentation**: Complete migration documentation and usage examples

### Integration Requirements
- [ ] **Service Patterns**: Maintains existing service architecture and patterns
- [ ] **State Machine**: No disruption to existing event-driven state machine
- [ ] **Session Flow**: Seamless integration with existing session lifecycle
- [ ] **Bot Service**: Customer context properly provided to LLM for personalization
- [ ] **Configuration**: Environment-based configuration preserved and enhanced

## Risk Mitigation for SDK Migration

### Migration Risks
- **Risk**: SDK migration breaks existing functionality
- **Mitigation**: Comprehensive test suite, gradual migration, rollback plan

### Performance Risks
- **Risk**: Official SDK introduces performance overhead
- **Mitigation**: Performance benchmarking, optimization, monitoring

### Compatibility Risks
- **Risk**: SDK doesn't support all current features
- **Mitigation**: Feature gap analysis, custom wrappers if needed, fallback options

### Dependency Risks
- **Risk**: New SDK dependency introduces vulnerabilities or conflicts
- **Mitigation**: Security audit, dependency scanning, version pinning

## Rollout Strategy for SDK Migration
1. **Phase 1**: Development with TDD approach, local testing with official SDK
2. **Phase 2**: Comprehensive testing with both mock and real MCP servers
3. **Phase 3**: Staging deployment with production MCP server and monitoring
4. **Phase 4**: Gradual production rollout with feature flags and rollback capability
5. **Phase 5**: Full migration completion and legacy code cleanup
