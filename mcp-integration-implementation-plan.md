# MCP Integration Implementation Plan

## Overview

This document outlines the atomic, testable implementation steps for integrating the MCP (Model Context Protocol) server into the voice connector application for customer identification via ANI (Automatic Number Identification).

## MCP Server Context

Based on previous requirements and analysis:

### MCP Server Details
- **Local Development**: `http://127.0.0.1:6274`
- **Production**: `https://aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2/mcp`
- **Primary Purpose**: Customer identification (ANI) and information for voice connector sessions, connection to other information systems
- **Protocol**: Standard MCP implementation with tool-based architecture

### Current Status
- MCP server is currently running locally
- MCP Inspector shows available tools (e.g., "GetDateTime" visible in screenshot)
- Server follows standard MCP protocol for tool discovery and execution
- Integration needed for seamless customer identification during voice sessions

### Integration Requirements
- **ANI Processing**: Customer lookup triggered when ANI is available during session initialization
- **Barge-in Preservation**: Must not interfere with existing barge-in functionality
- **Event-Driven Architecture**: Integration with existing state machine without disrupting current flow
- **Graceful Degradation**: Application must continue functioning when MCP server unavailable

## Architecture Analysis

Based on the existing codebase patterns:
- **Service Pattern**: Following LiteLLM provider pattern with HTTP client integration
- **Event-Driven**: Integration with existing state machine architecture
- **Error Handling**: Graceful fallback when MCP server unavailable
- **Configuration**: Environment-based configuration following existing patterns

## Implementation Phases
- **Test Driven Development (TDD) Paradigm:**
  For every atomic step, follow this workflow:
  - [ ] **TDD Step:** Write a failing test (unit/integration/E2E as appropriate) before implementing the feature.
  - [ ] Implement the feature to make the test pass.
  - [ ] Add at least one negative/edge case test for each integration point.
  - [ ] Organize all new test files in `__tests__` folders alongside implementation files.
  - [ ] Use mocking libraries (e.g., `nock`, `msw`) for MCP server and event emitter/state machine integration.
  - [ ] Use descriptive test names and `describe`/`it` blocks.
  - [ ] Refactor only with all tests passing (green-to-green refactoring).
  - [ ] Add regression tests for any discovered bugs.

### Phase 1: Foundation & Types
**Goal**: Establish MCP service foundation with proper TypeScript interfaces

#### Step 1.1: Create MCP Types Interface
- [ ] **TDD Step:** Write a failing TypeScript type test for `MCPRequest`, `MCPResponse`, `MCPError`, `CustomerInfo`, and `CustomerLookupResult` interfaces.
- [x] Create `src/services/mcp/types.ts`
- [x] Define `MCPRequest`, `MCPResponse`, `MCPError` interfaces
- [x] Define `CustomerInfo` interface with ANI, name, metadata fields
- [x] Define `CustomerLookupResult` interface with success/error states
- [x] **Test:** TypeScript compilation passes and type tests succeed
- [x] **Success Criteria:** All types properly exported and importable

#### Step 1.2: Create MCP Client Foundation
- [x] **TDD Step:** Write a failing unit test for MCP client instantiation in `src/services/mcp/__tests__/mcp-client.test.ts`
- [x] Create `src/services/mcp/mcp-client.ts` with basic structure
- [x] Implement constructor with configuration
- [x] Add connection test method (returns boolean)
- [x] **Test:** Unit test for client instantiation and configuration
- [x] **Success Criteria:** Client can be created and configured

#### Step 1.3: Create MCP Service Foundation
- [x] **TDD Step:** Write a failing unit test for service instantiation and initialization in `src/services/mcp/__tests__/mcp-service.test.ts`
- [x] Create `src/services/mcp/mcp-service.ts` with basic structure
- [x] Implement initialization method
- [x] Add availability check method
- [x] **Test:** Unit test for service instantiation and initialization
- [x] **Success Criteria:** Service follows existing service patterns

### Phase 2: MCP Protocol Implementation
**Goal**: Implement core MCP protocol communication

> **Note:** For initial implementation, only the `getDateTime` tool is available:
> ```json
> {
>   "name": "getDateTime",
>   "arguments": {
>     "format": "ISO"
>   }
> }
> ```
> The plan is adapted to use this tool instead of customer lookup.

#### Step 2.1: Implement Tool Discovery
- [x] **TDD Step:** Write a failing unit test for `listTools()` with mocked MCP server response in `src/services/mcp/__tests__/mcp-client.test.ts`
- [x] Add `listTools()` method to MCPClient
- [x] Implement MCP JSON-RPC request/response handling
- [x] Add error handling for connection failures (add negative test for connection failure)
- [x] **Test:** Mock MCP server responses for tool discovery
- [x] **Success Criteria:** Can discover available tools from MCP server

#### Step 2.2: Implement Tool Execution (getDateTime) and General Tool Invocation
- [x] **TDD Step:** Write a failing unit test for `callTool('getDateTime', { format: 'ISO' })` with various response formats and error cases
- [x] Add `callTool()` method to MCPClient
- [x] Implement parameter passing and response parsing for `getDateTime`
- [x] Add timeout and retry logic (add negative test for timeout)
- [x] **Test:** Mock tool execution for `getDateTime` with various response formats and error scenarios
- [x] **Success Criteria:** Can execute `getDateTime` tool and parse responses
- [x] **Enhancement:** Type-safe tool metadata (`MCPToolMetadata`) and generic tool invocation (`invokeTool`) implemented in MCPService and BotService. All tool discovery and invocation is now robust, type-safe, and logged at startup.


**Integration Test Pattern Note:**
It is possible and recommended to create an integration test that runs the connector, lets the LLM discover available tools, and invokes `getDateTime` to display the current time. This pattern validates the full tool-discovery and execution flow, ensuring the LLM can use the tool as intended. Such end-to-end tests are valuable for verifying real integration and should be included as part of the test suite.

### Phase 3: Error Handling & Fallbacks
**Goal**: Ensure robust error handling and graceful degradation

#### Step 3.1: Add Error Handling & Fallbacks
- [x] **TDD Step:** Write a failing test for MCP unavailable scenario and fallback logic
- [x] Implement graceful degradation when MCP unavailable
- [x] Add retry logic with exponential backoff
- [x] Ensure session continues without customer data if MCP fails
- [x] **Test:** Simulate MCP server failures and verify fallback behavior
- [x] **Success Criteria:** Application remains functional when MCP is down

### Phase 4: Session Integration
**Goal**: Integrate MCP service into existing session lifecycle

#### Step 4.1: Extend Session Interface
- [x] **TDD Step:** Write a failing unit test for session storing/retrieving tool results
- [x] Add tool result fields to session interface (e.g., lastToolResult)
- [x] Add MCP service dependency to session constructor
- [x] Implement tool result getter methods
- [x] **Test:** Session unit tests with tool data (success and error)
- [x] **Success Criteria:** Session can store and retrieve tool results

#### Step 4.2: Integrate with OpenMessageHandler
- [x] **TDD Step:** Write a failing integration test for tool invocation triggered by session start or user prompt in OpenMessageHandler
- [x] Modify OpenMessageHandler to trigger `getDateTime` tool invocation on session start or specific prompt
- [x] Implement async tool invocation without blocking session initialization
- [x] **Test:** Integration test with mock tool data and error scenarios
- [x] **Success Criteria:** Tool invocation triggered during session setup

- [x] **Note:** Tool invocation is now tested in a type-safe, test-only manner. The main application does not hardcode tool invocation at session startup; integration tests directly exercise MCP tool discovery and invocation.

#### Step 4.3: State Machine Integration (Not Applicable)

- [x] **Note:** Customer/tool lookup is now LLM-driven, not state machine-driven. No state machine action for customer lookup will be implemented. Integration tests will focus on LLM-driven tool invocation and end-to-end flows.

### Phase 5: Bot Service Integration
**Goal**: Provide customer context to LLM for personalized responses

#### Step 5.1: Extend Bot Service Context
- [ ] **TDD Step:** Write a failing unit test for bot service receiving customer information
- [ ] Modify bot service to accept customer information
- [x] Implement customer-aware prompt templates
- [x] **Test:** Bot service tests with customer context (present and missing)
- [x] **Success Criteria:** LLM receives customer information for personalization


### Phase 6: Configuration & Environment
**Goal**: Proper configuration management and environment setup

#### Step 6.1: Environment Configuration
- [ ] **TDD Step:** Write a failing test for configuration loading with different environments and timeouts
- [ ] Add MCP_SERVER_URL environment variable with defaults:
  - Local: `http://127.0.0.1:6274`
  - Production: `https://aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2/mcp`
- [ ] Add optional timeout and retry configuration
- [ ] Support environment-based URL switching
- [ ] **Test:** Configuration loading tests with both environments and error cases
- [ ] **Success Criteria:** Service configurable via environment variables

#### Step 6.2: Service Registration
- [x] **TDD Step:** Write a failing test for MCP service lifecycle (startup/shutdown)
- [x] Create singleton pattern for MCP service (following existing patterns)
- [x] Add service initialization to application startup
- [x] Implement graceful service disposal
- [x] **Test:** Application startup/shutdown tests
- [x] **Success Criteria:** MCP service properly managed in application lifecycle

### Phase 7: Testing & Documentation
**Goal**: Comprehensive testing and documentation

#### Step 7.1: Unit Test Coverage
- [ ] Ensure all MCPClient and MCPService features have unit tests with mocked HTTP responses and event emitters
- [ ] Add error handling and edge case tests for all integration points
- [ ] Use descriptive test names and `describe`/`it` blocks
- [ ] **Test:** Achieve >90% code coverage (enforced in CI)
- [ ] **Success Criteria:** All components thoroughly tested

#### Step 7.2: Integration Tests
- [ ] End-to-end tests with mock MCP server
- [ ] Session integration tests with customer lookup (success, failure, fallback)
- [ ] Bot service integration tests with customer context (present, missing, error)
- [ ] **Test:** Full conversation flow with customer identification and error scenarios
- [ ] **Success Criteria:** Complete integration verified

#### Step 7.3: Continuous Integration
- [ ] Ensure all new and existing tests are run in CI for every commit/merge
- [ ] Block merges on failing tests or insufficient coverage

#### Step 7.4: Documentation
- [ ] Create `src/services/mcp/mcp-service.md` with usage examples and TDD/test strategy
- [ ] Update main README.md with MCP integration and TDD/test coverage details
- [ ] Add configuration documentation
- [ ] **Test:** Documentation review and validation
- [ ] **Success Criteria:** Clear documentation for developers, including how to run and extend tests

Phase 8: Robust MCP HTTP Transport & Observability
Goal: Ensure the MCP client transport is production-grade, voice-optimized, and fully testable.

Step 8.1: Production-Ready HTTP Transport
[x] TDD Step: Write failing unit/integration tests for all error and edge cases (network, HTTP, JSON-RPC, malformed, timeout, retry).
[x] Implement _post using native fetch with:
[x] JSON-RPC 2.0 compliance (id correlation, error/result handling)
[x] Configurable timeout via AbortController
[x] Proper headers (Content-Type, Accept)
[x] Structured error handling (network, HTTP, JSON-RPC, malformed)
[x] Request/response validation and logging (with payload sanitization)
[x] Timing metrics and correlation IDs for observability
[x] Brief retry logic for transient failures
[x] Ensure all tool calls are non-blocking and support graceful degradation if MCP is unavailable.
[x] Test: All error and edge cases are covered, including integration with a real MCP server.
[x] Success Criteria: Transport is robust, low-latency, observable, and testable.
Step 8.2: Observability & Debugging
[x] Add structured logging for all MCP requests/responses (INFO/DEBUG/ERROR as appropriate).
[x] Track timing metrics for each request.
[x] Support request tracing/correlation IDs.
[x] Test: Logs and metrics are emitted as expected in all scenarios.
Step 8.3: Error Handling & Graceful Degradation
[x] Implement custom error types for network, HTTP, JSON-RPC, and malformed responses.
[x] Ensure the voice pipeline degrades gracefully if MCP is unavailable (no hangs, fallback logic).
[x] Test: Simulate MCP downtime and verify graceful fallback.
Step 8.4: SDK Evaluation & Migration (Optional)
[ ] Evaluate the official MCP SDK for all above requirements (robust transport, error handling, observability, testability, fetch-based).
[ ] If the SDK meets requirements, plan and execute migration.
[ ] If not, document rationale for custom implementation and re-evaluate periodically.

## Testing Strategy

### TDD Workflow
- For every new feature or bugfix, write a failing test before implementation.
- Add at least one negative/edge case test for each integration point.
- Organize all new test files in `__tests__` folders alongside implementation files.
- Use mocking libraries (e.g., `nock`, `msw`) for MCP server and event emitter/state machine integration.
- Use descriptive test names and `describe`/`it` blocks.
- Refactor only with all tests passing (green-to-green refactoring).
- Add regression tests for any discovered bugs.
- Ensure all tests are run in CI for every commit/merge.

### Unit Tests
- Mock MCP server responses for predictable testing
- Test error conditions and edge cases
- Verify caching behavior and performance optimizations

### Integration Tests
- Use test MCP server or mock server for integration testing
- Test complete session flow with customer identification (success, failure, fallback)
- Verify bot service receives customer context correctly

### Manual Testing
- Start MCP server locally: `cd ~/Dev/aidcc-ccczautomationmcpserver && npm start`
- Verify MCP server running on `http://127.0.0.1:6274`
- Use MCP Inspector to verify tool discovery and execution
- Test various ANI scenarios and customer data formats
- Verify customer lookup tools are available (beyond GetDateTime shown in screenshot)

## Success Criteria

### Functional Requirements
- [ ] Customer identification via ANI during session initialization
- [ ] Graceful fallback when MCP server unavailable
- [ ] Customer context provided to bot service for personalization
- [ ] Caching to improve performance and reduce server load

### Non-Functional Requirements
- [ ] Follows existing service architecture patterns
- [ ] Comprehensive error handling and logging
- [ ] Configurable via environment variables
- [ ] >90% test coverage
- [ ] Documentation collocated with code

### Integration Requirements
- [ ] Seamless integration with existing state machine
- [ ] No disruption to existing session flow
- [ ] Compatible with current logging and metrics systems
- [ ] Maintains application performance standards

## Risk Mitigation

### MCP Server Availability
- **Risk**: MCP server downtime affects customer identification
- **Mitigation**: Graceful fallback, caching, retry logic

### Performance Impact
- **Risk**: Customer lookup adds latency to session initialization
- **Mitigation**: Async lookup, caching, timeout configuration

### Data Privacy
- **Risk**: Customer information in logs or errors
- **Mitigation**: Sanitized logging, secure error handling

## Rollout Strategy
1. **Development**: Implement with local MCP server, following TDD for every step
2. **Testing**: Comprehensive testing with mock and real servers, all tests must pass in CI
3. **Staging**: Deploy to staging environment with production MCP server
4. **Production**: Gradual rollout with monitoring and fallback capabilities
