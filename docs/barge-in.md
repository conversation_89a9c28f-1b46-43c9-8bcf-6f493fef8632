# Barge-in Flow: Architecture, Implementation & Debugging

## Overview

Barge-in allows a user to interrupt system output (audio playback or TTS response) and immediately provide new input. The state machine is designed to process barge-in events only in states where interruption is meaningful and safe.

---

## State Machine Flow

### When and How Barge-In is Processed

- **Allowed states for barge-in:**
  - `PLAYING` (audio playback)
  - `RESPONDING` (TTS/bot response)
- **On barge-in:**
  1. Transition to `IDLE` (for cleanup)
  2. Immediately transition to `LISTENING` (ASR boundary)
  3. Inject the barge-in utterance as user input and transition to `PROCESSING_INPUT`
- **All user input, including barge-in, must go through `LISTENING` before being processed.**

### Why Not in Other States?

- In `PROCESSING_INPUT` or `PROCESSING_BOT`, the system is already handling user input or bot processing. Interrupting here would:
  - Discard in-flight user turns or bot responses
  - Cause race conditions or lost context
- In `IDLE`, the system is already waiting for input; barge-in is redundant.

### State Table

| State            | Barge-in Handling                                 |
|------------------|---------------------------------------------------|
| PLAYING          | Interrupt, transition to LISTENING, process input |
| RESPONDING       | Interrupt, transition to LISTENING, process input |
| LISTENING        | Already listening; input is processed             |
| PROCESSING_INPUT | Ignore/log; let current user turn complete        |
| PROCESSING_BOT   | Ignore/log; let current user turn complete        |
| IDLE             | Ignore/log (already waiting for input)            |
| DISCONNECTING    | Ignore/log (session is ending)                    |

---

## Implementation Details

- **IDLE Entry Action:**
  - Checks and clears the pending barge-in flag.
  - Schedules the transition to `LISTENING` asynchronously to avoid mutex deadlocks.
- **LISTENING Entry Action:**
  - If a barge-in utterance is present, schedules the transition to `PROCESSING_INPUT` asynchronously.
- **Redundant Transitions:**
  - The state machine guards against redundant transitions to `LISTENING` (e.g., from `playback_completed`) if a barge-in is pending.
- **All transitions are serialized via a mutex.**

---

## Debugging Tips

- **Key logs to check:**
  - State transitions: `PLAYING → IDLE → LISTENING → PROCESSING_INPUT`
  - Entry actions for `IDLE` and `LISTENING`
  - Barge-in detection and flag clearing
- **Common issues:**
  - If the barge-in utterance is not processed, check for races between barge-in and playback completion events.
  - Ensure the transition to `PROCESSING_INPUT` is scheduled asynchronously from the `LISTENING` entry action.
  - If a transition is dropped, check the transition validation rules and metadata requirements.

---

## Metrics

- On barge-in, TTS and LLM metrics phases are ended early if playback or bot response is interrupted.
- On entry to `LISTENING`, the `speechToText` phase is started.

---

## Summary

- The barge-in flow is robust, race-free, and maintainable.
- All barge-in state is passed via transition metadata.
- Logging is minimal and focused on state transitions and errors.
- For debugging, focus on the state transition logs and entry actions for `IDLE` and `LISTENING`.
