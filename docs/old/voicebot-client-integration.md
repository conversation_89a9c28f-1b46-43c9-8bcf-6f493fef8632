# Voicebot Client Integration Guide

## Overview

This document provides technical specifications for implementing a React-based voicebot client that integrates with the websocket server.

## Protocol Implementation

### Required Types

Create these type definitions in your client project:

```typescript
// Core Types
type Uuid = string;
type SequenceNumber = number;
type Duration = number;
type LanguageCode = string;
type JsonObject = Record<string, any>;
type JsonStringMap = Record<string, string>;
type EmptyObject = Record<string, never>;

// Media Parameters
interface MediaParameters {
  audio: {
    format: string;
    channels: number;
    samplingRate: number;
  };
}

// Message Base Types
interface MessageBase<Type extends string = string, Parameters extends JsonObject = JsonObject> {
  version: '2';
  id: Uuid;
  type: Type;
  seq: SequenceNumber;
  parameters: Parameters;
}

interface ClientMessageBase<T extends string = string, P extends JsonObject = JsonObject>
  extends MessageBase<T, P> {
  serverseq: SequenceNumber;
  position: Duration;
}

interface ServerMessageBase<T extends string = string, P extends JsonObject = JsonObject>
  extends MessageBase<T, P> {
  clientseq: SequenceNumber;
}

// Participant Info
interface Participant {
  id: Uuid;
  ani: string;
  aniName: string;
  dnis: string;
}

// Message Parameters
interface OpenParameters {
  organizationId: Uuid;
  conversationId: Uuid;
  participant: Participant;
  media: MediaParameters;
  language?: LanguageCode;
  customConfig?: JsonObject;
  inputVariables?: JsonStringMap;
}

interface OpenedParameters {
  media: MediaParameters;
  discardTo?: Duration;
  startPaused?: boolean;
}

interface ErrorParameters {
  code: number;
  message: string;
  retryAfter?: Duration;
}

// Message Types
type OpenMessage = ClientMessageBase<'open', OpenParameters>;
type OpenedMessage = ServerMessageBase<'opened', OpenedParameters>;
type ErrorMessage = ClientMessageBase<'error', ErrorParameters>;
```

## Protocol Details

### Message Flow

```sequence
Client->Server: WebSocket Connection Request
Server-->Client: Connection Accepted
Client->Server: Open Message
Server-->Client: Opened Message
```

1. **Initial Connection**

   - Client establishes WebSocket connection
   - Server authenticates the connection
   - Client sends 'open' message with configuration
   - Server responds with 'opened' message

2. **Audio Communication**

   - Binary messages contain raw audio data (16-bit Linear PCM)
   - Text messages for control and events
   - Each audio chunk should be ~20ms in duration
   - Position tracking in milliseconds

3. **Connection States**
   ```typescript
   type ConnectionState =
     | 'connecting' // Initial connection attempt
     | 'authenticating' // Waiting for server authentication
     | 'configuring' // Sending initial configuration
     | 'connected' // Ready for audio streaming
     | 'reconnecting' // Attempting to restore connection
     | 'disconnected'; // Connection lost or closed
   ```

### Error Recovery

```typescript
class ConnectionManager {
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private backoffMs = 1000; // Start with 1 second

  async reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      throw new Error('Max reconnection attempts reached');
    }

    // Exponential backoff
    const delay = this.backoffMs * Math.pow(2, this.reconnectAttempts);
    await new Promise(resolve => setTimeout(resolve, delay));

    this.reconnectAttempts++;
    return this.connect();
  }

  private async connect() {
    try {
      // Connection logic
      this.reconnectAttempts = 0; // Reset on successful connection
      this.backoffMs = 1000; // Reset backoff
    } catch (error) {
      return this.reconnect();
    }
  }
}
```

### Binary Audio Format

```typescript
// Audio configuration
const AUDIO_CONFIG = {
  sampleRate: 16000,
  channels: 1,
  bitsPerSample: 16,
  chunkDurationMs: 20,
};

// Calculate samples per chunk
const samplesPerChunk = Math.floor((AUDIO_CONFIG.sampleRate * AUDIO_CONFIG.chunkDurationMs) / 1000);

// Convert audio samples
function convertToL16(float32Array: Float32Array): ArrayBuffer {
  const int16Array = new Int16Array(float32Array.length);
  for (let i = 0; i < float32Array.length; i++) {
    // Convert Float32 [-1,1] to Int16 [-32768,32767]
    const sample = Math.max(-1, Math.min(1, float32Array[i]));
    int16Array[i] = sample < 0 ? sample * 0x8000 : sample * 0x7fff;
  }
  return int16Array.buffer;
}
```

## Project Setup

1. Create new React project with TypeScript:

```bash
npx create-react-app voicebot-client --template typescript
cd voicebot-client
```

2. Install required dependencies:

```bash
npm install websocket @reduxjs/toolkit react-redux styled-components
```

3. Create project structure:

```
src/
  ├── components/
  │   └── VoiceBot/
  │       ├── index.tsx          # Main component
  │       ├── AudioHandler.ts    # Audio processing
  │       └── MessageHandler.ts  # WebSocket message handling
  ├── services/
  │   ├── websocket.ts          # WebSocket client
  │   └── audio.ts              # Audio services
  ├── types/
  │   ├── messages.ts           # Protocol types
  │   └── state.ts              # State types
  ├── hooks/
  │   ├── useWebSocket.ts       # WebSocket hook
  │   └── useAudio.ts          # Audio hook
  └── store/
      └── voicebot.ts           # State management
```

## WebSocket Client Implementation

```typescript
// src/services/websocket.ts
export class WebSocketClient {
  private ws: WebSocket | null = null;
  private seq = 0;
  private serverSeq = 0;
  private position = 0;
  private connectionState: ConnectionState = 'disconnected';
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private backoffMs = 1000;
  private pingInterval: NodeJS.Timeout | null = null;
  private lastPongTime = Date.now();

  constructor(
    private config: {
      url: string;
      organizationId: string;
      onMessage: (msg: any) => void;
      onError: (error: Error) => void;
      onStateChange: (state: ConnectionState) => void;
    }
  ) {}

  private updateState(newState: ConnectionState) {
    this.connectionState = newState;
    this.config.onStateChange(newState);
  }

  private setupPingPong() {
    // Send ping every 15 seconds
    this.pingInterval = setInterval(() => {
      if (this.connectionState === 'connected') {
        this.send({
          version: '2',
          id: crypto.randomUUID(),
          type: 'ping',
          seq: this.getNextSeq(),
          serverseq: this.serverSeq,
          position: this.position,
          parameters: { rtt: 0 },
        });

        // Check if we missed too many pongs
        if (Date.now() - this.lastPongTime > 30000) {
          console.warn('No pong received for 30s, reconnecting...');
          this.reconnect();
        }
      }
    }, 15000);
  }

  private cleanupPingPong() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }

  private async reconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.updateState('disconnected');
      this.config.onError(new Error('Max reconnection attempts reached'));
      return;
    }

    this.updateState('reconnecting');
    this.cleanupPingPong();

    // Exponential backoff
    const delay = this.backoffMs * Math.pow(2, this.reconnectAttempts);
    await new Promise(resolve => setTimeout(resolve, delay));

    this.reconnectAttempts++;
    this.connect();
  }

  connect() {
    try {
      this.updateState('connecting');
      this.ws = new WebSocket(this.config.url);

      this.ws.onopen = () => {
        this.updateState('authenticating');
        this.sendOpen();
      };

      this.ws.onmessage = event => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('Failed to parse message:', error);
          this.config.onError(new Error('Invalid message format'));
        }
      };

      this.ws.onerror = error => {
        console.error('WebSocket error:', error);
        this.config.onError(error);
        this.reconnect();
      };

      this.ws.onclose = event => {
        console.log(`WebSocket closed: ${event.code} ${event.reason}`);
        if (this.connectionState !== 'disconnected') {
          this.reconnect();
        }
      };

      // Reset reconnection counter on successful connection
      this.reconnectAttempts = 0;
      this.backoffMs = 1000;
      this.setupPingPong();
    } catch (error) {
      console.error('Connection error:', error);
      this.reconnect();
    }
  }

  private sendOpen() {
    const openMessage: OpenMessage = {
      version: '2',
      id: crypto.randomUUID(),
      type: 'open',
      seq: this.getNextSeq(),
      serverseq: this.serverSeq,
      position: this.position,
      parameters: {
        organizationId: this.config.organizationId,
        conversationId: crypto.randomUUID(),
        participant: {
          id: crypto.randomUUID(),
          ani: 'web-client',
          aniName: 'Web Client',
          dnis: 'web',
        },
        media: {
          audio: {
            format: 'audio/l16',
            channels: 1,
            samplingRate: 16000,
          },
        },
      },
    };

    this.send(openMessage);
  }

  private handleMessage(message: any) {
    // Update sequence number if present
    if (message.seq) {
      this.serverSeq = message.seq;
    }

    // Handle different message types
    switch (message.type) {
      case 'opened':
        this.updateState('connected');
        this.setupPingPong();
        break;

      case 'pong':
        this.lastPongTime = Date.now();
        break;

      case 'disconnect':
        const reason = message.parameters?.reason;
        console.log(`Disconnected by server: ${reason}`);
        if (reason === 'error') {
          this.updateState('disconnected');
          this.cleanupPingPong();
        } else if (reason === 'reconnect') {
          this.reconnect();
        }
        break;

      case 'error':
        console.error('Server error:', message.parameters);
        if (message.parameters.code === 429) {
          // Rate limit
          const retryAfter = message.parameters.retryAfter || 5000;
          setTimeout(() => this.reconnect(), retryAfter);
        }
        break;

      case 'event':
        // Handle bot events (speech, intents, etc.)
        if (message.parameters?.entities) {
          for (const entity of message.parameters.entities) {
            switch (entity.type) {
              case 'bot_turn_response':
                if (entity.data.disposition === 'match') {
                  // Handle successful bot response
                  console.log('Bot response:', entity.data.text);
                } else if (entity.data.disposition === 'no_match') {
                  console.log("Bot couldn't understand the input");
                }
                break;
              case 'barge_in':
                // Handle barge-in event
                console.log('User barge-in detected');
                break;
            }
          }
        }
        break;
    }

    // Notify message listeners
    this.config.onMessage(message);
  }

  private getNextSeq(): number {
    return ++this.seq;
  }

  send(message: any) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    }
  }

  sendBinary(data: ArrayBuffer) {
    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(data);
    }
  }

  close() {
    this.ws?.close();
  }
}
```

## Audio Implementation

```typescript
// src/services/audio.ts
export class AudioHandler {
  private stream: MediaStream | null = null;
  private audioContext: AudioContext | null = null;
  private processor: ScriptProcessorNode | null = null;

  async initialize() {
    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          channelCount: 1,
          sampleRate: 16000,
        },
      });

      this.audioContext = new AudioContext({
        sampleRate: 16000,
      });

      const source = this.audioContext.createMediaStreamSource(this.stream);
      this.processor = this.audioContext.createScriptProcessor(1024, 1, 1);

      source.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      return true;
    } catch (error) {
      console.error('Audio initialization failed:', error);
      return false;
    }
  }

  onAudioData(callback: (data: Float32Array) => void) {
    if (this.processor) {
      this.processor.onaudioprocess = e => {
        const data = e.inputBuffer.getChannelData(0);
        callback(data);
      };
    }
  }

  close() {
    this.processor?.disconnect();
    this.audioContext?.close();
    this.stream?.getTracks().forEach(track => track.stop());
  }
}
```

## Main Component Implementation

```typescript
// src/components/VoiceBot/index.tsx
import React, { useEffect, useState } from 'react';
import { WebSocketClient } from '../../services/websocket';
import { AudioHandler } from '../../services/audio';

interface VoiceBotProps {
  organizationId: string;
  serverUrl: string;
}

export const VoiceBot: React.FC<VoiceBotProps> = ({ organizationId, serverUrl }) => {
  const [status, setStatus] = useState<ConnectionState>('disconnected');
  const [wsClient, setWsClient] = useState<WebSocketClient | null>(null);
  const [audioHandler, setAudioHandler] = useState<AudioHandler | null>(null);
  const [audioEnabled, setAudioEnabled] = useState(false);
  const [botMessage, setBotMessage] = useState<string>('');
  const [isSpeaking, setIsSpeaking] = useState(false);

  useEffect(() => {
    const client = new WebSocketClient({
      url: serverUrl,
      organizationId,
      onMessage: handleMessage,
      onError: handleError,
      onStateChange: state => {
        setStatus(state);
        if (state === 'connected') {
          setAudioEnabled(true);
        } else if (state === 'disconnected') {
          setAudioEnabled(false);
          setBotMessage('');
          setIsSpeaking(false);
        }
      },
    });

    const audio = new AudioHandler();

    const initialize = async () => {
      setStatus('connecting');
      const audioInitialized = await audio.initialize();

      if (audioInitialized) {
        audio.onAudioData(data => {
          // Convert Float32Array to Int16Array for L16 format
          const intData = new Int16Array(data.length);
          for (let i = 0; i < data.length; i++) {
            intData[i] = data[i] * 32767;
          }
          client.sendBinary(intData.buffer);
        });

        client.connect();
        setStatus('connected');
      }
    };

    setWsClient(client);
    setAudioHandler(audio);
    initialize();

    return () => {
      client.close();
      audio.close();
    };
  }, [organizationId, serverUrl]);

  const handleMessage = (message: any) => {
    switch (message.type) {
      case 'event':
        if (message.parameters?.entities) {
          for (const entity of message.parameters.entities) {
            if (entity.type === 'bot_turn_response') {
              if (entity.data.disposition === 'match') {
                setBotMessage(entity.data.text || '');
                setIsSpeaking(true);
              } else if (entity.data.disposition === 'no_match') {
                setBotMessage("I didn't understand that. Could you please try again?");
              }
            } else if (entity.type === 'barge_in') {
              setIsSpeaking(false); // Stop playback when user interrupts
            }
          }
        }
        break;
    }
  };

  const handleError = (error: Error) => {
    console.error('WebSocket error:', error);
    setBotMessage('Connection error occurred. Please try again.');
  };

  const toggleMicrophone = async () => {
    if (!audioEnabled) {
      const initialized = await audioHandler?.initialize();
      if (initialized) {
        setAudioEnabled(true);
      } else {
        setBotMessage('Could not access microphone. Please check permissions.');
      }
    } else {
      audioHandler?.close();
      setAudioEnabled(false);
    }
  };

  return (
    <div className="voicebot-container">
      <div className={`status-indicator ${status}`}>Status: {status}</div>

      {botMessage && (
        <div className="bot-message">
          <p>{botMessage}</p>
          {isSpeaking && <div className="speaking-indicator" />}
        </div>
      )}

      <div className="controls">
        <button
          onClick={toggleMicrophone}
          disabled={status !== 'connected'}
          className={audioEnabled ? 'active' : ''}
        >
          {audioEnabled ? 'Stop' : 'Start'} Microphone
        </button>
      </div>

      {status === 'connected' && (
        <div className="audio-status">{audioEnabled ? 'Listening...' : 'Click Start to begin'}</div>
      )}
    </div>
  );
};
```

## Component Styling

Add the following CSS to style your VoiceBot component:

```css
/* src/components/VoiceBot/styles.css */
.voicebot-container {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
  background: #f8f9fa;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
}

.status-indicator.connecting,
.status-indicator.authenticating,
.status-indicator.configuring {
  background: #fff3cd;
  color: #856404;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.status-indicator.reconnecting {
  background: #cce5ff;
  color: #004085;
}

.bot-message {
  background: white;
  padding: 16px;
  border-radius: 8px;
  margin: 16px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.speaking-indicator {
  display: inline-block;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  margin-left: 8px;
  animation: pulse 1.5s infinite;
}

.controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.controls button {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 16px;
}

.controls button:disabled {
  background: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.controls button.active {
  background: #dc3545;
}

.controls button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
}

.audio-status {
  font-size: 14px;
  color: #666;
  text-align: center;
  margin-top: 10px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .voicebot-container {
    background: #343a40;
    color: #f8f9fa;
  }

  .bot-message {
    background: #495057;
    color: #f8f9fa;
  }

  .audio-status {
    color: #adb5bd;
  }
}
```

Import the styles in your VoiceBot component:

```typescript
import './styles.css';
```

## Usage Example

```typescript
// App.tsx
import { VoiceBot } from './components/VoiceBot';

function App() {
  return (
    <div>
      <h1>Voicebot Demo</h1>
      <VoiceBot organizationId="your-org-id" serverUrl="wss://your-server-url/websocket" />
    </div>
  );
}
```

## Security Considerations

1. **Authentication**

   - Implement signature verification
   - Use HTTPS for all communication
   - Store sensitive data securely

2. **Audio Security**

   - Request microphone permissions explicitly
   - Provide clear user feedback about audio recording
   - Implement proper cleanup of audio resources

3. **Error Handling**
   - Implement reconnection logic
   - Handle audio device failures gracefully
   - Validate all incoming messages

## Testing

1. **Unit Tests**

   - Test message formatting
   - Test audio processing
   - Test state management

2. **Integration Tests**

   - Test WebSocket connection
   - Test audio capture/playback
   - Test full conversation flow

3. **E2E Tests**
   - Test complete user scenarios
   - Test error scenarios
   - Test performance under load

## Development Tips

1. Use Chrome DevTools for WebSocket debugging
2. Monitor audio levels and quality
3. Implement logging for easier debugging
4. Use TypeScript strictly for better type safety
5. Test on different browsers and devices

## Common Issues and Solutions

1. **Audio Issues**

   - Ensure proper sample rate conversion
   - Handle audio permission denials
   - Monitor audio buffer sizes

2. **WebSocket Issues**

   - Implement heartbeat mechanism
   - Handle reconnection properly
   - Monitor connection state

3. **Browser Compatibility**
   - Test on major browsers
   - Implement fallbacks where needed
   - Handle vendor prefixes

## Performance Optimization

1. **Audio Processing**

   - Optimize buffer sizes
   - Use Web Workers for heavy processing
   - Implement proper cleanup

2. **Memory Management**

   - Clear unused resources
   - Monitor memory usage
   - Implement proper disposal

3. **Network Optimization**
   - Compress audio data when possible
   - Monitor bandwidth usage
   - Implement throttling if needed
