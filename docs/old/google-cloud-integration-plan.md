# Google Cloud Speech Integration Plan

## 1. Service Architecture

### 1.1 Speech Service Factory

Create an abstract factory pattern to instantiate speech services:

```typescript
interface SpeechServiceFactory {
  createASRService(): BaseSpeechService;
  createTTSService(): BaseTTSService;
}

class AzureSpeechFactory implements SpeechServiceFactory {
  createASRService(): BaseSpeechService {
    return new AzureSpeechService();
  }
  createTTSService(): BaseTTSService {
    return new AzureTTSService();
  }
}

class GoogleSpeechFactory implements SpeechServiceFactory {
  createASRService(): BaseSpeechService {
    return new GoogleSpeechService();
  }
  createTTSService(): BaseTTSService {
    return new GoogleTTSService();
  }
}
```

### 1.2 Base TTS Service

Extract common TTS functionality into a base class:

```typescript
abstract class BaseTTSService {
  protected abstract initializeService(): void;
  abstract getAudioBytes(text: string): Promise<Uint8Array>;
  abstract dispose(): void;

  // Common functionality like audio caching can be shared here
}
```

## 2. Google Cloud Implementation

### 2.1 Required Dependencies

```json
{
  "@google-cloud/speech": "^5.x",
  "@google-cloud/text-to-speech": "^4.x"
}
```

### 2.2 Environment Variables

```
SPEECH_SERVICE=google|azure
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
GOOGLE_PROJECT_ID=your-project-id
```

### 2.3 Configuration Management

Create a new configuration service:

```typescript
interface SpeechConfig {
  provider: 'google' | 'azure';
  credentials: any;
  region?: string;
  // Common config options
}

class ConfigurationService {
  getSpeechConfig(): SpeechConfig;
  getASRConfig(): ASRConfig;
  getTTSConfig(): TTSConfig;
}
```

## 3. Implementation Steps

1. Create Base Interfaces

   - Extract interfaces from existing Azure implementations
   - Define common configuration interfaces
   - Create factory interfaces

2. Implement Google Services

   - GoogleSpeechService for ASR
   - GoogleTTSService for TTS
   - Handle audio format conversion (8kHz µ-law)

3. Configuration Management

   - Update environment-variables.ts
   - Add configuration validation
   - Implement factory selection logic

4. Testing Strategy
   - Unit tests for Google implementations
   - Integration tests with Google Cloud
   - Factory selection tests
   - Configuration validation tests

## 4. File Structure

```
src/
  services/
    speech/
      factories/
        azure-speech-factory.ts
        google-speech-factory.ts
        speech-factory.ts
      base/
        base-speech-service.ts
        base-tts-service.ts
      azure/
        azure-speech-service.ts
        azure-tts-service.ts
      google/
        google-speech-service.ts
        google-tts-service.ts
    config/
      configuration-service.ts
      speech-config.ts
```

## 5. Migration Strategy

1. Extract Base Classes

   - Move common functionality to base classes
   - Update Azure implementations to use new base classes

2. Implement Google Services

   - Create Google implementations
   - Add tests for Google services

3. Update Factory Integration

   - Implement factory selection
   - Update service initialization

4. Testing & Validation
   - End-to-end testing with both providers
   - Performance comparison
   - Error handling verification

## 6. Dependency Injection Setup

```typescript
class ServiceContainer {
  private static instance: ServiceContainer;
  private speechFactory: SpeechServiceFactory;

  initialize(config: SpeechConfig) {
    this.speechFactory =
      config.provider === 'google'
        ? new GoogleSpeechFactory(config)
        : new AzureSpeechFactory(config);
  }

  static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  getSpeechFactory(): SpeechServiceFactory {
    return this.speechFactory;
  }
}
```

## 7. Success Criteria

1. Seamless provider switching via environment variables
2. No direct provider dependencies in business logic
3. Maintained or improved performance
4. Complete test coverage for both implementations
5. Clear error handling and logging
6. Documentation for both provider setups
