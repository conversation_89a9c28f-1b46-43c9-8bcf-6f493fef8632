# ElevenLabs TTS Integration Plan

## Overview

This document outlines the implementation plan for integrating ElevenLabs Text-to-Speech (TTS) service while maintaining existing Google STT functionality.

## Architecture Changes

The integration will follow the existing architecture patterns, adding Eleven<PERSON>abs as a new TTS provider alongside Google and Azure:

```mermaid
classDiagram
    BaseTTSService <|-- GoogleTTSService
    BaseTTSService <|-- AzureTTSService
    BaseTTSService <|-- ElevenLabsTTSService
    TTSProvider -- SpeechFactory

    class BaseTTSService {
        <<abstract>>
        +getAudioBytes(text: string)
        #initializeService()
        #synthesizeAudio(text: string)
        #dispose()
    }

    class TTSProvider {
        <<enumeration>>
        GOOGLE
        AZURE
        ELEVENLABS
    }

    class SpeechFactory {
        +createTTSService(provider: TTSProvider)
    }
```

## Implementation Steps

### 1. Provider Enum Creation

Add new TTS provider enum in `src/services/speech/base/types.ts`:

```typescript
export enum TTSProvider {
  GOOGLE = 'google',
  AZURE = 'azure',
  ELEVENLABS = 'elevenlabs',
}
```

### 2. Environment Configuration

Add new variables to `.env`:

```bash
# ElevenLabs Configuration
ELEVENLABS_API_KEY=your_api_key_here
ELEVENLABS_VOICE_ID=voice_id_here
TTS_PROVIDER=elevenlabs  # Options: google, azure, elevenlabs
HTTP_PROXY=http://proxy-server:port  # Optional: for proxy support
```

### 3. ElevenLabs Service Implementation

The service uses direct HTTP calls to the ElevenLabs API via Axios instead of the SDK:

```typescript
class ElevenLabsTTSService extends BaseTTSService {
  private readonly baseURL = 'https://api.elevenlabs.io/v1';

  protected async initializeService(): Promise<void>;
  protected async synthesizeAudio(text: string): Promise<Uint8Array>;
  dispose(): void;
}
```

Key Features:

- Direct API integration using Axios
- HTTP/HTTPS proxy support
- Automatic audio format handling
- Enhanced error reporting
- Proper initialization verification

### 4. Factory Updates

Update `src/services/speech/factories/speech-factory.ts`:

- Add ElevenLabs to provider options
- Update factory logic for ElevenLabs service creation

## Technical Specifications

### API Integration

The service communicates directly with ElevenLabs API endpoints:

- GET `/voices` - For service initialization and voice verification
- POST `/text-to-speech/{voice_id}/stream` - For audio synthesis

### Voice Settings

Default voice settings for optimal output:

```typescript
{
  similarity_boost: 0.75,
  stability: 0.5,
  style: 0.0,
  use_speaker_boost: true
}
```

### Audio Format Handling

- Uses ElevenLabs multilingual model (eleven_multilingual_v2)
- Streams audio directly from the API
- Converts to Uint8Array for internal processing
- Supports audio caching for performance

### Error Handling

Comprehensive error handling for:

- API rate limiting
- Invalid API key errors
- Network connectivity issues
- Voice ID validation
- Malformed request errors
- Proxy configuration issues

## Configuration Guide

### Setting up ElevenLabs

1. Create an ElevenLabs account at https://elevenlabs.io
2. Obtain your API key from the ElevenLabs dashboard
3. Select or create a voice and note its ID
4. Configure environment variables:
   ```bash
   ELEVENLABS_API_KEY=your_api_key_here
   ELEVENLABS_VOICE_ID=your_voice_id_here
   TTS_PROVIDER=elevenlabs
   HTTP_PROXY=http://proxy-server:port  # Optional
   ```

### Environment Variables

- `ELEVENLABS_API_KEY`: Your ElevenLabs API key
- `ELEVENLABS_VOICE_ID`: ID of the voice to use for synthesis
- `TTS_PROVIDER`: Set to 'elevenlabs' to use ElevenLabs TTS
- `HTTP_PROXY`: (Optional) HTTP/HTTPS proxy server configuration

### Proxy Support

When HTTP_PROXY is configured:

- Automatically uses HttpsProxyAgent for all API calls
- Maintains secure communication through the proxy
- Handles proxy authentication if provided in the URL
