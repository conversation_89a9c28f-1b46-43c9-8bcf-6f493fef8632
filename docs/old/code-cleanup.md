# Code Cleanup Plan: Removing Chat Functionality

This document outlines the plan to remove chat-specific functionality and the MCP server implementation from the codebase while preserving the core STT-LLM-TTS pipeline.

## Overview

The current codebase supports both voice interactions (STT-LLM-TTS) and chat functionality, as well as an MCP server implementation. We will remove the chat-specific components and the MCP server implementation while ensuring that:

1. The core voice functionality remains intact
2. The codebase is cleaner and more focused on its primary purpose
3. The MCP server functionality will be implemented as a standalone server in a different repository in the future

## Files to Remove

### Chat-Related Files

The following files are exclusively related to chat functionality and can be safely removed:

- [x] `src/websocket/routes/chat.ts` - Chat API endpoints
- [x] `src/websocket/routes/history.ts` - Chat history retrieval
- [x] `src/websocket/middleware/validation.ts` - Chat-specific validation
- [x] `docs/chatbot-implementation-plan.md` - Chat documentation
- [x] `docs/spec/chatbot-interface.yaml` - Chat API specification
- [x] `docs/react-spa-integration-plan.md` - React SPA for chat UI

### MCP-Related Files

The following files are related to the MCP server implementation and can be safely removed:

- [x] `src/mcp/` directory - All MCP-related files
- [x] `src/types/mcp.ts` - MCP type definitions

## Code Modifications

### 1. Bot Service (`src/services/bot-service.ts`)

#### a. Remove the `ischat` Parameter

The `getBotResponse` method currently has an `ischat` parameter that determines whether to return audio bytes. This should be simplified:

```typescript
// Current implementation
async getBotResponse(
  data: string,
  ischat: boolean = false
): Promise<BotResponse> {
  try {
    // ... existing code ...

    if (ischat) {
      return new BotResponse("match", response)
        .withConfidence(1.0)
        .withEndSession(shouldEndSession, escalateReason);
    } else {
      const textToTts = this.cleanBotResponse(response);
      const audioBytes = await this.ttsService.getAudioBytes(textToTts);
      return new BotResponse("match", textToTts)
        .withConfidence(1.0)
        .withAudioBytes(audioBytes)
        .withEndSession(shouldEndSession, escalateReason);
    }
  } catch (error) {
    console.error("Error in getBotResponse:", error);
    return this.getErrorResponse();
  }
}

// Modified implementation
async getBotResponse(data: string): Promise<BotResponse> {
  try {
    // ... existing code ...

    const textToTts = this.cleanBotResponse(response);
    const audioBytes = await this.ttsService.getAudioBytes(textToTts);
    return new BotResponse("match", textToTts)
      .withConfidence(1.0)
      .withAudioBytes(audioBytes)
      .withEndSession(shouldEndSession, escalateReason);
  } catch (error) {
    console.error("Error in getBotResponse:", error);
    return this.getErrorResponse();
  }
}
```

#### b. Remove MCP-Related Code

Remove MCP-related code from the bot service:

```typescript
// Remove this import
import { tools } from "../mcp/customer-context-server";

// Remove the handleToolCall method and any related code
private async handleToolCall(toolCall: {...}): Promise<string> {...}

// Remove tool calls from getOpenAIResponse method
const requestPayload = {
  messages,
  // Remove these lines
  tools,
  tool_choice: "auto",
  ...
};
```

### 2. WebSocket Server (`src/websocket/server.ts`)

#### a. Remove Chat-Related Endpoint Registrations

In the `start()` method of the `Server` class, remove the following lines that register chat-related endpoints:

```typescript
// Remove these lines
this.app.use('/askChatbot', chatRouter(this.botService));
this.app.use('/getHistory', historyRouter(this.botService));
```

#### b. Remove Related Imports

At the top of the file, remove the imports for the chat-related routers:

```typescript
// Remove these imports
import { chatRouter } from './routes/chat';
import { historyRouter } from './routes/history';
```

#### c. Update Chat-Related Type Imports

Keep the `ChatMessage` import as it's still used for conversation summaries, but fix any TypeScript errors:

```typescript
// Import ChatMessage for type definitions
import { ChatMessage } from '../types/chat';

// Fix TypeScript errors in the code
const summaryPrompt: ChatMessage[] = [
  // ...
];
```

#### d. Remove MCP Server Initialization

Remove the MCP server initialization and mounting code:

```typescript
// Remove these lines
private mcpServer: CustomerContextMcpServer;

// In constructor
this.mcpServer = new CustomerContextMcpServer();

// In start() method
console.log("Initializing MCP server...");
await this.mcpServer.start();
this.app.use("/mcp", this.mcpServer.getRouter());
console.log("MCP server initialized and mounted at /mcp/*");
```

#### e. Update Static File Serving (if needed)

If the `/app` directory contained chat-specific UI files that have been removed, you may need to update or remove the static file serving code:

```typescript
// Review if these are still needed
this.app.use('/app', express.static('app'));

// SPA fallback - serve index.html for all /app/* routes
this.app.get('/app/*', (req, res) => {
  res.sendFile('app/index.html', { root: process.cwd() });
});
```

If the app directory is only used for chat UI and not for voice UI, you might want to remove these lines.

### 3. In-Memory Database Service (`src/services/in-memory-database-service.ts`)

Remove chat-specific methods if they're not used by voice functionality:

- [x] `getChatHistoryNonVoice` method
- [x] Chat-related code in the `storeMessage` method (if any)

## Files to Keep

The following files should be kept as they are used by core functionality:

- [x] `src/types/chat.ts` - Contains `ChatMessage` type used for conversation history and summaries
- [x] `docs/supabase-integration-plan.md` - Contains database schema that may be useful

## Implementation Steps

1. Create a new branch for this refactoring ✅
2. Remove the files listed in the "Files to Remove" section ✅
3. Modify the code as described in the "Code Modifications" section ✅
4. Update package.json to remove MCP-related dependencies and scripts ✅
   - Remove `@types/d3-ease` dependency
   - Remove `@modelcontextprotocol/sdk` dependency
   - Remove `mcp` script
5. Fix TypeScript errors and clean up the code ✅
   - Fix unused variable warnings
   - Update type annotations
   - Ensure proper imports
6. Test the STT-LLM-TTS pipeline to ensure it works correctly ✅
7. Update documentation to reflect the changes ✅
8. Merge the branch once all tests pass

## Testing Plan

After implementing the changes, test the following:

1. Voice interaction flow (STT-LLM-TTS)
   - Audio input processing
   - LLM response generation
   - Text-to-speech conversion
   - WebSocket communication

### Voice Interaction Testing Procedure

1. **Server Startup Test**:

   ```bash
   # Start the server and check logs for successful initialization
   npm start
   # Look for log messages like "HTTP Server successfully started on port 8080"
   ```

2. **WebSocket Connection Test**:

   - Use a WebSocket client to connect to the server
   - Verify that the connection is established successfully

3. **Audio Processing Test**:

   - Send audio data to the server
   - Verify that the audio is processed correctly
   - Check that the LLM generates a response
   - Verify that the response is converted to speech

4. **End-to-End Test**:
   - Simulate a complete voice interaction
   - Verify that the conversation flows correctly
   - Check that the bot responds appropriately to user input

## Future Considerations

Since the MCP server will be implemented as a standalone server in a different repository, consider:

1. Documenting the interface that will be needed for integration with the MCP server
2. Preparing the codebase for future integration with the external MCP server
3. Designing a clean API for communicating with the MCP server when it becomes available
