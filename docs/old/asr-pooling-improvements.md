# ASR Pooling Improvements Plan

## Current Issues

1. Fixed pool size (10 concurrent connections) may exceed Azure Speech Service limits
2. No rate limiting implementation
3. No active monitoring of Azure service limits or errors
4. Lack of backoff strategy when limits are hit

## Proposed Solutions

### 1. Implement Dynamic Pool Sizing

- Start with a smaller default pool size (5 connections)
- Add configuration option for max concurrent connections based on subscription tier
- Implement dynamic scaling based on error rates and Azure service responses

### 2. Add Rate Limiting

- Implement a token bucket algorithm for rate limiting
- Configure rate limits based on Azure subscription tier
- Add monitoring for rate limit errors and implement automatic backoff

### 3. Improve Error Handling

- Add specific handling for Azure Speech Service quota errors
- Implement exponential backoff when service limits are hit
- Add detailed logging of connection pool statistics and error rates

### 4. Configuration Updates

Update AzureSpeechServicePool configuration:

```typescript
interface AzureSpeechServiceConfig {
  maxPoolSize: number; // Default to 5
  queueTimeout: number; // Keep 30000ms default
  rateLimitPerMinute?: number; // Based on subscription
  backoffSettings: {
    initialDelay: number; // e.g., 1000ms
    maxDelay: number; // e.g., 30000ms
    factor: number; // e.g., 2
  };
}
```

### 5. Implementation Steps

1. **Pool Size Management**

```typescript
class AzureSpeechServicePool {
  private dynamicPoolSize: number;
  private errorCount: number = 0;

  // Reduce pool size when hitting errors
  private adjustPoolSize() {
    if (this.errorCount > threshold) {
      this.dynamicPoolSize = Math.max(1, this.dynamicPoolSize - 1);
      this.errorCount = 0;
    }
  }
}
```

2. **Rate Limiting**

```typescript
class RateLimiter {
  private tokens: number;
  private lastRefill: number;

  public tryAcquire(): boolean {
    this.refill();
    if (this.tokens > 0) {
      this.tokens--;
      return true;
    }
    return false;
  }
}
```

3. **Error Handling**

```typescript
private async handleQuotaError() {
  this.errorCount++;
  this.adjustPoolSize();
  await this.backoff();
}
```

## Benefits

1. More efficient resource utilization
2. Reduced likelihood of hitting service limits
3. Better error recovery and resilience
4. Improved monitoring and debugging capabilities

## Next Steps

1. Determine exact Azure Speech Service limits for your subscription tier
2. Implement the dynamic pool sizing with conservative initial limits
3. Add monitoring for connection pool statistics
4. Test system under various load conditions to verify improvements
