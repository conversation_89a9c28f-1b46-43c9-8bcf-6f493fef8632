# GetHistory Implementation Plan

## Overview

Implement a new `/getHistory` endpoint that returns the last 5 conversation summaries for a given ANI (user identifier).

## 1. Database Query

### Requirements

- Fetch last 5 conversation summaries from `conversation_summaries` table
- Order by end_time DESC with LIMIT 5
- Filter by ANI parameter
- Use existing index `idx_summaries_ani` for efficient querying

### SQL Query Structure

```sql
SELECT summary_text, end_time, conversation_id, duration_seconds
FROM conversation_summaries
WHERE ani = $1
ORDER BY end_time DESC
LIMIT 5
```

## 2. API Specification

### Endpoint Details

- Path: `/getHistory`
- Method: GET
- Query Parameter: `ani` (required)

### Response Structure

```typescript
interface HistoryResponse {
  summaries: {
    summary: string; // Conversation summary text
    datetime: string; // ISO 8601 formatted end_time
    conversationId: string; // For reference/tracking
    durationSeconds: number; // Additional context
  }[];
}
```

### Error Responses

- 400 Bad Request: Missing or invalid ANI parameter
- 500 Internal Server Error: Database or system errors

### OpenAPI Specification Update

The following addition will be made to chatbot-interface.yaml:

```yaml
/getHistory:
  get:
    summary: Retrieve conversation history for a user
    description: Returns the last 5 conversation summaries for a given ANI
    operationId: getHistory
    tags:
      - Chatbot
    parameters:
      - name: ani
        in: query
        required: true
        description: Unique identifier for the user
        schema:
          type: string
          example: 'user-123456'
    responses:
      '200':
        description: Successfully retrieved conversation history
        content:
          application/json:
            schema:
              type: object
              properties:
                summaries:
                  type: array
                  items:
                    type: object
                    properties:
                      summary:
                        type: string
                        description: Text summary of the conversation
                      datetime:
                        type: string
                        format: date-time
                        description: When the conversation ended
                      conversationId:
                        type: string
                        description: Unique identifier for the conversation
                      durationSeconds:
                        type: integer
                        description: Duration of conversation in seconds
      '400':
        description: Bad request - missing or invalid parameters
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: 'Missing required parameter: ani'
      '500':
        description: Server error
        content:
          application/json:
            schema:
              type: object
              properties:
                error:
                  type: string
                  example: 'Internal server error'
```

## 3. Implementation Steps

1. Add new method to SupabaseService:

```typescript
async getLastSummaries(ani: string): Promise<ConversationSummary[]> {
  // Implementation here
}
```

2. Update API specification in chatbot-interface.yaml

3. Implement route handler with:

- Parameter validation
- Error handling
- Response formatting

4. Add tests for:

- Valid ANI parameter
- Missing/invalid ANI
- Database error scenarios
- Response format validation

## 4. Testing Plan

1. Unit Tests

- SupabaseService.getLastSummaries()
  - Returns correct number of summaries (max 5)
  - Proper ordering by end_time
  - Correct data transformation
  - Error handling

2. Integration Tests

- API endpoint
  - Valid ANI returns proper response
  - Invalid ANI returns 400
  - Database errors handled properly
  - Response matches schema

3. Load Tests

- Verify index performance under load
- Test concurrent requests

## 5. Deployment Steps

1. Database

- Verify index exists and is being used
- Monitor query performance

2. API Changes

- Deploy OpenAPI spec changes
- Deploy new endpoint implementation
- Update API documentation

## Next Steps

1. Switch to Code mode to implement:

   - OpenAPI specification update
   - SupabaseService enhancement
   - Route handler implementation

2. Create test cases

3. Update documentation
