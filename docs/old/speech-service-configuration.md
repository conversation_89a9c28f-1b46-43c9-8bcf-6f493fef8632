# Speech Service Configuration Guide

This application supports both Azure Speech Services and Google Cloud Speech Services for speech recognition (ASR) and text-to-speech (TTS) functionality.

## Configuring the Speech Service

The speech service is configured through environment variables. Use the `.env` file to set your configuration:

### Azure Speech Configuration

```env
SPEECH_SERVICE=azure
AZURE_SPEECH_KEY=your-subscription-key
AZURE_SPEECH_REGION=your-region
```

### Google Cloud Configuration

```env
SPEECH_SERVICE=google
GOOGLE_PROJECT_ID=your-project-id
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
```

## Getting Credentials

### Azure Speech Services

1. Create an Azure account if you don't have one
2. Create a Speech service resource in the Azure portal
3. Copy the subscription key and region from the resource overview

### Google Cloud Services

1. Create a Google Cloud project
2. Enable the Speech-to-Text and Text-to-Speech APIs
3. Create a service account
4. Download the service account key file (JSON)
5. Set the path to the key file in GOOGLE_APPLICATION_CREDENTIALS

## Switching Services

The application uses dependency injection to handle service selection:

1. Set the SPEECH_SERVICE environment variable to either 'azure' or 'google'
2. Provide the required credentials for your chosen service
3. Restart the application

The system will automatically initialize the appropriate service based on your configuration.

## Additional Configuration

### HTTP Proxy Support

If you need to use a proxy server:

```env
HTTP_PROXY=http://proxy-server:port
```

## Features

Both services support:

- Real-time speech recognition
- High-quality text-to-speech synthesis
- 8kHz µ-law audio format
- Czech language (cs-CZ)

## Troubleshooting

If you encounter issues:

1. Check that all required environment variables are set
2. Verify your credentials are valid
3. Ensure you have the correct permissions in your cloud service
4. Check proxy settings if behind a corporate firewall
