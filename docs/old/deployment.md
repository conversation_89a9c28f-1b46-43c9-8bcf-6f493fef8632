# Local Deployment Guide for Windows

This guide covers local development and deployment for the Genesys Cloud Audio Connector using Windows 11 and Azure CLI.

## Prerequisites

- Windows 11
- Docker Desktop for Windows
- Node.js 20.x
- Azure CLI (`az`) installed via Windows installer
- PowerShell 7+ (comes with Windows 11)
- Azure subscription with required permissions

## Local Development and Testing

### 1. Build and Test Locally

```powershell
# Build and run development container
docker compose up app

# Test production build
docker compose up app-prod
```

### 2. Run Tests

```powershell
docker compose exec app npm test
```

## Deployment Steps

### 1. Azure Container Registry Setup

```powershell
# Login to Azure
az login

# Create resource group
az group create --name genesys-audio-rg --location eastus

# Create container registry
az acr create --resource-group genesys-audio-rg `
    --name genesysaudiocr `
    --sku Standard `
    --admin-enabled true

# Get ACR credentials
az acr credential show --name genesysaudiocr
```

### 2. Build and Push Docker Image

```powershell
# Login to container registry
az acr login --name genesysaudiocr

# Build image
docker build -t genesysaudiocr.azurecr.io/audio-connector:latest .

# Push image to ACR
docker push genesysaudiocr.azurecr.io/audio-connector:latest
```

### 3. Create and Configure Azure Web App

```powershell
# Create app service plan
az appservice plan create --name genesys-audio-plan `
    --resource-group genesys-audio-rg `
    --sku P1V3 `
    --is-linux

# Create web app
az webapp create --resource-group genesys-audio-rg `
    --plan genesys-audio-plan `
    --name genesys-cloud-audio-connector `
    --deployment-container-image-name genesysaudiocr.azurecr.io/audio-connector:latest

# Configure container registry credentials
$acrUser = $(az acr credential show --name genesysaudiocr --query "username" -o tsv)
$acrPassword = $(az acr credential show --name genesysaudiocr --query "passwords[0].value" -o tsv)

az webapp config container set `
    --name genesys-cloud-audio-connector `
    --resource-group genesys-audio-rg `
    --docker-custom-image-name genesysaudiocr.azurecr.io/audio-connector:latest `
    --docker-registry-server-url https://genesysaudiocr.azurecr.io `
    --docker-registry-server-user $acrUser `
    --docker-registry-server-password $acrPassword

# Configure environment variables from .env file
az webapp config appsettings set `
    --resource-group genesys-audio-rg `
    --name genesys-cloud-audio-connector `
    --settings @.env
```

## Quick Deployment Using PowerShell Script

A PowerShell script (`deploy.ps1`) is provided for easy deployment:

1. Open PowerShell as Administrator
2. Navigate to project directory
3. Run the deployment script:

```powershell
.\deploy.ps1
```

The script will:

- Check prerequisites (Azure CLI, Docker)
- Build the Docker image
- Push to Azure Container Registry
- Deploy to Azure Web App

## Security Best Practices

1. **Container Security**:

   - Non-root user in container (configured in Dockerfile)
   - Multi-stage builds to minimize attack surface
   - Regular security updates for base images
   - Dependencies audit during build

2. **Infrastructure Security**:

   - Web App runs in isolated container
   - Use Azure Key Vault for sensitive configuration
   - Regular rotation of ACR credentials
   - Network security rules for container registry

3. **Monitoring and Health**:
   - Container health checks configured
   - Azure Monitor integration
   - Application logs available through Azure portal

## Troubleshooting

### View Logs

```powershell
# Stream logs
az webapp log tail --name genesys-cloud-audio-connector `
    --resource-group genesys-audio-rg

# Download logs
az webapp log download --name genesys-cloud-audio-connector `
    --resource-group genesys-audio-rg
```

### Check Container Health

```powershell
# View container status
az webapp show --name genesys-cloud-audio-connector `
    --resource-group genesys-audio-rg

# Restart container if needed
az webapp restart --name genesys-cloud-audio-connector `
    --resource-group genesys-audio-rg
```

### Common Issues

1. **Docker Desktop not running**:

   - Ensure Docker Desktop is running and accessible
   - Check Docker Desktop settings for Windows containers vs Linux containers

2. **Azure CLI Authentication**:

   - Run `az login` if you encounter authentication issues
   - Verify your Azure subscription is active

3. **PowerShell Execution Policy**:
   If you cannot run the deploy.ps1 script, you may need to adjust the PowerShell execution policy:

   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

4. **Network Issues**:
   - Verify your VPN isn't blocking Docker or Azure connections
   - Check corporate proxy settings if applicable
