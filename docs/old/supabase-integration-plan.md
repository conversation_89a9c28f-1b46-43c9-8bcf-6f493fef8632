# Supabase Integration Plan

## 1. Database Schema

### Chat History Table

```sql
CREATE TABLE chat_history (
    id SERIAL PRIMARY KEY,
    ani VARCHAR(20) NOT NULL,           -- Caller's phone number
    conversation_id TEXT NOT NULL,       -- Conversation identifier
    message_text TEXT NOT NULL,          -- Actual message content
    is_bot BOOLEAN NOT NULL,            -- true for bot, false for customer
    created_at TIMESTAMPTZ DEFAULT NOW() -- Message timestamp
);

CREATE INDEX idx_chat_history_ani_conv
ON chat_history(ani, conversation_id);
```

### Conversation Summaries Table

```sql
CREATE TABLE conversation_summaries (
    id SERIAL PRIMARY KEY,
    ani VARCHAR(20) NOT NULL,           -- Caller's phone number
    conversation_id TEXT NOT NULL,       -- Conversation identifier
    start_time TIMESTAMPTZ NOT NULL,    -- Conversation start time
    end_time TIMESTAMPTZ NOT NULL,      -- Conversation end time
    summary_text TEXT NOT NULL,          -- Summarized conversation in Czech
    message_count INT NOT NULL,         -- Total number of messages
    duration_seconds INT NOT NULL,       -- Conversation duration
    created_at TIMESTAMPTZ DEFAULT NOW() -- Summary creation timestamp
);

CREATE INDEX idx_summaries_ani
ON conversation_summaries(ani);
```

## 2. Implementation Steps

### A. Supabase Integration

1. Create new service `src/services/supabase-service.ts`
   - Initialize Supabase client with URL and anon key
   - Implement methods for database operations

```typescript
interface ChatMessage {
  ani: string;
  conversationId: string;
  messageText: string;
  isBot: boolean;
}

interface ConversationSummary {
  ani: string;
  conversationId: string;
  startTime: Date;
  endTime: Date;
  summaryText: string;
  messageCount: number;
  durationSeconds: number;
}
```

### B. Bot Service Modifications

```mermaid
sequenceDiagram
    participant Client
    participant BotService
    participant OpenAI
    participant Supabase

    Client->>BotService: Connect with ANI
    BotService->>Supabase: Get last conversation summary
    alt Has Previous Summary
        BotService->>BotService: Add to system prompt:<br/>"Zákazník tě naposledy kontaktovat dne [datetime]<br/>a hovořili jste o [last convesation summary]"
    end
    BotService->>OpenAI: Initialize conversation

    loop Each Message
        Client->>BotService: Send message
        BotService->>Supabase: Store customer message
        BotService->>OpenAI: Get response
        BotService->>Supabase: Store bot response
        BotService-->>Client: Return response
    end

    Client->>BotService: Disconnect
    BotService->>OpenAI: Generate summary with dedicated prompt
    BotService->>Supabase: Store conversation summary
```

### C. Core Changes Required

1. Update `BotResource` class:

```typescript
export class BotResource {
  private ani: string | null = null;
  private conversationStartTime: Date | null = null;

  async initialize(ani: string, conversationId: string) {
    this.ani = ani;
    this.conversationStartTime = new Date();

    // Get last conversation summary
    const lastSummary = await supabaseService.getLastSummary(ani);
    if (lastSummary) {
      const systemPromptAddition =
        `Zákazník tě naposledy kontaktovat dne ${formatDate(lastSummary.endTime)} ` +
        `a hovořili jste o ${lastSummary.summaryText}`;
      this.conversationHistory[0].content += '\n\n' + systemPromptAddition;
    }
  }

  async dispose() {
    if (this.ani && this.conversationStartTime) {
      const history = await supabaseService.getConversationHistory(this.ani);

      // Dedicated prompt for summarization
      const summaryPrompt = [
        {
          role: 'system',
          content: 'Summarize the following conversation in Czech language.',
        },
        {
          role: 'user',
          content: history.map(m => `${m.isBot ? 'Bot' : 'Zákazník'}: ${m.messageText}`).join('\n'),
        },
      ];

      const summary = await this.getOpenAIResponse(summaryPrompt);

      await supabaseService.storeSummary({
        ani: this.ani,
        conversationId: this.conversationId,
        startTime: this.conversationStartTime,
        endTime: new Date(),
        summaryText: summary,
        messageCount: history.length,
        durationSeconds: Math.floor(
          (new Date().getTime() - this.conversationStartTime.getTime()) / 1000
        ),
      });
    }

    // Original cleanup code...
    this.conversationHistory = [];
    this.endpoint = null;
    this.apiKey = null;
    this.deploymentName = null;
  }
}
```

### D. Error Handling & Resilience

1. Implement retry logic for database operations
2. Add error boundaries for OpenAI summarization calls
3. Handle edge cases:
   - No previous conversations
   - Failed summary generation
   - Database connection issues

## 3. Implementation Notes

- The Supabase URL is already available in .env file
- Each conversation is uniquely identified by ANI + conversationId
- Message history is maintained in both memory and database during active conversations
- Summary generation happens at the end of conversation using OpenAI
- System prompt is enhanced with last conversation summary only when available
