# In-Memory Database Implementation Plan

## Overview

Replace the current Supabase integration with a pure in-memory database implementation while maintaining existing functionality and adding a 50 message per conversation limit.

## Current State

- Hybrid implementation with both Supabase and mock storage
- No message limits per conversation
- PostgreSQL dependencies
- Mock storage already implements core functionality

## Implementation Steps

### 1. Create New InMemoryDatabaseService

- Create new class based on existing MockStorage implementation
- Add 50 message limit per conversationId
- Implement automatic cleanup of old messages
- Remove all Supabase/PostgreSQL dependencies
- Maintain existing interfaces for compatibility

```typescript
class InMemoryDatabaseService {
  private static instance: InMemoryDatabaseService;
  private chatHistory: Map<string, DbChatRecord[]> = new Map(); // conversationId -> messages
  private summaries: DbSummaryRecord[] = [];
  private readonly MESSAGE_LIMIT = 50;

  // Methods will be similar to MockStorage but with message limits
}
```

### 2. Update Dependencies

1. Remove from package.json:

   - pg (PostgreSQL client)
   - Related database dependencies

2. Remove from .env:
   - DATABASE_URL
   - MOCKING_DATABASE
   - Other Supabase-related variables

### 3. Code Changes

#### Phase 1: Create New Service

1. Create new InMemoryDatabaseService class
2. Implement message limit functionality
3. Add automatic cleanup of old messages
4. Port all existing MockStorage functionality

#### Phase 2: Update Service References

1. Update bot-service.ts to use InMemoryDatabaseService
2. Update index.ts initialization
3. Update any other service references

#### Phase 3: Cleanup

1. Remove supabase-service.ts
2. Remove Supabase-related configuration
3. Update documentation
4. Remove unused dependencies

## Technical Details

### Message Limit Implementation

```typescript
private enforceMessageLimit(conversationId: string): void {
  const messages = this.chatHistory.get(conversationId);
  if (messages && messages.length > this.MESSAGE_LIMIT) {
    // Remove oldest messages to maintain limit
    this.chatHistory.set(
      conversationId,
      messages.slice(-this.MESSAGE_LIMIT)
    );
  }
}
```

### Testing Plan

1. Verify message limit enforcement
2. Test conversation history retrieval
3. Validate summary functionality
4. Check memory usage under load
5. Verify no data persistence between restarts

## Migration Notes

- No database migration needed as we're removing persistence
- All existing data will be lost on service restart
- Applications must handle temporary nature of in-memory storage

## Risks and Mitigation

1. **Data Persistence**:

   - Risk: All data is lost on service restart
   - Mitigation: Document clearly in README and logs

2. **Memory Usage**:

   - Risk: Large number of conversations could consume significant memory
   - Mitigation: 50 message limit per conversation + automatic cleanup

3. **Service Disruption**:
   - Risk: Existing applications expect persistent storage
   - Mitigation: Clear documentation and version bumping

## Timeline

1. Development: 1-2 days
2. Testing: 1 day
3. Documentation: 0.5 day
4. Deployment: 0.5 day

Total: 3-4 days

## Acceptance Criteria

- [ ] All tests pass with new implementation
- [ ] Message limit of 50 per conversation enforced
- [ ] No Supabase/PostgreSQL dependencies remain
- [ ] Memory usage remains stable under load
- [ ] Documentation updated
- [ ] Clean shutdown and startup behavior
