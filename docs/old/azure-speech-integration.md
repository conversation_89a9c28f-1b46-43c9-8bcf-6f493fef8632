# Azure Speech Service Integration

## Overview

The ASR service uses Azure Cognitive Services Speech-to-Text for real-time transcription. The implementation includes a connection pooling mechanism to handle concurrent transcriptions efficiently.

## Configuration

### Environment Variables

- `AZURE_SPEECH_KEY`: Azure Speech Service subscription key
- `AZURE_SPEECH_REGION`: Azure region (e.g., "westeurope")

### Connection Pool Settings

The ASR service now supports connection pooling to handle multiple concurrent transcriptions:

```typescript
// Default settings
const service = createSpeechService(); // Uses pool with default settings (10 concurrent)

// Custom pool settings
const service = createSpeechService(true, 20, 45000); // 20 concurrent, 45s timeout

// Disable pooling (legacy single-connection mode)
const service = createSpeechService(false);
```

## Resource Management

### Connection Pooling

- Default pool size: 10 concurrent connections
- Configurable queue timeout: 30 seconds by default
- Automatic connection reuse and cleanup
- Fair scheduling for queued requests

### Pool Statistics

Monitor pool usage with the getPoolStats() method:

```typescript
const stats = service.getPoolStats();
// Returns:
{
  poolSize: number,          // Current number of connections
  activeConnections: number, // Currently active connections
  queueLength: number,      // Pending requests in queue
  maxPoolSize: number       // Maximum allowed connections
}
```

## Error Handling

The service includes robust error handling for common scenarios:

- Connection limits exceeded: Requests are queued automatically
- Network issues: Automatic retry with exponential backoff
- Service errors: Detailed error messages with appropriate actions

## Recommendations

### Pool Size Configuration

1. Start with the default pool size (10)
2. Monitor queue length during peak usage
3. Adjust pool size based on:
   - Number of concurrent calls
   - Available system resources
   - Azure subscription limits

### Queue Management

- Set appropriate timeouts based on your use case
- Monitor queue length to avoid excessive wait times
- Consider implementing request prioritization if needed

## Best Practices

1. Monitor pool statistics during peak usage
2. Set appropriate timeouts for your use case
3. Handle errors appropriately in your application
4. Clean up resources using the dispose() method when done
5. Consider your Azure subscription limits when configuring pool size
