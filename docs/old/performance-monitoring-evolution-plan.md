# Performance Monitoring Evolution Plan

## Current System Analysis

The current system uses:

- PerformanceLogger singleton for timing operations
- Session tracking with WebSocket connections
- InMemoryDatabaseService for chat history and summaries
- Basic timing metrics for operations (ASR, TTS, bot processing)

## Planned Enhancements

### 1. Performance Metrics Store

```typescript
interface ContentMetrics {
  contentLength: number;
  category: 'xs' | 's' | 'm' | 'l'; // <=50, <=100, <=150, >150 chars
  processingTime: number;
}

interface TrendMetrics {
  timeRange: string; // e.g., "1h", "24h", "7d"
  contentCategory: {
    xs: number; // Count of <=50 char interactions
    s: number; // Count of <=100 char interactions
    m: number; // Count of <=150 char interactions
    l: number; // Count of >150 char interactions
  };
  averageProcessingTimes: {
    xs: number; // Avg processing time for <=50 chars
    s: number; // Avg processing time for <=100 chars
    m: number; // Avg processing time for <=150 chars
    l: number; // Avg processing time for >150 chars
  };
  contextSizeImpact: {
    smallContext: number; // Avg processing time with context <1000 chars
    mediumContext: number; // Avg processing time with context 1000-5000 chars
    largeContext: number; // Avg processing time with context >5000 chars
  };
}

interface SystemMetrics {
  timestamp: Date;
  cpu: {
    usage: number; // Percentage
    loadAverage: number[]; // 1, 5, 15 minute averages
  };
  memory: {
    total: number; // Total system memory in bytes
    used: number; // Used memory in bytes
    free: number; // Free memory in bytes
    heapTotal: number; // V8 total heap size
    heapUsed: number; // V8 used heap size
    rss: number; // Resident Set Size
  };
  processMetrics: {
    uptime: number; // Process uptime in seconds
    activeHandles: number; // Active handle count
    activeRequests: number; // Active request count
  };
}

interface ConversationMetrics {
  conversationId: string;
  ani: string;
  startTime: Date;
  endTime: Date;
  userInputCount: number;
  botResponseCount: number;

  // Content length metrics
  userInputs: ContentMetrics[];
  botResponses: ContentMetrics[];
  currentContextSize: number;

  // Performance metrics
  asrMetrics: {
    avgProcessingTime: number;
    minProcessingTime: number;
    maxProcessingTime: number;
    totalProcessingTime: number;
    sampleCount: number;
  };

  ttsMetrics: {
    avgProcessingTime: number;
    minProcessingTime: number;
    maxProcessingTime: number;
    avgStreamReceiveTime: number;
    totalProcessingTime: number;
    sampleCount: number;
  };

  botMetrics: {
    avgResponseTime: number;
    minResponseTime: number;
    maxResponseTime: number;
    totalProcessingTime: number;
    sampleCount: number;
    byContentLength: {
      xs: { avg: number; count: number };
      s: { avg: number; count: number };
      m: { avg: number; count: number };
      l: { avg: number; count: number };
    };
  };

  systemMetricsSnapshots: SystemMetrics[];
}
```

### 2. New Components

1. **ConversationPerformanceMonitor**

   - Main class for tracking conversation-level metrics
   - Tracks user/bot interaction counts and content lengths
   - Manages timing aggregation
   - Tracks context size impact
   - Integrates with PerformanceMetricsStore
   - Captures system metrics snapshots

2. **TrendAnalyzer**

   - Analyzes performance trends by content length
   - Tracks context size impact on performance
   - Generates hourly/daily/weekly reports
   - Identifies performance patterns
   - Provides optimization recommendations

3. **SystemMetricsCollector**

   - Collects CPU usage metrics
   - Monitors memory utilization
   - Tracks process statistics
   - Configurable sampling interval
   - Historical metrics retention

4. **PerformanceMetricsStore**

   - In-memory storage for metrics
   - Maintains metrics per conversation
   - Handles metric aggregation and calculations
   - Provides query interface for metrics retrieval
   - Stores system metrics history
   - Manages trend data storage

5. **MetricsController**
   - REST API endpoints:
     - GET `/metrics/:conversationId` for conversation metrics
     - GET `/metrics/system` for system metrics
     - GET `/metrics/trends` for performance trends
     - Query parameters for filtering
   - JSON response format

### 3. Implementation Steps

#### Phase 1: Core Implementation

1. Create PerformanceMetricsStore class

   - Design metric storage structure
   - Implement basic CRUD operations
   - Add metric calculation methods
   - Add content length tracking

2. Implement TrendAnalyzer

   - Add content length categorization
   - Implement trend calculations
   - Add context size tracking
   - Setup trend storage

3. Implement SystemMetricsCollector

   - Add CPU monitoring
   - Add memory monitoring
   - Add process metrics collection
   - Configure sampling intervals

4. Enhance PerformanceLogger
   - Add interaction counting
   - Add content length tracking
   - Add context size monitoring
   - Integrate with metrics store
   - Implement metric aggregation
   - Add system metrics collection

#### Phase 2: API & Advanced Features

1. Implement REST API endpoints

   - Add metrics controller
   - Add system metrics endpoints
   - Add trends endpoint
   - Define response formats
   - Add filtering capabilities
   - Add basic error handling

2. Add advanced metrics
   - Implement min/max tracking
   - Add rolling averages
   - Calculate throughput metrics
   - Add system metrics aggregation
   - Add trend analysis

#### Phase 3: Optimization & Utilities

1. Add metric persistence

   - Implement storage mechanism
   - Add data retention policies
   - Handle data migration
   - Configure metrics rotation
   - Setup trend data archiving

2. Add visualization support
   - Define visualization formats
   - Add export capabilities
   - Create basic dashboard
   - Add trend graphs
   - Add system metrics graphs

### 4. Architecture Diagrams

```mermaid
graph TB
    subgraph Current System
        PL[PerformanceLogger]
        WS[WebSocket Session]
        DB[InMemoryDB]
        PL -->|Basic Timing| WS
        WS -->|Chat History| DB
    end

    subgraph Enhanced System
        CPM[Conversation Performance Monitor]
        TA[Trend Analyzer]
        SMC[System Metrics Collector]
        PMS[Performance Metrics Store]
        API[Metrics REST API]

        CPM -->|Store Metrics| PMS
        SMC -->|Store System Metrics| PMS
        TA -->|Store Trends| PMS
        PMS -->|Retrieve Metrics| API

        subgraph Metrics
            CM[Conversation Metrics]
            PM[Performance Metrics]
            SM[System Metrics]
            TM[Trend Metrics]
            AM[Aggregated Metrics]

            CM -->|Aggregate| AM
            PM -->|Aggregate| AM
            SM -->|Aggregate| AM
            TM -->|Analyze| TA
        end
    end
```

### 5. Data Flow

```mermaid
sequenceDiagram
    participant C as Client
    participant S as Session
    participant PM as PerformanceMonitor
    participant TA as TrendAnalyzer
    participant SM as SystemMetrics
    participant MS as MetricsStore
    participant API as REST API

    C->>S: Start Conversation
    S->>PM: Initialize Metrics
    SM->>MS: Begin System Metrics Collection

    loop Conversation
        C->>S: User Input
        S->>PM: Track Content Length
        S->>PM: Track ASR Time
        S->>PM: Track Bot Time
        S->>PM: Track TTS Time
        S->>TA: Update Trends
        SM->>MS: Capture System Metrics
        PM->>MS: Update Metrics
    end

    C->>S: End Conversation
    S->>PM: Finalize Metrics
    S->>TA: Update Trend Analysis
    SM->>MS: Final System Metrics
    PM->>MS: Store Final Metrics

    C->>API: Request Metrics/Trends
    API->>MS: Fetch Data
    MS->>API: Return Data
    API->>C: Send Response
```

### 6. Benefits

1. **Improved Monitoring**

   - Comprehensive conversation metrics
   - Content length impact analysis
   - Context size performance tracking
   - Real-time monitoring capabilities
   - System resource utilization insights

2. **Better Analysis**

   - Historical performance data
   - Trend analysis by content length
   - Context size impact assessment
   - Bottleneck identification
   - System health correlation

3. **Optimization Opportunities**
   - Performance optimization insights
   - Resource utilization tracking
   - Content length recommendations
   - Context size optimization
   - System scaling decisions

### 7. Success Metrics

- Successful implementation of all metrics
- API response time under 100ms
- Accurate min/max/avg calculations
- Minimal memory overhead (<5% additional)
- CPU overhead from monitoring <1%
- System metrics collection every 10s
- Trend analysis updated every minute
- Context size tracking accuracy >99%
- 30-day metrics retention
- Easy integration with existing systems
