# CI/CD Pipeline Workflow

Latest documentation of the **GitOps Application Workflow** is to be seen here: [GitOps Application Workflow](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_WORKFLOW.md).

---

The GitOps CI/CD Pipeline allows you to `sync` or `remove` your Deployments Repository Application on top of any [Managed K8s Cluster](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/catalog/CLUSTERS_MANAGED.md).

To make this automation as much sustainable as we can, the Pipeline configuration is not stored within this repository but rather invoked using inclusion from the main GitOps Repository:

`.gitlab-ci.yml`

```yaml
include:
  - project: 'ntwcl/gitopsntw'
    ref: HEAD
    file:
      - '/templates/cicd/gitlab-ci-deployment_form.yml'
      - '/templates/cicd/gitlab-ci-deployment.yml'
```

Following two remote files of the [GitOps Repository](https://network.git.cz.o2/ntwcl/gitopsntw/) are referenced:

- [gitlab-ci-deployment.yml](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/cicd/gitlab-ci-deployment.yml): The main Pipeline configuration
- [gitlab-ci-deployment_form.yml](https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/templates/cicd/gitlab-ci-deployment_form.yml): Your **Run Pipeline** form for manipulating the K8s Clusters using GitOps

# Extending the CI/CD Pipeline

To see some examples of extending your CI/CD Pipeline you may inspect the included Helm Starter files here: [charts/starters/python/files/](/charts/starters/python/files/).

## Build on `tag`

To build some image of your liking when tagging this GitLab Repository, see the [charts/starters/python/files/.gitlab-ci.yml](/charts/starters/python/files/.gitlab-ci.yml) and its job `docker:build:on_tag`. It's the most basic example of Docker Image build.

## Sync Docker images

To build and synchronize your Docker Images between remote repository of source code and this Deployments repository, see the:

1. [charts/starters/python/files/.gitlab-ci.yml](/charts/starters/python/files/.gitlab-ci.yml): `docker:synchronize:images` job
2. [charts/starters/python/files/.gitlab-ci-remote.yml](/charts/starters/python/files/.gitlab-ci-remote.yml): `docker:images` job
3. [charts/starters/python/files/registry_sync.sh](/charts/starters/python/files/registry_sync.sh): script to sync the Docker Images (for convenience purposes mostly)

There are following prerequisites to be made:

1. Configure the **Access Token Allowlist** for Docker Image pulling
2. Configure **Pipeline Trigger Token** for... remote `API` CI/CD Pipeline triggering

### Source Code Repository: CI/CD Token Allowlist

To simplify cooperation between projects there is **Token Access Allowlist** configuration available within the **SOURCE CODE** GitLab Repository CI/CD Settings. Go to the `Settings` / `CI/CD` / `Token Access` menu and set the scope of your **Access Token** to your `Deployment` repository's path. I.e., should your setup be like this:

1. source code repository: `network-management-reporting/anomalydetection/ip_anomaly_detector_analysis`
2. deployments repository: `deployments/nmrep-anomaly`

... configuration of the **Source Code Repository** must look like this:

![TOKEN_ALLOWLIST.png](/docs/TOKEN_ALLOWLIST.png)

Please keep in mind that configuring this **Allowlist** the other way around achieves nothing.

### Deployments Repository: Pipeline Triggering

If previous configuration allows the Deployments Repository to pull foreign Docker Image, this configuration allows the Source Code Repository to trigger the Docker Image synchronization itself. Once again there are some (dark) necessities to be done beforehand:

**Step One.** Go to the `Settings` / `CI/CD` / `Pipeline trigger tokens` configuration menu within your **Deployments Repository** and create new Token:

![TOKEN_TRIGGER.png](/docs/TOKEN_TRIGGER.png)

Be sure to copy the `Token` itself, as you won't be able to see it again like never ever. Then go to the `Settings` / `CI/CD` / `Variables` configuration menu within your **Source Code Repository** and create new CI/CD Variable with name `DOCKER_SYNC_TRIGGER_TOKEN` and value of the Trigger Token.

**Step Two.** The `DOCKER_SYNC_TRIGGER_TOKEN` variable is used for triggering the synchronization CI/CD pipeline as seen here: [charts/starters/python/files/.gitlab-ci-remote.yml](/charts/starters/python/files/.gitlab-ci-remote.yml) (`docker:images` job). The API call itself be like:

```shell
curl -X POST \
     -F token="${DOCKER_SYNC_TRIGGER_TOKEN}" \
     -F "ref=main" \
     -F "variables[DOCKER_SYNC]=True" \
     -F "variables[SOURCE_VERSION]=${CI_COMMIT_TAG}" \
     https://network.git.cz.o2/api/v4/projects/<REMOTE_PROJECT_ID>/trigger/pipeline
```

As you may kindly observe, there are four `-F` parameters for `curl` API call, first of them being our freshly configured `DOCKER_SYNC_TRIGGER_TOKEN` variable and rest of them parameters to be used within the target repository of the API call (Deployments Repository).

What you need to specify manually here is the `<REMOTE_PROJECT_ID>` number of the Deployments Repository. You may find this number by opening the `Settings` / `General` menu:

![GENERAL.png](/docs/GENERAL.png)

**Step Three.** The last but not the least configuration is needed for the [registry_sync.sh](/charts/starters/python/files/registry_sync.sh) script, which by design does not know the address of your Source Code Repository, but has his own way of informing you about this condition. Open the script and you will find this:

```shell
SOURCE_REPO="network.git.cz.o2:5005/<group>/<project>"
# Delete this afterwards
# >>>
echo "Set the 'SOURCE_REPO' variable of this shell-script first: '${SOURCE_REPO}'"
exit 0
```

Using the beforementioned setup, change this part of the script to:

```shell
SOURCE_REPO="network.git.cz.o2:5005/network-management-reporting/anomalydetection/ip_anomaly_detector_analysis"
```

...and move the script itself to the content root of your Deployments Repository.

And you are all set now.
