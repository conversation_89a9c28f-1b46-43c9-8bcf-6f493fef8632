Removed from toolbox

# Type Definitions

This section contains the type definition of the protocol messages in TypeScript.

[

## Protocol Types

](/devapps/audiohook/type-definitions#protocol-types)

<a id="types-core-uuid"/>

Copied

Uuid

    type Uuid = string;             // UUID as defined by RFC#4122

<a id="types-core-sequencenumber"/>

Copied

SequenceNumber

    type SequenceNumber = number;   // Non-negative integer

<a id="types-core-jsonvalue"/>

Copied

JsonValue

    type JsonValue = string | number | boolean | null | JsonObject | JsonArray | JsonStringMap;

<a id="types-core-jsonarray"/>

Copied

JsonArray

    type JsonArray = JsonValue[];

<a id="types-core-jsonobject"/>

Copied

JsonObject

    type JsonObject = {
        [key: string]: JsonValue
    };

<a id="types-core-jsonstringmap"/>

Copied

JsonStringMap

    type JsonStringMap = {
        [key: string]: string
    };

<a id="types-core-emptyobject"/>

Copied

EmptyObject

    type EmptyObject = {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        [K in any] : never
    };

<a id="types-core-duration"/>

Copied

Duration

    type Duration = `PT${number}S`; // ISO8601 duration in seconds, where 'number' in non-negative decimal representation

<a id="types-core-mediachannel"/>

Copied

MediaChannel

    type MediaChannel = 'external' | 'internal';

<a id="types-core-mediachannelid"/>

Copied

MediaChannelId

    type MediaChannelId = 0 | 1;

<a id="types-core-mediachannels"/>

Copied

MediaChannels

    type MediaChannels = MediaChannel[];

<a id="types-core-mediatype"/>

Copied

MediaType

    type MediaType = 'audio';

<a id="types-core-mediaformat"/>

Copied

MediaFormat

    type MediaFormat = 'PCMU';

<a id="types-core-mediarate"/>

Copied

MediaRate

    type MediaRate = 8000;

<a id="types-core-mediaparameter"/>

Copied

MediaParameter

    type MediaParameter = {
        type: MediaType;
        format: MediaFormat;
        channels: MediaChannels;
        rate: MediaRate;
    };

<a id="types-core-mediaparameters"/>

Copied

MediaParameters

    type MediaParameters = MediaParameter[];

<a id="types-core-languagecode"/>

Copied

LanguageCode

    type LanguageCode = string;

<a id="types-core-supportedlanguages"/>

Copied

SupportedLanguages

    type SupportedLanguages = LanguageCode[];

<a id="types-core-evententitybase"/>

Copied

EventEntityBase

    type EventEntityBase<T extends string, D extends JsonValue> = {
        type: T;
        data: D;
    };

<a id="types-entities-evententitypredefined"/>

Copied

EventEntityPredefined

    type EventEntityPredefined =
        | EventEntityTranscript
        | EventEntityBargeIn
        | EventEntityBotTurnResponse;

<a id="types-entities-evententity"/>

Copied

EventEntity

    type EventEntity =
         | EventEntityPredefined
         | EventEntityBase<string, JsonValue>

<a id="types-entities-evententities"/>

Copied

EventEntities

    type EventEntities = EventEntity[];

<a id="types-message-messagebase"/>

Copied

MessageBase

    type MessageBase<Type extends string = string, Parameters extends JsonObject = JsonObject> = {
        version: '2';
        id: Uuid;
        type: Type;
        seq: SequenceNumber;
        parameters: Parameters;
    };

<a id="types-message-clientmessagebase"/>

Copied

ClientMessageBase

    type ClientMessageBase<T extends string = string, P extends JsonObject = JsonObject> = MessageBase<T, P> & {
        serverseq: SequenceNumber;
        position: Duration;
    };

<a id="types-message-servermessagebase"/>

Copied

ServerMessageBase

    type ServerMessageBase<T extends string = string, P extends JsonObject = JsonObject> = MessageBase<T, P> & {
        clientseq: SequenceNumber;
    };

<a id="types-message-closereason"/>

Copied

CloseReason

    type CloseReason = 'end' | 'error' | 'disconnect' | 'reconnect';

<a id="types-message-closeparameters"/>

Copied

CloseParameters

    type CloseParameters = {
        reason: CloseReason;
    };

<a id="types-message-closedparameters"/>

Copied

ClosedParameters

    type ClosedParameters = EmptyObject;

<a id="types-message-discardedparameters"/>

Copied

DiscardedParameters

    type DiscardedParameters = {
        start: Duration;
        discarded: Duration;
    };

<a id="types-message-disconnectreason"/>

Copied

DisconnectReason

    type DisconnectReason = 'completed' | 'unauthorized' | 'error';

<a id="types-message-disconnectparameters"/>

Copied

DisconnectParameters

    type DisconnectParameters = {
        reason: DisconnectReason;
        info?: string;
        outputVariables?: JsonStringMap;
    };

<a id="types-message-errorcode"/>

Copied

ErrorCode

    type ErrorCode =
        | 400
        | 405
        | 408
        | 409
        | 413
        | 415
        | 429
        | 500
        | 503;

<a id="types-message-errorparameters"/>

Copied

ErrorParameters

    type ErrorParameters = {
        code: ErrorCode;
        message: string;
        retryAfter?: Duration;          // Used by client rate limiter (429)
    };

<a id="types-message-eventparameters"/>

Copied

EventParameters

    type EventParameters = {
        entities: EventEntities;
    };

<a id="types-message-participant"/>

Copied

Participant

    type Participant = {
        id: Uuid;
        ani: string;
        aniName: string;
        dnis: string;
    };

<a id="types-message-openparameters"/>

Copied

OpenParameters

    type OpenParameters = {
        organizationId: Uuid;
        conversationId: Uuid;
        participant: Participant;
        media: MediaParameters;
        language?: LanguageCode;
        supportedLanguages?: boolean;
        customConfig?: JsonObject;
        inputVariables?: JsonStringMap;
    };

<a id="types-message-openedparameters"/>

Copied

OpenedParameters

    type OpenedParameters = {
        media: MediaParameters;
        discardTo?: Duration;
        startPaused?: boolean;
        supportedLanguages?: SupportedLanguages;
    };

<a id="types-message-pauseparameters"/>

Copied

PauseParameters

    type PauseParameters = EmptyObject;

<a id="types-message-pausedparameters"/>

Copied

PausedParameters

    type PausedParameters = EmptyObject;

<a id="types-message-pingparameters"/>

Copied

PingParameters

    type PingParameters = {
        rtt?: Duration;
    };

<a id="types-message-pongparameters"/>

Copied

PongParameters

    type PongParameters = EmptyObject;

<a id="types-message-reconnectparameters"/>

Copied

ReconnectParameters

    type ReconnectParameters = {
        info?: string;
    };

<a id="types-message-resumeparameters"/>

Copied

ResumeParameters

    type ResumeParameters = EmptyObject;

<a id="types-message-resumedparameters"/>

Copied

ResumedParameters

    type ResumedParameters = {
        start: Duration;
        discarded: Duration;
    };

<a id="types-message-updateparameters"/>

Copied

UpdateParameters

    type UpdateParameters = {
        language?: LanguageCode;
    };

<a id="types-message-updatedparameters"/>

Copied

UpdatedParameters

    type UpdatedParameters = EmptyObject;

<a id="types-message-closemessage"/>

Copied

CloseMessage

    type CloseMessage = ClientMessageBase<'close', CloseParameters>;

<a id="types-message-closedmessage"/>

Copied

ClosedMessage

    type ClosedMessage = ServerMessageBase<'closed', ClosedParameters>;

<a id="types-message-discardedmessage"/>

Copied

DiscardedMessage

    type DiscardedMessage = ClientMessageBase<'discarded', DiscardedParameters>;

<a id="types-message-disconnectmessage"/>

Copied

DisconnectMessage

    type DisconnectMessage = ServerMessageBase<'disconnect', DisconnectParameters>;

<a id="types-message-errormessage"/>

Copied

ErrorMessage

    type ErrorMessage = ClientMessageBase<'error', ErrorParameters>;

<a id="types-message-eventmessage"/>

Copied

EventMessage

    type EventMessage = ServerMessageBase<'event', EventParameters>;

<a id="types-message-openmessage"/>

Copied

OpenMessage

    type OpenMessage = ClientMessageBase<'open', OpenParameters>;

<a id="types-message-openedmessage"/>

Copied

OpenedMessage

    type OpenedMessage = ServerMessageBase<'opened', OpenedParameters>;

<a id="types-message-pausemessage"/>

Copied

PauseMessage

    type PauseMessage = ServerMessageBase<'pause', PauseParameters>;

<a id="types-message-pausedmessage"/>

Copied

PausedMessage

    type PausedMessage = ClientMessageBase<'paused', PausedParameters>;

<a id="types-message-pingmessage"/>

Copied

PingMessage

    type PingMessage = ClientMessageBase<'ping', PingParameters>;

<a id="types-message-pongmessage"/>

Copied

PongMessage

    type PongMessage = ServerMessageBase<'pong', PongParameters>;

<a id="types-message-reconnectmessage"/>

Copied

ReconnectMessage

    type ReconnectMessage = ServerMessageBase<'reconnect', ReconnectParameters>;

<a id="types-message-resumemessage"/>

Copied

ResumeMessage

    type ResumeMessage = ServerMessageBase<'resume', ResumeParameters>;

<a id="types-message-resumedmessage"/>

Copied

ResumedMessage

    type ResumedMessage = ClientMessageBase<'resumed', ResumedParameters>;

<a id="types-message-updatemessage"/>

Copied

UpdateMessage

    type UpdateMessage = ClientMessageBase<'update', UpdateParameters>;

<a id="types-message-updatedmessage"/>

Copied

UpdatedMessage

    type UpdatedMessage = ServerMessageBase<'updated', UpdatedParameters>;

<a id="types-message-clientmessage"/>

Copied

ClientMessage

    type ClientMessage =
        | CloseMessage
        | DiscardedMessage
        | ErrorMessage
        | OpenMessage
        | PausedMessage
        | PingMessage
        | ResumedMessage
        | UpdateMessage
        | DTMFMessage
        | PlaybackStartedMessage
        | PlaybackCompletedMessage;

<a id="types-message-servermessage"/>

Copied

ServerMessage

    type ServerMessage =
        | ClosedMessage
        | DisconnectMessage
        | EventMessage
        | OpenedMessage
        | PauseMessage
        | PongMessage
        | ReconnectMessage
        | ResumeMessage
        | UpdatedMessage

<a id="types-message-message"/>

Copied

Message

    type Message = ClientMessage | ServerMessage;

<a id="types-message-clientmessagetype"/>

Copied

ClientMessageType

    type ClientMessageType = ClientMessage['type'];

<a id="types-message-clientmessageparameters"/>

Copied

ClientMessageParameters

    type ClientMessageParameters = ClientMessage['parameters'];

<a id="types-message-servermessagetype"/>

Copied

ServerMessageType

    type ServerMessageType = ServerMessage['type'];

<a id="types-message-servermessageparameters"/>

Copied

ServerMessageParameters

    type ServerMessageParameters = ServerMessage['parameters'];

<a id="types-message-messagetype"/>

Copied

MessageType

    type MessageType = ClientMessageType | ServerMessageType;

<a id="types-message-messageparameters"/>

Copied

MessageParameters

    type MessageParameters = ClientMessageParameters | ServerMessageParameters;

<a id="types-message-selectparametersfortype"/>

Copied

SelectParametersForType

    type SelectParametersForType<T extends string, M> = M extends {type: T, parameters: infer P} ? P : never;

<a id="types-message-selectmessagefortype"/>

Copied

SelectMessageForType

    type SelectMessageForType<T extends string, M> = M extends {type: T} ? M : never;

<a id="types-message-messagedispatcher"/>

Copied

MessageDispatcher

    type MessageDispatcher<M extends Message, R = void> = {
        readonly [T in M['type']]: (message: SelectMessageForType<T, M>) => R;

[

## Dialog Types

](/devapps/audiohook/type-definitions#dialog-types)

<a id="types-voice-bots-dtmfparameters"/>

Copied

DTMFParameters

    type DTMFParameters = {
        digit: string;
    };

<a id="types-voice-bots-dtmfmessage"/>

Copied

DTMFMessage

    type DTMFMessage = ClientMessageBase<'dtmf', DTMFParameters>;

<a id="types-voice-bots-playbackstartedparameters"/>

Copied

PlaybackStartedParameters

    type PlaybackStartedParameters = EmptyObject;

<a id="types-voice-bots-playbackstartedmessage"/>

Copied

PlaybackStartedMessage

    type PlaybackStartedMessage = ClientMessageBase<'playback_started', PlaybackStartedParameters>;

<a id="types-voice-bots-playbackcompletedparameters"/>

Copied

PlaybackCompletedParameters

    type PlaybackCompletedParameters = EmptyObject;

<a id="types-voice-bots-playbackcompletedmessage"/>

Copied

PlaybackCompletedMessage

    type PlaybackCompletedMessage = ClientMessageBase<'playback_completed', PlaybackCompletedParameters>;

<a id="types-voice-bots-evententitydatabargein"/>

Copied

EventEntityDataBargeIn

    type EventEntityDataBargeIn = EmptyObject;

<a id="types-voice-bots-evententitybargein"/>

Copied

EventEntityBargeIn

    type EventEntityBargeIn = EventEntityBase<'barge_in', EventEntityDataBargeIn>;

<a id="types-voice-bots-botturndisposition"/>

Copied

BotTurnDisposition

    type BotTurnDisposition = 'no_input' | 'no_match' | 'match';

<a id="types-voice-bots-evententitydatabotturnresponse"/>

Copied

EventEntityDataBotTurnResponse

    type EventEntityDataBotTurnResponse = {
        disposition: BotTurnDisposition;
        text?: string;
        confidence?: number;
    };

[

## Transcript Entity Types

](/devapps/audiohook/type-definitions#transcript-entity-types)

<a id="types-entities-transcript-evententitytranscript"/>

Copied

EventEntityTranscript

    type EventEntityTranscript = EventEntityBase<'transcript', EventEntityDataTranscript>;

<a id="types-entities-transcript-evententitydatatranscript"/>

Copied

EventEntityDataTranscript

    type EventEntityDataTranscript = {
        id: Uuid;
        channelId: MediaChannelId;
        isFinal: boolean;
        offset?: Duration;
        duration?: Duration;
        alternatives: TranscriptAlternative[];
    };

<a id="types-entities-transcript-transcriptalternative"/>

Copied

TranscriptAlternative

    type TranscriptAlternative = {
        confidence: number;
        languages?: LanguageCode[];
        interpretations: TranscriptInterpretation[];
    };

<a id="types-entities-transcript-transcriptinterpretationtype"/>

Copied

TranscriptInterpretationType

    type TranscriptInterpretationType = 'display';

<a id="types-entities-transcript-transcriptinterpretation"/>

Copied

TranscriptInterpretation

    type TranscriptInterpretation = {
        type: TranscriptInterpretationType;
        transcript: string;
        tokens?: TranscriptToken[];
    };

<a id="types-entities-transcript-transcripttokentype"/>

Copied

TranscriptTokenType

    type TranscriptTokenType = 'word';

<a id="types-entities-transcript-transcripttoken"/>

Copied

TranscriptToken

    type TranscriptToken = {
        type: TranscriptTokenType;
        value: string;
        confidence: number;
        offset: Duration;
        duration: Duration;
        language?: LanguageCode;
    };

Was this page helpful?
