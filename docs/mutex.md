# Mutex-Based State Machine Transition Control

## Overview

This document outlines the implementation plan for mutex-based control of state machine transitions, ensuring safe, predictable, and interruptible state changes. The approach prevents race conditions, supports barge-in/interruption, and provides a robust "second check" mechanism to compare the currently processed phase with the requested transition.

---

## Goals

- Prevent concurrent/overlapping transitions.
- Allow prioritized (barge-in) transitions when needed.
- Ensure transitions are queued, prioritized, or aborted according to system rules.
- Provide a clear, maintainable structure for transition control.

---

## Key Concepts

- **Mutex**: Ensures only one transition is processed at a time.
- **Current Phase**: The state/transition currently being processed.
- **Requested Phase**: The new transition being requested.
- **Second Check**: After acquiring the mutex, re-validate if the requested transition is still valid/desirable.
- **Transition Queue**: Holds pending transitions.
- **Barge-In/Interruption**: High-priority transitions that can preempt the current one.
- **Abort**: Cancels a transition if it becomes invalid or is superseded.

---

## Transition Control Flow

1. **Request**: A new transition is requested.
2. **Mutex Acquisition**: Attempt to acquire the transition mutex.
3. **Second Check**: After acquiring the mutex, compare the current phase and requested phase:
   - If the current phase matches the requested phase, proceed.
   - If the current phase has changed, re-evaluate (queue, prioritize, or abort).
4. **Transition Execution**: Perform the transition if valid.
5. **Release Mutex**: Unlock and process the next queued transition if any.

---

## Second Check Mechanism

- **Why?**: The system state may change between the initial request and mutex acquisition.
- **How?**: After acquiring the mutex, compare:
  - `currentPhase` (now) vs. `requestedPhase` (when requested)
- **Outcomes**:
  - **Match**: Proceed with transition.
  - **Mismatch**: Decide to queue, prioritize, or abort based on rules.

---

## Rules for Transition Handling

| Scenario                                 | Action         |
|-------------------------------------------|---------------|
| Current phase == requested phase          | Proceed       |
| Current phase is busy, request is normal  | Queue         |
| Current phase is busy, request is urgent  | Prioritize    |
| Current phase is incompatible/obsolete    | Abort         |
| Current phase is abortable, request is abort| Abort current, start new |

- **Queue**: Add to the end of the transition queue.
- **Prioritize**: Insert at the front of the queue or interrupt current transition if allowed.
- **Abort**: Cancel the requested or current transition, depending on context.

---

## Example: Barge-In/Interruption Flow

1. User requests a new transition (e.g., "Stop" during "Play").
2. System checks if "Stop" can interrupt "Play".
3. If allowed, "Stop" is prioritized:
   - Current "Play" transition is aborted.
   - "Stop" transition is executed immediately.
4. If not allowed, "Stop" is queued.

---

## Mermaid Diagram

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> Initializing: start()
    Initializing --> ProcessingInput: ready()
    ProcessingInput --> ProcessingBot: userInput()
    ProcessingBot --> Playing: botResponse()
    Playing --> Idle: playbackStopped()
    ProcessingInput --> Idle: abort()
    ProcessingBot --> Idle: abort()
    Playing --> ProcessingInput: bargeIn() / abort(playback)
    ProcessingBot --> Playing: bargeIn() / abort(botProcessing)
    state Idle {
        [*] --> Idle
    }
    state Initializing {
        [*] --> Initializing
    }
    state ProcessingInput {
        [*] --> ProcessingInput
    }
    state ProcessingBot {
        [*] --> ProcessingBot
    }
    state Playing {
        [*] --> Playing
    }
```

- **Solid arrows**: Normal transitions.
- **Dashed arrows**: Interruptions/barge-in (with abort).
- **abort()**: Transition is aborted, returns to Idle or next valid state.

---

## Implementation Steps

1. **Mutex Integration**
   - Wrap all transition logic in a mutex lock.
2. **Second Check**
   - After acquiring the mutex, compare current and requested phases.
3. **Transition Queue**
   - Implement a FIFO queue for pending transitions.
4. **Prioritization**
   - Allow urgent transitions to preempt the queue or current transition.
5. **Abort Logic**
   - Define abortable transitions and implement safe abortion.
6. **Testing**
   - Simulate concurrent, queued, and barge-in transitions.

---

## Example Pseudocode

```typescript
async function requestTransition(requestedPhase) {
  await mutex.lock();
  try {
    if (currentPhase !== requestedPhase) {
      // Second check: decide to queue, prioritize, or abort
      handleTransitionDecision(currentPhase, requestedPhase);
    } else {
      performTransition(requestedPhase);
    }
  } finally {
    mutex.unlock();
  }
}
```

---

## Testing Strategy

- **Unit Tests**: For mutex logic, queueing, prioritization, and aborts.
- **Integration Tests**: Simulate real-world transition sequences, including barge-in.
- **Edge Cases**: Rapid-fire requests, simultaneous barge-ins, aborts during transitions.

---

## Dependencies & Integration

- Mutex implementation (e.g., async-mutex or custom).
- State machine core logic.
- Event emitter for transition events.
- Logging for transition attempts, successes, aborts.

---

## Summary

This plan provides a robust, maintainable approach to mutex-based state machine transition control, supporting safe queuing, prioritization, and abortion of transitions. The "second check" mechanism ensures correctness even under concurrent or rapid transition requests.
