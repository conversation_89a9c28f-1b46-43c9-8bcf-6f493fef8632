# Genesys Cloud Setup Guide

This document provides instructions for setting up your AudioConnector server with Genesys Cloud.

## Prerequisites

Before you begin, ensure you have:

- A running instance of the AudioConnector server
- A Microsoft DevTunnel or other tunneling solution exposing your server to the internet
- Access to a Genesys Cloud organization with appropriate permissions

## Setting Up Microsoft DevTunnel

Microsoft DevTunnel provides a secure way to expose your local development server to the internet, which is necessary for Genesys Cloud to communicate with your AudioConnector server.

### Installing DevTunnel CLI

```bash
# Install DevTunnel CLI globally
npm install -g @microsoft/dev-tunnels
```

### Creating a Persistent Tunnel

```bash
# Create a persistent tunnel (you only need to do this once)
devtunnel create --allow-anonymous

# Note the tunnel ID and URL that are displayed
```

### Hosting Your Tunnel

```bash
# Host the tunnel and point to your local server
devtunnel host --port 8080

# The output will show the public URL for your tunnel
# Example: https://12345abcde.usw2.devtunnels.ms
```

Keep this terminal window open while you're using the tunnel.

## Configuring Genesys Cloud

### Creating an AudioConnector Integration

1. Log in to your Genesys Cloud organization
2. Navigate to **Admin** > **Integrations**
3. Click **Add Integration**
4. Search for and select **AudioConnector**
5. Configure the integration:
   - **Name**: Give your integration a descriptive name
   - **WebSocket URI**: Enter your DevTunnel URL (e.g., `wss://12345abcde.usw2.devtunnels.ms`)
   - **Authentication**: Configure as needed for your implementation

### Setting Up a Flow

1. Navigate to **Admin** > **Architect**
2. Create or edit a flow
3. Add an **AudioConnector** action to your flow
4. Configure the action to use your AudioConnector integration
5. Set up any additional flow logic as needed

## Testing the Connection

1. Ensure your AudioConnector server is running locally
2. Ensure your DevTunnel is active and hosting
3. Make a test call to your flow
4. Check the logs in your AudioConnector server to verify the connection

## Troubleshooting

### Common Issues

- **Connection Refused**: Ensure your server is running and the DevTunnel is properly configured
- **Authentication Errors**: Verify your authentication settings in both the server and Genesys Cloud
- **WebSocket Errors**: Check that your DevTunnel URL is using the `wss://` protocol

### Debugging

- Check the logs in your AudioConnector server for detailed error messages
- Use the Genesys Cloud Developer Tools to inspect the WebSocket connection
- Verify that your DevTunnel is properly forwarding traffic to your local server

## Additional Resources

- [Genesys Cloud AudioConnector Documentation](https://developer.genesys.cloud/devapps/audiohook/)
- [Microsoft DevTunnel Documentation](https://learn.microsoft.com/en-us/azure/developer/dev-tunnels/overview)
