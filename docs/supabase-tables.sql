-- Create chat history table
CREATE TABLE IF NOT EXISTS chat_history (
    id SERIAL PRIMARY KEY,
    ani VARCHAR(20) NOT NULL,           -- Caller's phone number
    conversation_id TEXT NOT NULL,       -- Conversation identifier
    message_text TEXT NOT NULL,          -- Actual message content
    is_bot BOOLEAN NOT NULL,            -- true for bot, false for customer
    created_at TIMESTAMPTZ DEFAULT NOW() -- Message timestamp
);

-- Create index for efficient querying by ANI and conversation_id
CREATE INDEX IF NOT EXISTS idx_chat_history_ani_conv 
ON chat_history(ani, conversation_id);

-- Create conversation summaries table
CREATE TABLE IF NOT EXISTS conversation_summaries (
    id SERIAL PRIMARY KEY,
    ani VARCHAR(20) NOT NULL,           -- Caller's phone number
    conversation_id TEXT NOT NULL,       -- Conversation identifier
    start_time TIMESTAMPTZ NOT NULL,    -- Conversation start time
    end_time TIMESTAMPTZ NOT NULL,      -- Conversation end time
    summary_text TEXT NOT NULL,          -- Summarized conversation in Czech
    message_count INT NOT NULL,         -- Total number of messages
    duration_seconds INT NOT NULL,       -- Conversation duration
    created_at TIMESTAMPTZ DEFAULT NOW() -- Summary creation timestamp
);

-- Create index for efficient querying by ANI
CREATE INDEX IF NOT EXISTS idx_summaries_ani 
ON conversation_summaries(ani);