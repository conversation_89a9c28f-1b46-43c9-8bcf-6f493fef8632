# Azure Bot MCP Integration Plan

## Current Status Analysis

### MCP Server Implementation

- ChatContextMcpServer provides two MCP tools:
  - `getUserName`: Gets a user's name based on their ANI
  - `saveUserName`: Saves a user's name associated with their ANI

### Azure Bot Service

- Uses Azure OpenAI service for chat completions
- No tool calling capabilities currently enabled
- Lacks integration with MCP tools

## Required Changes

```mermaid
graph TD
    A[Azure Bot Service] --> B[Enable Tool Calling]
    B --> C[Integrate MCP Tools]
    C --> D[Configure Tool Definitions]
    D --> E[Handle Tool Calls]

    subgraph "MCP Integration"
        C --> F[getUserName Tool]
        C --> G[saveUserName Tool]
    end

    subgraph "Azure OpenAI Config"
        B --> H[Update API Version]
        B --> I[Add Tools Parameter]
    end
```

## Implementation Plan

### 1. Update Azure OpenAI Integration

#### API Configuration

- Update API version to support tool calling
- Add tools parameter to chat completion requests
- Implement tool call handling logic

#### Tool Definitions

```typescript
const tools = [
  {
    type: 'function',
    function: {
      name: 'getUserName',
      description: 'Get user name for given ani',
      parameters: {
        type: 'object',
        properties: {
          ani: {
            type: 'string',
            description: 'ANI to get name for',
          },
        },
        required: ['ani'],
      },
    },
  },
  {
    type: 'function',
    function: {
      name: 'saveUserName',
      description: 'Save user name for given ani',
      parameters: {
        type: 'object',
        properties: {
          ani: {
            type: 'string',
            description: 'ANI of the user',
          },
          name: {
            type: 'string',
            description: 'Name to save for the user',
          },
        },
        required: ['ani', 'name'],
      },
    },
  },
];
```

### 2. Update Bot Service Implementation

#### Changes in getOpenAIResponse Method

```typescript
private async getOpenAIResponse(messages: ChatMessage[]): Promise<string> {
  // ... existing initialization code ...

  const response = await axios.post(
    url,
    {
      messages,
      tools, // Add tool definitions
      tool_choice: "auto", // Enable automatic tool selection
      temperature: 0.7,
      // ... other parameters ...
    },
    // ... config options ...
  );

  // Handle tool calls in response
  const responseMessage = response.data.choices[0].message;
  if (responseMessage.tool_calls) {
    for (const toolCall of responseMessage.tool_calls) {
      const result = await this.handleToolCall(toolCall);
      // Add tool result to conversation
      messages.push({
        role: "tool",
        tool_call_id: toolCall.id,
        name: toolCall.function.name,
        content: result
      });
    }
    // Get final response with tool results
    return this.getOpenAIResponse(messages);
  }

  return responseMessage.content;
}
```

#### Add Tool Call Handler

```typescript
private async handleToolCall(toolCall: {
  id: string;
  type: "function";
  function: {
    name: string;
    arguments: string;
  }
}): Promise<string> {
  const args = JSON.parse(toolCall.function.arguments);

  switch(toolCall.function.name) {
    case "getUserName":
      // Call MCP getUserName tool
      return ""; // Implement MCP tool call
    case "saveUserName":
      // Call MCP saveUserName tool
      return ""; // Implement MCP tool call
    default:
      throw new Error(`Unknown tool: ${toolCall.function.name}`);
  }
}
```

### 3. MCP Integration Steps

1. Create HTTP client for MCP server communication
2. Implement tool call to MCP tool mapping
3. Handle MCP tool responses in bot service
4. Add error handling for MCP communication

### 4. Testing Plan

1. Verify tool calling configuration
2. Test individual MCP tool integration
3. Test conversation flow with tool calls
4. Verify error handling and recovery

## Next Steps

1. Switch to code mode for implementation
2. Update bot service with tool calling support
3. Integrate MCP tool communication
4. Add comprehensive testing
