# MCP Server Implementation Plan

## Overview

Simple MVP implementation of MCP server integration that adds user profile storage while reusing existing patterns from InMemoryDatabaseService.

## Implementation Steps

### 1. Extend InMemoryDatabaseService

Add new types and storage for user profiles:

```typescript
// Database record type
interface DbUserProfileRecord {
  ani: string;
  name: string;
  created_at: string;
  updated_at: string;
}

// Application interface type
interface UserProfile {
  ani: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

class InMemoryDatabaseService {
  private static instance: InMemoryDatabaseService;
  private chatHistory: Map<string, DbChatRecord[]> = new Map();
  private summaries: DbSummaryRecord[] = [];
  // New storage for user profiles
  private userProfiles: DbUserProfileRecord[] = [];

  // Add new methods
  async storeUserProfile(profile: UserProfile): Promise<void> {
    try {
      const record: DbUserProfileRecord = {
        ani: profile.ani,
        name: profile.name,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Update existing or add new
      const existingIndex = this.userProfiles.findIndex(p => p.ani === profile.ani);
      if (existingIndex >= 0) {
        this.userProfiles[existingIndex] = record;
      } else {
        this.userProfiles.push(record);
      }

      console.log('[DB] User profile stored successfully');
    } catch (error) {
      console.error('[DB] Error storing user profile:', error);
      throw error;
    }
  }

  async getUserProfile(ani: string): Promise<UserProfile | null> {
    try {
      const record = this.userProfiles.find(p => p.ani === ani);
      if (!record) {
        return null;
      }

      return {
        ani: record.ani,
        name: record.name,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at),
      };
    } catch (error) {
      console.error('[DB] Error fetching user profile:', error);
      throw error;
    }
  }

  // Update getStats to include user profiles
  getStats(): {
    conversationCount: number;
    totalMessages: number;
    summaryCount: number;
    userProfileCount: number;
  } {
    // Existing stats
    let totalMessages = 0;
    this.chatHistory.forEach(messages => {
      totalMessages += messages.length;
    });

    return {
      conversationCount: this.chatHistory.size,
      totalMessages,
      summaryCount: this.summaries.length,
      userProfileCount: this.userProfiles.length,
    };
  }
}
```

### 2. Create MCP Server

Create a new MCP server that integrates with the InMemoryDatabaseService:

```typescript
import { Server } from '@modelcontextprotocol/sdk/server';
import { InMemoryDatabaseService } from './in-memory-database-service';

// Define tool schemas
const tools = [
  {
    type: 'function',
    function: {
      name: 'saveUserName',
      description: 'Save user name for given ani',
      parameters: {
        type: 'object',
        properties: {
          ani: {
            type: 'string',
            description: 'ANI of the user',
          },
          name: {
            type: 'string',
            description: 'Name to save for the user',
          },
        },
        required: ['ani', 'name'],
      },
    },
  },
  {
    type: 'function',
    function: {
      name: 'getUserName',
      description: 'Get user name for given ani',
      parameters: {
        type: 'object',
        properties: {
          ani: {
            type: 'string',
            description: 'ANI to get name for',
          },
        },
        required: ['ani'],
      },
    },
  },
];

// Implementation
const dbService = InMemoryDatabaseService.getInstance();

const toolFunctions = {
  saveUserName: async (params: { ani: string; name: string }) => {
    await dbService.storeUserProfile({
      ani: params.ani,
      name: params.name,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
    return {
      success: true,
      message: `Name '${params.name}' saved for ANI ${params.ani}`,
    };
  },

  getUserName: async (params: { ani: string }) => {
    const profile = await dbService.getUserProfile(params.ani);
    return {
      name: profile?.name ?? null,
      exists: profile !== null,
      ani: params.ani,
    };
  },
};

// Server setup
class ChatContextMcpServer {
  private server: Server;
  private dbService: InMemoryDatabaseService;

  constructor() {
    this.server = new Server({
      name: 'chat-context-server',
      version: '0.1.0',
    });
    this.dbService = InMemoryDatabaseService.getInstance();

    this.setupTools();
  }

  private setupTools() {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: tools,
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async request => {
      const toolFunction = toolFunctions[request.params.name];
      if (!toolFunction) {
        throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${request.params.name}`);
      }

      const result = await toolFunction(request.params.arguments);
      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    });
  }

  async start() {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('Chat Context MCP server running on stdio');
  }
}
```

## Testing Steps

1. Test InMemoryDatabaseService extensions:

   - Storing user profiles
   - Retrieving user profiles
   - Updating existing profiles
   - Error handling

2. Test MCP Server functionality:
   - Tool registration
   - Tool execution
   - Error handling
   - Integration with InMemoryDatabaseService

## Usage Example

```typescript
// Save user name
await callTool('saveUserName', {
  ani: '1234567890',
  name: 'John Doe',
});

// Get user name
const result = await callTool('getUserName', {
  ani: '1234567890',
});
// Expected: { name: "John Doe", exists: true, ani: "1234567890" }
```

## Next Steps

1. Implement InMemoryDatabaseService extensions
2. Create and test MCP server
3. Integrate with existing services
4. Add error handling and logging
5. Test functionality
