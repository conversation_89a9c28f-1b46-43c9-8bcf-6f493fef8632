# Tooling should contain

- Save user name by ID
- Get user name by ID
- get summary of last conversation with finish datetime

# MCP server MVP proposal

const express = require('express');
const bodyParser = require('body-parser');

// In-memory storage
const userProfiles = new Map();
const chatHistory = new Map();

// Express aplikace
const app = express();
app.use(bodyParser.json());

// Middleware pro logování
app.use((req, res, next) => {
console.log(`${req.method} ${req.url}`);
next();
});

// Definice nástrojů pro LLM
const tools = [
{
type: "function",
function: {
name: "saveUserName",
description: "Uloží jméno uživatele do systému podle jeho ID",
parameters: {
type: "object",
properties: {
userId: {
type: "string",
description: "ID uživatele"
},
name: {
type: "string",
description: "<PERSON><PERSON><PERSON> uživatele, které chceme ulo<PERSON>it"
}
},
required: ["userId", "name"]
}
}
},
{
type: "function",
function: {
name: "getUserName",
description: "Získá jméno uživatele podle jeho ID",
parameters: {
type: "object",
properties: {
userId: {
type: "string",
description: "ID uživatele, jehož jméno chceme získat"
}
},
required: ["userId"]
}
}
},
{
type: "function",
function: {
name: "getLastChatSummary",
description: "Získá shrnutí posledního chatu kromě aktuálního",
parameters: {
type: "object",
properties: {
currentChatId: {
type: "string",
description: "ID aktuálního chatu (tento bude vynechán při hledání)"
},
userId: {
type: "string",
description: "ID uživatele, jehož historii chatů prohledáváme"
}
},
required: ["currentChatId", "userId"]
}
}
},
{
type: "function",
function: {
name: "saveChatSummary",
description: "Uloží shrnutí aktuálního chatu",
parameters: {
type: "object",
properties: {
chatId: {
type: "string",
description: "ID chatu"
},
userId: {
type: "string",
description: "ID uživatele"
},
summary: {
type: "string",
description: "Shrnutí obsahu chatu"
}
},
required: ["chatId", "userId", "summary"]
}
}
}
];

// Implementace funkcí nástrojů
const toolFunctions = {
saveUserName: (params) => {
const { userId, name } = params;

    userProfiles.set(userId, name);

    return {
      success: true,
      message: `Jméno '${name}' bylo uloženo pro uživatele s ID ${userId}`
    };

},

getUserName: (params) => {
const { userId } = params;

    const name = userProfiles.get(userId) || null;

    return {
      name,
      exists: name !== null,
      userId
    };

},

getLastChatSummary: (params) => {
const { currentChatId, userId } = params;

    // Získání všech chatů daného uživatele
    const userChats = [];
    chatHistory.forEach((chatData, chatId) => {
      if (chatData.userId === userId && chatId !== currentChatId) {
        userChats.push({
          chatId,
          timestamp: chatData.timestamp,
          summary: chatData.summary
        });
      }
    });

    // Seřazení podle času (nejnovější první)
    userChats.sort((a, b) => b.timestamp - a.timestamp);

    // Výběr posledního chatu (pokud existuje)
    const lastChat = userChats.length > 0 ? userChats[0] : null;

    if (lastChat) {
      // Formátování data a času
      const formattedDate = new Date(lastChat.timestamp).toLocaleString('cs-CZ');

      return {
        success: true,
        chatId: lastChat.chatId,
        summary: lastChat.summary,
        timestamp: formattedDate,
        originalTimestamp: lastChat.timestamp
      };
    } else {
      return {
        success: false,
        message: "Žádný předchozí chat nebyl nalezen"
      };
    }

},

saveChatSummary: (params) => {
const { chatId, userId, summary } = params;

    chatHistory.set(chatId, {
      userId,
      timestamp: Date.now(),
      summary
    });

    return {
      success: true,
      message: "Shrnutí chatu bylo úspěšně uloženo",
      chatId,
      timestamp: new Date().toLocaleString('cs-CZ')
    };

}
};

// Endpoint pro volání funkcí nástrojů
app.post('/api/tools', (req, res) => {
const { tool, params } = req.body;

if (!toolFunctions[tool]) {
return res.status(400).json({ error: `Nástroj '${tool}' neexistuje` });
}

try {
const result = toolFunctions[tool](params);
res.json(result);
} catch (error) {
res.status(500).json({ error: error.message });
}
});

// Endpoint pro získání všech nástrojů
app.get('/api/tools', (req, res) => {
res.json({ tools });
});

// Spuštění serveru
const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
console.log(`MCP server běží na portu ${PORT}`);
});

# Integration proposal

const express = require('express');
const { v4: uuidv4 } = require('uuid');
const bodyParser = require('body-parser');
const axios = require('axios');
const { OpenAIClient, AzureKeyCredential } = require("@azure/openai");
require('dotenv').config();

// Konfigurace Azure OpenAI
const azureOpenAIEndpoint = process.env.AZURE_OPENAI_ENDPOINT;
const azureOpenAIKey = process.env.AZURE_OPENAI_KEY;
const azureOpenAIDeploymentName = process.env.AZURE_OPENAI_DEPLOYMENT_NAME;

// Inicializace Azure OpenAI klienta
const openAIClient = new OpenAIClient(
azureOpenAIEndpoint,
new AzureKeyCredential(azureOpenAIKey)
);

// URL MCP serveru
const mcpServerUrl = 'http://localhost:3000';

// Express aplikace
const app = express();
app.use(bodyParser.json());

// Middleware pro vytvoření chat ID
app.use((req, res, next) => {
if (!req.headers['x-chat-id']) {
req.headers['x-chat-id'] = uuidv4();
}
next();
});

// Získání dostupných nástrojů z MCP serveru
let availableTools = [];

async function fetchTools() {
try {
const response = await axios.get(`${mcpServerUrl}/api/tools`);
availableTools = response.data.tools;
console.log('Nástroje MCP serveru byly úspěšně načteny');
} catch (error) {
console.error('Chyba při načítání nástrojů z MCP serveru:', error);
// Použití základních nástrojů v případě chyby
availableTools = [
{
type: "function",
function: {
name: "saveUserName",
description: "Uloží jméno uživatele do systému podle jeho ID",
parameters: {
type: "object",
properties: {
userId: { type: "string" },
name: { type: "string" }
},
required: ["userId", "name"]
}
}
},
{
type: "function",
function: {
name: "getUserName",
description: "Získá jméno uživatele podle jeho ID",
parameters: {
type: "object",
properties: {
userId: { type: "string" }
},
required: ["userId"]
}
}
},
{
type: "function",
function: {
name: "getLastChatSummary",
description: "Získá shrnutí posledního chatu kromě aktuálního",
parameters: {
type: "object",
properties: {
currentChatId: { type: "string" },
userId: { type: "string" }
},
required: ["currentChatId", "userId"]
}
}
},
{
type: "function",
function: {
name: "saveChatSummary",
description: "Uloží shrnutí aktuálního chatu",
parameters: {
type: "object",
properties: {
chatId: { type: "string" },
userId: { type: "string" },
summary: { type: "string" }
},
required: ["chatId", "userId", "summary"]
}
}
}
];
}
}

// Načíst nástroje při startu serveru
fetchTools();

// Funkce pro volání nástroje
async function callTool(tool, params) {
try {
const response = await axios.post(`${mcpServerUrl}/api/tools`, {
tool,
params
});
return response.data;
} catch (error) {
console.error(`Chyba při volání nástroje ${tool}:`, error);
throw error;
}
}

// Endpoint pro chat
app.post('/api/chat', async (req, res) => {
try {
const { message, userId } = req.body;
const chatId = req.headers['x-chat-id'] || uuidv4();

    // Systémový prompt s instrukcemi
    const systemPrompt = `
    Jsi asistent, který má přístup k následujícím nástrojům pro uchování kontextu o uživateli:

    1. getUserName - pro získání jména uživatele, pokud ho již známe
    2. saveUserName - pro uložení jména uživatele, pokud se představí
    3. getLastChatSummary - pro zjištění, kdy a o čem byl poslední chat s tímto uživatelem
    4. saveChatSummary - pro uložení shrnutí aktuálního chatu

    Při konverzaci s uživatelem:
    - Na začátku použij getUserName, abys zjistil, zda znáš jméno uživatele
    - Pokud uživatel zmíní své jméno (např. "Jmenuji se Jan", "Jsem Petr", apod.), použij saveUserName
    - Když se uživatel zeptá na předchozí konverzaci, použij getLastChatSummary
    - Na konci konverzace použij saveChatSummary pro uložení shrnutí tohoto chatu

    Aktuální Chat ID: ${chatId}
    Uživatelské ID: ${userId}
    `;

    // Vytvoření zpráv pro konverzaci
    const messages = [
      { role: "system", content: systemPrompt },
      { role: "user", content: message }
    ];

    // Tvorba odpovědi s nástroji
    let responseComplete = false;
    let finalResponse = '';

    while (!responseComplete) {
      // Volání Azure OpenAI
      const response = await openAIClient.getChatCompletions(
        azureOpenAIDeploymentName,
        messages,
        {
          temperature: 0.7,
          maxTokens: 800,
          tools: availableTools,
          toolChoice: "auto"
        }
      );

      const responseMessage = response.choices[0].message;

      // Kontrola, zda je potřeba volat nástroj
      if (responseMessage.toolCalls && responseMessage.toolCalls.length > 0) {
        // Zpracování volání nástrojů
        for (const toolCall of responseMessage.toolCalls) {
          const functionName = toolCall.function.name;
          const functionArgs = JSON.parse(toolCall.function.arguments);

          // Doplnění chybějících parametrů
          if (!functionArgs.userId && (functionName === 'getUserName' ||
                                       functionName === 'saveUserName' ||
                                       functionName === 'getLastChatSummary' ||
                                       functionName === 'saveChatSummary')) {
            functionArgs.userId = userId;
          }

          if (functionName === 'getLastChatSummary' && !functionArgs.currentChatId) {
            functionArgs.currentChatId = chatId;
          }

          if (functionName === 'saveChatSummary' && !functionArgs.chatId) {
            functionArgs.chatId = chatId;
          }

          console.log(`Volání nástroje: ${functionName}`, functionArgs);

          // Volání nástroje
          const toolResponse = await callTool(functionName, functionArgs);

          // Přidání výsledku do zpráv
          messages.push({
            role: "assistant",
            tool_calls: [
              {
                id: toolCall.id,
                type: "function",
                function: {
                  name: functionName,
                  arguments: JSON.stringify(functionArgs)
                }
              }
            ]
          });

          messages.push({
            role: "tool",
            tool_call_id: toolCall.id,
            content: JSON.stringify(toolResponse)
          });
        }
      } else {
        // Normální odpověď bez volání nástroje
        finalResponse = responseMessage.content;
        responseComplete = true;

        // Přidání odpovědi do historie zpráv
        messages.push(responseMessage);

        // Automatické uložení shrnutí chatu
        try {
          // Vytvoření shrnutí z konverzace
          const userMessage = message.length > 100 ? message.substring(0, 100) + "..." : message;
          const summary = `Uživatel se ptal: "${
