# Folder Structure Proposal

## Current Structure Analysis

The current folder structure has several strengths but also some areas for improvement:

### Strengths

- Services are generally organized by feature in the `src/services` directory
- Common utilities and types are separated appropriately
- Test files are collocated with the code they test

### Areas for Improvement

- Some core session functionality is split between `src/common` and `src/session`
- Some services have their documentation in `src/common` rather than with the service
- Some services are still using legacy file locations (e.g., `src/services/dtmf-service.ts`)
- The `src/common` directory contains a mix of different concerns

## Proposed Structure

Based on the analysis, here's a proposed folder structure that improves organization while maintaining the strengths of the current approach:

```
src/
├── session/                  # All session-related functionality
│   ├── session.ts            # Main session class
│   ├── session-service.md    # Session documentation
│   ├── session-state-manager.ts # State management
│   ├── websocket-controller.ts # WebSocket communication
│   └── __tests__/            # Session tests
│
├── services/                 # Service modules
│   ├── asr-service/          # Automatic Speech Recognition
│   │   ├── index.ts
│   │   ├── asr-service.md    # ASR documentation
│   │   ├── base-speech-service.ts
│   │   ├── azure-speech-service.ts
│   │   ├── google-speech-adapter.ts
│   │   ├── types.ts
│   │   └── __tests__/
│   │
│   ├── audio/                # Audio processing
│   │   ├── index.ts
│   │   ├── audio-service.md  # Audio documentation
│   │   ├── audio-manager.ts
│   │   ├── audio-service-adapter.ts
│   │   └── __tests__/
│   │
│   ├── barge-in/             # Barge-in detection
│   │   ├── index.ts
│   │   ├── barge-in-service.md # Barge-in documentation
│   │   ├── barge-in-manager.ts
│   │   └── __tests__/
│   │
│   ├── bot-service/          # Bot and LLM integration
│   │   ├── index.ts
│   │   ├── bot-service.md    # Bot documentation
│   │   ├── bot-service.ts
│   │   ├── bot-resource.ts
│   │   ├── bot-response.ts
│   │   ├── bot-service-adapter.ts
│   │   ├── providers/        # LLM providers
│   │   ├── types/            # Bot-related types
│   │   └── __tests__/
│   │
│   ├── dtmf/                 # DTMF processing
│   │   ├── index.ts
│   │   ├── dtmf-service.md   # DTMF documentation
│   │   ├── dtmf-manager.ts
│   │   ├── dtmf-service.ts   # Move from src/services/dtmf-service.ts
│   │   └── __tests__/
│   │
│   ├── monitoring/           # Logging and metrics
│   │   ├── index.ts
│   │   ├── monitoring-service.md # Monitoring documentation
│   │   ├── performance-logger.ts
│   │   ├── session-metrics-adapter.ts
│   │   ├── logging.ts
│   │   ├── logging-config.ts
│   │   └── __tests__/
│   │
│   ├── speech/               # Speech services (TTS)
│   │   ├── index.ts
│   │   ├── speech-service.md # Speech documentation
│   │   ├── base/             # Base classes
│   │   ├── azure/            # Azure implementation
│   │   ├── google/           # Google implementation
│   │   ├── elevenlabs/       # ElevenLabs implementation
│   │   ├── factories/        # Service factories
│   │   └── __tests__/
│   │
│   └── transcript/           # Transcript processing
│       ├── index.ts
│       ├── transcript-service.md # Transcript documentation
│       ├── transcript-processor.ts
│       └── __tests__/
│
├── protocol/                 # Protocol definitions
│   ├── core.ts
│   ├── message.ts
│   └── voice-bots.ts
│
├── types/                    # Shared type definitions
│   ├── index.ts
│   └── chat.ts
│
├── utils/                    # Utility functions
│   ├── index.ts
│   └── ...
│
├── websocket/                # WebSocket handling
│   ├── server.ts
│   ├── message-handlers/
│   ├── middleware/
│   └── routes/
│
└── index.ts                  # Application entry point
```

## Key Changes

1. **Session Directory Structure** (COMPLETED):

   - ✅ Session functionality is now in `src/session/session.ts`
   - ✅ State management is in `src/session/session-state-manager.ts`
   - ✅ Session documentation is in `src/session/session-service.md`
   - ✅ Legacy session files have been removed and archived

2. **Consolidate DTMF Service**:

   - Move `src/services/dtmf-service.ts` to `src/services/dtmf/dtmf-service.ts`
   - Create `src/services/dtmf/index.ts` to export all DTMF functionality

3. **Add Documentation to Each Service**:

   - Add service-specific .md files in each service directory
   - Ensure documentation follows a consistent format

4. **Create Index Files for Each Service**:
   - Add index.ts files to export the public API of each service
   - Simplify imports from other parts of the application

## Benefits

1. **Improved Cohesion**: Related functionality is grouped together
2. **Better Discoverability**: Documentation is collocated with the code it describes
3. **Clearer Dependencies**: Service boundaries are more explicit
4. **Easier Maintenance**: Each service can be maintained independently
5. **Simplified Imports**: Index files provide a clean public API for each service
6. **Consistent Structure**: All services follow the same organizational pattern

## Implementation Strategy

1. **Phase 1**: Create documentation files for each service
2. **Phase 2**: Move session-related files to the session directory
3. **Phase 3**: Consolidate DTMF service files
4. **Phase 4**: Create index files for each service
5. **Phase 5**: Update imports throughout the codebase

This phased approach minimizes risk and allows for incremental improvements to the codebase structure.
