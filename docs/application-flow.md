# Application Flow: From Session Setup to ASR-LLM-TTS Turns

This document provides a visual representation of the application flow from session setup through the ASR-LLM-TTS conversation turns. It includes sequence diagrams, state machine diagrams, and combined flow diagrams to help understand the system from different perspectives.

**Update:** The state machine now includes an explicit `LISTENING` state, which acts as the asynchronous boundary for ASR (speechToText) phase management. All user input and barge-in transitions now route through `LISTENING`, ensuring robust, idempotent context handling and clear phase metrics.

## 1. Sequence Diagram

This diagram shows the temporal flow of events and interactions between components during a typical conversation turn:

```mermaid
sequenceDiagram
    participant Client
    participant Session
    participant ASR
    participant Bot
    participant TTS
    participant StateManager

    %% Session Initialization
    Client->>Session: Connect
    Session->>StateManager: Initialize
    StateManager->>StateManager: INITIALIZING → IDLE

    %% User Input Processing (ASR Phase managed by StateManager)
    Client->>Session: Audio data
    Session->>StateManager: USER_INPUT_RECEIVED
    StateManager->>StateManager: IDLE → LISTENING
    Note over StateManager: Start 'speechToText' phase at LISTENING
    Session->>ASR: Start ASR
    ASR->>Session: Interim transcripts
    ASR->>Session: Final transcript
    Session->>StateManager: FINAL_TRANSCRIPT_READY
    StateManager->>StateManager: LISTENING → PROCESSING_INPUT
    Note over StateManager: End 'speechToText' phase at exit from LISTENING

    %% LLM Phase
    Note over StateManager: Start 'llmProcessing' phase
    StateManager->>Session: Begin LLM processing
    Session->>Bot: Process user input
    Bot->>Session: Bot response
    Note over StateManager: End 'llmProcessing' phase
    Session->>StateManager: BOT_RESPONSE_RECEIVED
    StateManager->>StateManager: PROCESSING_BOT → RESPONDING

    %% TTS Phase
    Note over StateManager: Start 'textToSpeech' phase
    Session->>TTS: Convert text to speech
    TTS->>Session: Audio data
    Session->>StateManager: RESPONSE_PREPARATION_COMPLETED
    StateManager->>StateManager: RESPONDING → PLAYING
    Session->>Client: Play audio
    Client->>Session: Playback completed
    Note over StateManager: End 'textToSpeech' phase
    Session->>StateManager: PLAYBACK_COMPLETED
    StateManager->>StateManager: PLAYING → IDLE

    %% Barge-In Scenario
    Note over Client,StateManager: Barge-In Scenario
    Client->>Session: Audio data (during playback)
    Session->>StateManager: BARGE_IN_DETECTED
    StateManager->>StateManager: PLAYING → LISTENING
    Note over StateManager: Start 'speechToText' phase at LISTENING (barge-in)
    Session->>ASR: Start ASR
    ASR->>Session: Interim/final transcript
    Session->>StateManager: FINAL_TRANSCRIPT_READY
    StateManager->>StateManager: LISTENING → PROCESSING_INPUT
    Note over StateManager: End 'speechToText' phase at exit from LISTENING
    Session->>Client: Stop playback
```

## 2. State Machine Diagram

This diagram focuses on the state transitions during the ASR-LLM-TTS flow, with annotations for metrics tracking phases:

```mermaid
stateDiagram-v2
    [*] --> IDLE

    %% Normal Flow with Metrics (All phase management in StateManager)
    IDLE --> LISTENING: USER_INPUT_RECEIVED
    note right of LISTENING: Start 'speechToText' phase (except initial greeting)
    LISTENING --> PROCESSING_INPUT: FINAL_TRANSCRIPT_READY
    note right of LISTENING: End 'speechToText' phase, Start 'llmProcessing' phase
    PROCESSING_INPUT --> PROCESSING_BOT: USER_INPUT_PROCESSED
    PROCESSING_BOT --> RESPONDING: BOT_RESPONSE_RECEIVED
    note right of PROCESSING_BOT: End 'llmProcessing' phase, Start 'textToSpeech' phase
    RESPONDING --> PLAYING: RESPONSE_PREPARATION_COMPLETED
    PLAYING --> IDLE: PLAYBACK_COMPLETED
    note right of IDLE: End 'textToSpeech' phase

    %% Barge-In Flow
    PLAYING --> LISTENING: BARGE_IN_DETECTED
    RESPONDING --> LISTENING: BARGE_IN_DETECTED
```

## 3. Combined Flow Diagram

This diagram combines elements of both sequence and state diagrams to show both the component interactions and state transitions in a single view:

```mermaid
flowchart TD
    %% States
    IDLE[IDLE State]
    LISTENING[LISTENING State]
    PROC_IN[PROCESSING_INPUT State]
    PROC_BOT[PROCESSING_BOT State]
    RESP[RESPONDING State]
    PLAY[PLAYING State]

    %% Components
    Client[Client]
    ASR[ASR Service]
    Bot[Bot Service]
    TTS[TTS Service]
    StateManager[StateManager]

    %% Normal Flow
    IDLE -->|USER_INPUT_RECEIVED| LISTENING
    StateManager -.->|Start 'speechToText' phase| LISTENING
    Client -->|Audio Data| ASR
    ASR -->|Process Audio| LISTENING

    %% ASR Phase (Managed by StateManager)
    subgraph "ASR Phase (speechToText, managed by StateManager)"
        LISTENING -->|Transcription| ASR_OUT[Final Transcript]
    end
    StateManager -.->|End 'speechToText' phase, Start 'llmProcessing' phase| PROC_IN

    ASR_OUT -->|FINAL_TRANSCRIPT_READY| PROC_IN

    %% LLM Phase (Managed by StateManager)
    subgraph "LLM Phase (llmProcessing, managed by StateManager)"
        PROC_IN -->|Process Text| Bot
        Bot -->|Generate Response| BOT_OUT[Bot Response]
    end
    StateManager -.->|End 'llmProcessing' phase, Start 'textToSpeech' phase| PROC_BOT

    BOT_OUT -->|USER_INPUT_PROCESSED| PROC_BOT

    %% TTS Phase (textToSpeech, managed by StateManager)
    subgraph "TTS Phase (textToSpeech, managed by StateManager)"
        PROC_BOT -->|Convert Text to Speech| TTS
        TTS -->|Generate Audio| TTS_OUT[Audio Data]
        TTS_OUT -->|RESPONSE_PREPARATION_COMPLETED| RESP
        RESP -->|Play Audio| Client
    end
    StateManager -.->|End 'textToSpeech' phase| IDLE

    RESP -->|PLAYBACK_COMPLETED| IDLE

    %% Barge-In Flow
    Client -->|Audio During Playback| BARGE[Barge-In Detection]
    BARGE -->|BARGE_IN_DETECTED| LISTENING
    PLAY -->|Stop Playback| BARGE
```
> **Note:** For details on limitations and best practices in phase metrics tracking, see [performance-metrics.md](../src/services/monitoring/performance-metrics.md#limitations-edge-cases-and-enforcement).

## Key Aspects of the Flow

1. **Event-Driven Architecture**: The system uses events to trigger state transitions and component interactions.

2. **State Machine Core**: The state machine manages the flow through well-defined states (IDLE, LISTENING, PROCESSING_INPUT, PROCESSING_BOT, RESPONDING, PLAYING).

3. **Centralized Phase Management**: All phase management (`speechToText`, `llmProcessing`, `textToSpeech`) is handled exclusively in the state machine. The `LISTENING` state is the explicit async boundary for ASR, ensuring robust, idempotent context handling and clear phase metrics.

4. **Metrics Tracking**: The system tracks three key phases, all managed by the state machine:
   - `speechToText`: Started on entry to `LISTENING` (except for the initial greeting), ended on exit from `LISTENING`.
   - `llmProcessing`: From transcript to bot response.
   - `textToSpeech`: From bot response to audio playback completion.

5. **Barge-In Handling**: The system can detect and handle user interruptions during playback or response, transitioning from PLAYING or RESPONDING to LISTENING, then to PROCESSING_INPUT after ASR completes.

6. **Component Interaction**: Components communicate through events rather than direct method calls, creating a decoupled architecture.

## Related Documentation

For more detailed information, see:
- [State Machine Documentation](../src/session/state-machine.md)
- [Session Service Documentation](../src/session/session-service.md)
- [Barge-In Documentation](./barge-in.md)
