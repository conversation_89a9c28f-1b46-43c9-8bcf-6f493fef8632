# Barge-in and Stable Stream Simplification Plan

## Overview

This plan describes the unification and simplification of barge-in and stable stream logic, as proposed in the recent review. The goal is to improve maintainability, clarity, and extensibility by introducing a single `BargeInManager` responsible for both audio and DTMF barge-in detection, centralizing playback state, and using a minimal, callback-based interface.

---

## 1. Unified Manager: `BargeInManager`

### Responsibilities

- Detect barge-in events from both audio (speech) and DTMF sources.
- Centralize and expose playback state (`playing`, `paused`, `stopped`).
- Provide a simple callback-based API for barge-in, pause detection, and playback state changes.
- Manage configuration for barge-in and stable stream features.
- Decouple barge-in logic from session and playback handlers.

### Example Interface

```typescript
// src/services/barge-in/barge-in-manager.ts
type PlaybackState = 'playing' | 'paused' | 'stopped';

interface BargeInManagerOptions {
  onBargeIn: (source: 'speech' | 'dtmf', input: string) => void;
  onPauseDetected: (transcript: Transcript) => void;
  onPlaybackStateChange?: (state: PlaybackState) => void;
}

export class BargeInManager {
  constructor(options: BargeInManagerOptions) {
    /* ... */
  }
  setPlaybackState(state: PlaybackState): void {
    /* ... */
  }
  processAudio(data: Uint8Array): void {
    /* ... */
  }
  processDTMF(digit: string): void {
    /* ... */
  }
}
```

---

## 2. Centralized Playback State

- Playback state (`playing`, `paused`, `stopped`) is managed by `BargeInManager`.
- All components (audio, DTMF, session) interact with playback state via the manager.
- No direct flag manipulation outside the manager.

### Example

```typescript
// Set playback state
bargeInManager.setPlaybackState('playing');

// Optionally handle state changes
bargeInManagerOptions.onPlaybackStateChange = state => {
  /* ... */
};
```

---

## 3. Minimal Callback-Based API

- All barge-in, pause detection, and playback state changes are handled via explicit callbacks.
- No event bus or observer pattern is used unless future extensibility is required.
- Consumers (session, metrics, etc.) provide callbacks when constructing the manager.

### Example

```typescript
const bargeInManager = new BargeInManager({
  onBargeIn: (source, input) => {
    /* handle barge-in */
  },
  onPauseDetected: transcript => {
    /* handle pause detection */
  },
  onPlaybackStateChange: state => {
    /* handle playback state change */
  },
});
```

---

## 4. Configuration Rationalization

### Retained Options (with defaults)

| Option                        | Default    | Description                                  |
| ----------------------------- | ---------- | -------------------------------------------- |
| ENABLE_BARGE_IN               | true       | Enable/disable barge-in                      |
| BARGE_IN_CONFIDENCE_THRESHOLD | 0.6        | Min confidence for speech barge-in           |
| ENABLE_STABLE_STREAM          | true       | Enable pause detection                       |
| PAUSE_THRESHOLD_MS            | 1500       | Pause duration for utterance finalization    |
| ASR_STREAMING_MODE            | 'standard' | ASR mode: 'standard', 'hybrid', 'continuous' |

### Removed Options

- Any per-manager or legacy flags now superseded by the unified manager.
- Debug flags (unless actively used for troubleshooting).

---

## 5. Documentation Update

- Update event/state flow diagrams to reflect the unified manager and callback-based flow.
- Update README and relevant docs to describe the new architecture and configuration.
- Add/replace Mermaid diagrams for clarity.

---

## 6. Progress Checklist

- [x] Create `BargeInManager` class with callback-based API
- [x] Migrate audio and DTMF barge-in logic into `BargeInManager`
- [x] Centralize playback state in `BargeInManager`
- [x] Refactor consumers to use callbacks (remove direct handler attachment)
- [ ] Rationalize configuration options and update usage
- [x] Update documentation (README, diagrams)
- [ ] Add/Update unit and integration tests for new manager
- [x] Remove legacy managers and unused config
- [x] Validate end-to-end barge-in and pause detection

---

## 7. Architecture Diagram

```mermaid
flowchart TD
    subgraph Session
      S1(Session)
    end
    subgraph BargeInManager
      B1(BargeInManager)
    end
    subgraph Inputs
      A1(Audio Input)
      D1(DTMF Input)
    end
    subgraph Outputs
      O1(Bot Service)
      O2(Genesys Cloud)
      O3(Metrics/Logger)
    end

    A1 -- audio --> B1
    D1 -- dtmf --> B1
    S1 -- setPlaybackState --> B1
    B1 -- "onBargeIn callback" --> S1
    B1 -- "onPauseDetected callback" --> S1
    B1 -- "onPlaybackStateChange callback" --> S1
    S1 -- user input --> O1
    B1 -- "barge-in event" --> O2
    B1 -- callbacks --> O3
```

---

## 8. Example Code Snippets

### BargeInManager Skeleton

```typescript
type PlaybackState = 'playing' | 'paused' | 'stopped';

interface BargeInManagerOptions {
  onBargeIn: (source: 'speech' | 'dtmf', input: string) => void;
  onPauseDetected: (transcript: Transcript) => void;
  onPlaybackStateChange?: (state: PlaybackState) => void;
}

export class BargeInManager {
  private playbackState: PlaybackState = 'stopped';
  private options: BargeInManagerOptions;

  constructor(options: BargeInManagerOptions) {
    this.options = options;
  }

  setPlaybackState(state: PlaybackState) {
    this.playbackState = state;
    this.options.onPlaybackStateChange?.(state);
  }

  processAudio(data: Uint8Array) {
    // ...detect barge-in, call this.options.onBargeIn('speech', text) if needed
  }

  processDTMF(digit: string) {
    // ...detect DTMF barge-in, call this.options.onBargeIn('dtmf', digit) if needed
  }
}
```

---

## 9. Testability

- All callback invocations are testable via mock functions.
- Playback state transitions are observable and testable.
- Barge-in detection logic is unit-testable for both audio and DTMF.
- Integration tests should cover end-to-end flows.

---

## 10. File/Module Changes

- `src/services/barge-in/barge-in-manager.ts` (new)
- Remove/replace `audio-manager.ts` and `dtmf-manager.ts`
- Update `session.ts` and consumers to use the new manager
- Update `.env.example` and docs

---

## Summary

This plan proposes a unified, callback-driven `BargeInManager` to handle all barge-in and playback state logic, replacing the current split and callback-based approach. Configuration is rationalized, and all consumers interact via explicit callbacks for maintainability and clarity. The plan includes a checklist, code snippets, and a Mermaid diagram for implementation guidance.

**Location:** `docs/barge-in-simplification.md`
