# Code Style Guide

This project uses <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> to enforce consistent code style and formatting across the codebase.

## Setup

The project is configured with:

- **Prettier**: For consistent code formatting
- **ESLint**: For code quality and style enforcement
- **TypeScript ESLint**: For TypeScript-specific linting rules

## Configuration Files

- `.prettierrc`: Prettier configuration
- `.prettierignore`: Files to exclude from Prettier formatting
- `.eslintrc`: ESLint configuration
- `.eslintignore`: Files to exclude from ESLint
- `.vscode/settings.json`: VS Code editor settings

## Code Style Rules

The project follows these code style rules:

- Use 2 spaces for indentation
- Use single quotes for strings
- Add semicolons at the end of statements
- Limit line length to 100 characters
- Use trailing commas in multi-line objects and arrays
- Use LF line endings

## Commands

The following npm scripts are available:

```bash
# Check code for linting errors
npm run lint

# Fix linting errors automatically where possible
npm run lint:fix

# Format code with Prettier
npm run format

# Check if code is properly formatted
npm run format:check

# Run both linting and format checking
npm run check
```

## VS Code Integration

If you're using VS Code, the project includes settings to:

- Format code on save
- Fix ESLint errors on save
- Use the correct tab size and spacing

Make sure you have the following extensions installed:

- ESLint (`dbaeumer.vscode-eslint`)
- Prettier (`esbenp.prettier-vscode`)

## Pre-commit Hook (Optional)

For even better code quality, consider setting up a pre-commit hook using Husky and lint-staged. This will automatically check and format your code before each commit.

To set this up:

1. Install the required packages:

   ```bash
   npm install --save-dev husky lint-staged
   ```

2. Add the following to your package.json:

   ```json
   {
     "husky": {
       "hooks": {
         "pre-commit": "lint-staged"
       }
     },
     "lint-staged": {
       "*.{js,ts}": ["eslint --fix", "prettier --write"],
       "*.{json,md}": ["prettier --write"]
     }
   }
   ```

3. Initialize Husky:
   ```bash
   npx husky install
   ```

## Troubleshooting

If you encounter any issues with the linting or formatting:

1. Make sure you have the latest dependencies installed:

   ```bash
   npm install
   ```

2. Try resetting your editor's cache:

   - In VS Code: `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac) and run "Developer: Reload Window"

3. Check if there are any conflicts between ESLint and Prettier rules:
   ```bash
   npm run lint
   npm run format:check
   ```
