# Build stage
FROM node:20-slim AS builder

WORKDIR /app

# Install build tools
RUN apt-get update && apt-get install -y \
    build-essential \
    python3

# Copy package files
COPY package*.json ./
COPY .npmrc ./

# Install dependencies
RUN npm ci

# Copy source code
COPY tsconfig.json ./
COPY tsconfig.build.json ./
COPY src/ ./src/

# Build TypeScript code
RUN npm run build

# Production stage
FROM node:20-slim AS production

WORKDIR /app

# Install build tools
RUN apt-get update && apt-get install -y \
    build-essential \
    python3

# Copy package files and install production dependencies
COPY package*.json ./

COPY system-prompt-azure.txt ./
COPY system-prompt-google.txt ./
COPY google-credentials.json ./
COPY system-prompt-elevenlabs.txt ./
# No app directory exists in the project
# Source code is already built in the builder stage and copied below with COPY --from=builder

COPY .npmrc ./
RUN npm ci --only=production

# Copy built files from builder stage
COPY --from=builder /app/dist ./dist

# Expose port
EXPOSE 8080

# Start the application
CMD ["node", "dist/index.js"]