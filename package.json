{"name": "audioconnector-server-reference-implementation", "private": true, "version": "1.5.1", "description": "VoiceBot Reference Implementation for AudioConnector Servers.", "author": "<PERSON> <<EMAIL>>", "main": "./dist/index.js", "bin": "./dist/index.js", "files": ["dist/**/*", "!dist/**/*.index.js"], "engines": {"node": ">=18"}, "scripts": {"start": "node ./dist/index.js", "clean": "rimraf ./dist/", "prebuild": "node -p \"'export const LIB_VERSION = ' + JSON.stringify(require('./package.json').version) + ';'\" > src/__version.ts", "build": "npm run clean && tsc -p tsconfig.build.json", "dev": "ts-node ./src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:metrics": "jest src/services/monitoring/__tests__", "lint": "eslint . --ext .js,.ts", "lint:fix": "eslint . --ext .js,.ts --fix", "format": "prettier --write \"**/*.{js,ts,json,md}\"", "format:check": "prettier --check \"**/*.{js,ts,json,md}\"", "check": "npm run lint && npm run format:check"}, "devDependencies": {"@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.0", "@semantic-release/exec": "^6.0.3", "@semantic-release/git": "^10.0.1", "@semantic-release/gitlab": "^13.1.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.178", "@types/node": "^18.9.1", "@types/uuid": "^9.0.1", "@types/websocket": "^1.0.5", "@types/ws": "^8.5.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.57.1", "eslint-config-prettier": "^8.10.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "rimraf": "^3.0.2", "ts-jest": "^29.1.0", "ts-node": "^10.4.0", "typescript": "^4.5.4"}, "dependencies": {"@ffmpeg/core": "^0.12.10", "@ffmpeg/ffmpeg": "^0.12.15", "@ffmpeg/util": "^0.12.2", "@google-cloud/speech": "^6.7.1", "@google-cloud/text-to-speech": "^5.8.1", "@modelcontextprotocol/sdk": "^1.12.1", "audiobuffer-to-wav": "^1.0.0", "axios": "^1.6.7", "dotenv": "^16.0.3", "elevenlabs": "^1.52.0", "elevenlabs-node": "^2.0.3", "express": "^4.18.2", "ffmpeg": "^0.0.4", "ffmpeg-static": "^5.2.0", "fluent-ffmpeg": "^2.1.3", "iso8601-duration": "^1.3.0", "lodash": "^4.17.21", "microsoft-cognitiveservices-speech-sdk": "^1.33.0", "mpv": "^2.0.1", "node-lame": "^1.3.2", "node-wav": "^0.0.2", "openai": "^4.85.4", "prism-media": "^1.3.5", "uuid": "^9.0.0", "wavefile": "^11.0.0", "websocket": "^1.0.34", "ws": "^8.13.0"}}