{{- if .Values.nginx.transport.enabled }}
apiVersion: v1
kind: Service
metadata:
  name: nginx-demo-transportserver
spec:
  type: ClusterIP
  selector:
    app.kubernetes.io/instance: aidcc-ccczautomationllmconnector-dev-nginx-demo
  ports:
    - name: {{ .Values.nginx.transport.upstream.name }}
      port: {{ .Values.nginx.transport.upstream.port }}
      protocol: {{ .Values.nginx.transport.upstream.protocol }}
      targetPort: {{ .Values.nginx.containerPorts.http }}
{{- end }}