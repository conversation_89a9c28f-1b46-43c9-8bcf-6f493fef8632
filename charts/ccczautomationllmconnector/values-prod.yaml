# Declare variables to be passed into your templates.

# -------------------------------------
# Prometheus Alerting Thresholds
# -------------------------------------
alerting:
  enabled: true
  critical:
    enabled: true
    CPUsageHigh:
      percent: 90
    MemoryUsageHigh:
      percent: 80
    PodFrequentRestarts:
      count: 2
    PersistentVolume:
      freeBytes: 0
      percentFull: 80
  warning:
    enabled: true
    CPUsageElevated:
      percent: 80
    MemoryUsageElevated:
      percent: 70
    PersistentVolumeElevated:
      percentFull: 70
  info:
    enabled: false
    CPUsageLow:
      percent: 20
    MemoryUsageLow:
      percent: 20

# -------------------------------------
# Connector Configuration
# -------------------------------------
connector:
  PORT: 8080
  APPLICATION_NAME: CCCZAutomationLLMConnector
  NODE_ENV: production
  REDIS_PASSWORD: redis123
  REDIS_PORT: 6379

# -------------------------------------
# Environment Variables
# -------------------------------------
environment:
  # Speech-to-Text (STT) Configuration
  SPEECH_SERVICE: ''
  # Google Cloud STT Configuration
  GOOGLE_PROJECT_ID: ''
  GOOGLE_APPLICATION_CREDENTIALS: ''
  # Azure STT Configuration
  AZURE_SPEECH_KEY: ''
  AZURE_SPEECH_REGION: ''

  # Text-to-Speech (TTS) Configuration
  TTS_PROVIDER: ''
  # ElevenLabs TTS Configuration
  ELEVENLABS_API_KEY: ''
  ELEVENLABS_VOICE_ID: ''

  # Language Model (LLM) Configuration
  LLM_PROVIDER_TYPE: ''
  # LiteLLM Configuration
  LITELLM_API_KEY: ''
  LITELLM_ENDPOINT: ''
  LITELLM_MODEL: ''

  # Server Configuration
  PORT: ''
  IS_DEBUG_MODE: ''
  DEBUG_AUDIO: ''

  # Monitoring & Performance
  ENABLE_PERF_MONITOR: ''

  # Database Configuration
  MOCKING_DATABASE: ''

  # Network Configuration
  HTTP_PROXY: ''
  NO_PROXY: ''
  GRPC_DNS_RESOLVER: ''

  # Barge-in and Streaming Configuration
  ENABLE_BARGE_IN: ''
  BARGE_IN_CONFIDENCE_THRESHOLD: ''
  INTERIM_TRANSCRIPT_STABILITY_THRESHOLD: ''
  ENABLE_STABLE_STREAM: ''
  PAUSE_THRESHOLD_MS: ''
  ASR_STREAMING_MODE: ''
  DEBUG_BARGE_IN: ''

  # Logging Configuration
  LOG_LEVEL: ''
  METRICS_LOG_LEVEL: ''
  LOG_HTTP_REQUESTS: ''
  LOG_HTTP_RESPONSES: ''
  LOG_OPENAI_REQUESTS: ''
  LOG_OPENAI_RESPONSES: ''
  LOG_WEBSOCKET_MESSAGES: ''
  LOG_ASR_INTERIM: ''
  LOG_ASR_RAW_RESPONSES: ''
  LOG_ASR_PARSED_RESULTS: ''
  LOG_ASR_UNSTABLE_TRANSCRIPTS: ''
  OPENAI_LOG_FORMAT: ''

# -------------------------------------
# Service Configuration
# -------------------------------------
service:
  type: ClusterIP
  port: 8080

# -------------------------------------
# Ingress Configuration
# -------------------------------------
ingress:
  enabled: true
  annotations:
    kubernetes.io/ingress.class: nginx
  ingressClassName: nginx
  domainname: aidcc-ccczautomationllmconnector.aks-cc.network.cz.o2
  hosts:
    - host: aidcc-ccczautomationllmconnector.aks-cc.network.cz.o2
      paths:
        - path: /
          pathType: Prefix
  tls:
    enabled: true

# -------------------------------------
# Resources Configuration
# -------------------------------------
resources:
  limits:
    cpu: 1000m
    memory: 1Gi
  requests:
    cpu: 200m
    memory: 256Mi

# -------------------------------------
# Affinity Configuration
# -------------------------------------
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app
                operator: In
                values:
                  - connector
          topologyKey: kubernetes.io/hostname
