apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: aidcc-ccczautomationllmconnector-dev
  namespace: argocd
  labels:
    framework: None
    environment: development
spec:
  project: aidcc
  source:
    path: charts/ccczautomationllmconnector
    repoURL: https://network.git.cz.o2/deployments/aidcc-ccczautomationllmconnector.git
    targetRevision:
    helm:
      valueFiles:
      parameters:
  destination:
    name: ''
    namespace: aidcc-ccczautomationllmconnector-dev
    server: 'https://kubernetes.default.svc'
  syncPolicy:
    automated:
  info:
  - name: "Application human-readable name"
    value: "URL to the GUI endpoint"
