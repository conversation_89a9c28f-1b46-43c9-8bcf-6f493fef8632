// Test script for metrics with user input
const { PerformanceLogger } = require('./dist/services/monitoring/performance-logger');
const { PerformanceMetricsStore } = require('./dist/services/monitoring/performance-metrics-store');

// Enable metrics logging
process.env.METRICS_LOG_LEVEL = '0';
process.env.ENABLE_PERF_MONITOR = 'true';

// Create a test function
async function testMetrics() {
  console.log('Starting metrics test with user input...');

  // Get the metrics store instance
  const metricsStore = PerformanceMetricsStore.getInstance();
  const logger = PerformanceLogger.getInstance();

  // Initialize a conversation
  const conversationId = 'test-conv-' + Date.now();
  const ani = '123456789';
  logger.setConversationId(conversationId);
  console.log(`Initialized conversation: ${conversationId}`);

  // Test 1: Initial greeting
  console.log('\nTest 1: Initial greeting');
  logger.initializeRequest();
  logger.setCurrentUserInput('Initial Greeting');
  logger.startPhase('llmProcessing');
  await sleep(500);
  logger.endPhase('llmProcessing');
  logger.startPhase('textToSpeech');
  await sleep(300);
  logger.endPhase('textToSpeech');
  logger.finalizeRequest('', 'Dobrý den, jak vám mohu pomoci?');

  // Test 2: User input from ASR
  console.log('\nTest 2: User input from ASR');
  logger.initializeRequest();
  logger.setCurrentUserInput('Potřebuji informace o mém tarifu');
  logger.startPhase('speechToText');
  await sleep(700);
  logger.endPhase('speechToText');
  logger.startPhase('llmProcessing');
  await sleep(1000);
  logger.endPhase('llmProcessing');
  logger.startPhase('textToSpeech');
  await sleep(500);
  logger.endPhase('textToSpeech');
  logger.finalizeRequest('', 'Váš aktuální tarif je Neomezený. Zbývá vám 500 MB dat.');

  // Test 3: DTMF input
  console.log('\nTest 3: DTMF input');
  logger.initializeRequest();
  logger.setCurrentUserInput('DTMF: 123');
  logger.startPhase('llmProcessing');
  await sleep(800);
  logger.endPhase('llmProcessing');
  logger.startPhase('textToSpeech');
  await sleep(400);
  logger.endPhase('textToSpeech');
  logger.finalizeRequest('', 'Zadali jste kód 123. Děkujeme.');

  // Finalize the conversation
  logger.finalizeConversation();

  // Get and display the conversation metrics
  const metrics = metricsStore.getConversationMetrics(conversationId);
  console.log('\nConversation Metrics:');
  console.log(JSON.stringify(metrics, null, 2));

  console.log('\nMetrics test complete.');
}

// Helper function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the test
testMetrics().catch(error => {
  console.error('Error in metrics test:', error);
});
