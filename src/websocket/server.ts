import WS, { WebSocket, Server as WebSocketServer } from 'ws';
import express, { Express, Request, Response, NextFunction } from 'express';
import { BotService } from '../services/bot-service/index';
import { verifyRequestSignature } from '../auth/authenticator';
import { Session } from '../session/session';

import { getPort } from '../common/environment-variables';
import { SecretService } from '../services/secret-service';
import { Server as HttpServer } from 'http';
import { PerformanceLogger } from '../services/monitoring/performance-logger';
import { metricsRouter } from './routes/metrics';
import { errorHandler } from './middleware/error-handler';
import { ChatMessage } from '../types/chat';
import { logError, logInfo } from '../services/logging';

export class Server {
  private performanceLogger = PerformanceLogger.getInstance();
  private app: Express | undefined;
  private httpServer: HttpServer | undefined;
  private wsServer: WebSocketServer | undefined;
  private sessionMap: Map<WebSocket, Session> = new Map();
  private secretService = new SecretService();
  private botService: BotService;
  // MCP server property removed

  constructor(botService: BotService) {
    this.botService = botService;
    // MCP server initialization removed

    // Enable event-driven architecture for testing
    this.enableEventDrivenArchitecture();
  }

  /**
   * Enable event-driven architecture for testing
   */
  private enableEventDrivenArchitecture() {
    // Enable event-driven architecture for all sessions
    logInfo('[Server] Enabling event-driven architecture for testing');

    // We'll use the SimpleEventAdapter to add event emission to traditional sessions
    logInfo('[Server] Using SimpleEventAdapter for event emission');

    logInfo('[Server] Event-driven architecture enabled');
  }

  async start() {
    try {
      const port = getPort();
      logInfo(`Starting server on port: ${port}`);

      this.app = express();

      if (!this.app) {
        throw new Error('Failed to create Express app');
      }

      // Add middleware
      this.app.use(express.json());

      // Enable CORS for all origins
      this.app.use((req: Request, res: Response, next: NextFunction) => {
        res.header('Access-Control-Allow-Origin', '*');
        res.header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
        res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');

        // Handle preflight requests
        if (req.method === 'OPTIONS') {
          return res.sendStatus(200);
        }
        next();
      });

      // Serve static files from /app directory
      this.app.use('/app', express.static('app'));

      // SPA fallback - serve index.html for all /app/* routes
      this.app.get('/app/*', (_req, res) => {
        res.sendFile('app/index.html', { root: process.cwd() });
      });

      this.app.use(errorHandler);

      // Chat-related endpoints removed
      this.app.use('/askMetrics', metricsRouter());

      // MCP server initialization and mounting removed

      // Add health check endpoint with database status
      this.app.get('/health', async (_req, res) => {
        try {
          const dbHealth = await this.botService.dbService.checkHealth();

          res.status(dbHealth.status === 'healthy' ? 200 : 500).json({
            status: dbHealth.status,
            timestamp: new Date().toISOString(),
            database: {
              status: dbHealth.status,
              error: dbHealth.error,
            },
          });
        } catch (error) {
          res.status(500).json({
            status: 'unhealthy',
            timestamp: new Date().toISOString(),
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      });

      // Return promise to properly handle startup
      return new Promise((resolve, reject) => {
        try {
          this.httpServer = this.app!.listen(port, () => {
            logInfo(`HTTP Server successfully started on port ${port}`);

            this.wsServer = new WebSocket.Server({
              noServer: true,
            });

            if (!this.wsServer) {
              throw new Error('Failed to create WebSocket server');
            }

            this.setupWebSocketHandlers();
            logInfo('WebSocket Server initialized');
            resolve(true);
          });

          this.httpServer.on('error', err => {
            logError(`Failed to start server: ${err}`);
            reject(err);
          });

          // Add timeout handling
          this.httpServer.setTimeout(120000); // 2 minute timeout
        } catch (err) {
          logError(`Error during server listen: ${err}`);
          reject(err);
        }
      });
    } catch (err) {
      logError(`Error during server startup: ${err}`);
      throw err;
    }
  }

  private setupWebSocketHandlers() {
    if (!this.httpServer || !this.wsServer) {
      throw new Error('HTTP Server or WebSocket Server not initialized');
    }

    this.httpServer.on('upgrade', async (request: Request, socket: any, head: any) => {
      try {
        logInfo(`Received a connection request from ${request.url}.`);

        const verifyResult = await verifyRequestSignature(request, this.secretService);
        if (verifyResult.code !== 'VERIFIED') {
          logInfo('Authentication failed, closing the connection.');
          socket.write('HTTP/1.1 401 Unauthorized\r\n\r\n');
          socket.destroy();
          return;
        }

        this.wsServer!.handleUpgrade(request, socket, head, (ws: WebSocket) => {
          logInfo('Authentication was successful.');
          this.wsServer!.emit('connection', ws, request);
        });
      } catch (error: unknown) {
        logError(`Error during WebSocket upgrade: ${error}`);
        socket.write('HTTP/1.1 500 Internal Server Error\r\n\r\n');
        socket.destroy();
      }
    });

    this.wsServer.on('connection', (ws: WebSocket, request: Request) => {
      const sessionId = request.headers['audiohook-session-id'] as string;
      logInfo(`[WebSocket] Connection received for session ${sessionId}`);

      ws.on('close', () => {
        // const session = this.sessionMap.get(ws);
        logInfo('WebSocket connection closed.');
        this.deleteConnection(ws);
      });

      ws.on('error', (error: Error) => {
        // const session = this.sessionMap.get(ws);
        logError(`WebSocket Error: ${error}`);
        ws.close();
      });

      ws.on('message', (data: WS.RawData, isBinary: boolean) => {
        if (ws.readyState !== WebSocket.OPEN) {
          return;
        }

        const session = this.sessionMap.get(ws);

        if (!session) {
          const dummySession = new Session(
            ws,
            request.headers['audiohook-session-id'] as string,
            request.url,
            this.botService
          );
          console.log('Session does not exist.');
          dummySession.sendDisconnect('error', 'Session does not exist.', {});
          return;
        }

        if (isBinary) {
          session.processBinaryMessage(data as Uint8Array);
        } else {
          session.processTextMessage(data.toString());
        }
      });

      this.createConnection(ws, request);
    });
  }

  private createConnection(ws: WebSocket, request: Request) {
    let session: Session | undefined = this.sessionMap.get(ws);

    if (session) {
      return;
    }

    const sessionId = request.headers['audiohook-session-id'] as string;

    // Create traditional session
    session = new Session(ws, sessionId, request.url, this.botService);

    // Session now uses event-driven architecture by default
    logInfo('[Server] Created session with event-driven architecture');

    // Initialize metrics for new conversation when ANI and conversationId are set
    const ani = session.getAni();
    const conversationId = session.getConversationId();
    if (ani && conversationId) {
      this.performanceLogger.setConversationId(conversationId);
    }

    logInfo('Creating a new session.');
    this.sessionMap.set(ws, session);
  }

  private async deleteConnection(ws: WebSocket) {
    const session: Session | undefined = this.sessionMap.get(ws);

    if (!session) {
      return;
    }

    try {
      const ani = session.getAni();
      const conversationId = session.getConversationId();
      const startTime = session.getStartTime();

      // Get the bot resource and other necessary details
      const botResource = await this.botService.getBotIfExists(conversationId, ani);

      if (botResource && ani && conversationId) {
        // Finalize metrics for the conversation
        this.performanceLogger.finalizeConversation();
        this.performanceLogger.setConversationId(null);

        // Generate and store conversation summary if we have an active conversation
        try {
          const conversationMessages = await this.botService.dbService.getConversationHistory(
            ani,
            conversationId
          );

          if (conversationMessages.length > 0) {
            const summaryPrompt: ChatMessage[] = [
              {
                role: 'system',
                content:
                  'Sumarizuj následující konverzaci v českém jazyce. Zaměř se na hlavní témata a důležité body diskuse.',
              },
              {
                role: 'user',
                content: conversationMessages
                  .map(
                    (m: { isBot: boolean; messageText: string }) =>
                      `${m.isBot ? 'Bot' : 'Zákazník'}: ${m.messageText}`
                  )
                  .join('\n'),
              },
            ];

            const summary = await botResource.getResponseForSummary(summaryPrompt);

            await this.botService.dbService.storeSummary({
              ani,
              conversationId,
              startTime,
              endTime: new Date(),
              summaryText: summary,
              messageCount: conversationMessages.length,
              durationSeconds: Math.floor((new Date().getTime() - startTime.getTime()) / 1000),
            });
          }

          this.botService.deleteBotService(conversationId);
        } catch (error) {
          logError(`[SERVER] Error in conversation summarization: ${error}`);
        }
      }

      session.close();
    } catch (error: unknown) {
      logError(`Error closing session: ${error}`);
    }

    logInfo('Deleting session.');
    this.sessionMap.delete(ws);
  }
}
