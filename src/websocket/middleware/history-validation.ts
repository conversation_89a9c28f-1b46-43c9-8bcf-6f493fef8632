import { Request, Response, NextFunction } from 'express';

export const validateHistoryRequest = (req: Request, res: Response, next: NextFunction) => {
  const { ani } = req.query;

  if (!ani || typeof ani !== 'string') {
    return res.status(400).json({
      error: 'Missing required parameter: ani',
    });
  }

  // Validate ani format if needed
  if (ani.trim().length === 0) {
    return res.status(400).json({
      error: 'Invalid ani parameter: cannot be empty',
    });
  }

  next();
};
