import { Request, Response, NextFunction } from 'express';

export const errorHandler = (err: any, _req: Request, res: Response, _next: NextFunction): void => {
  console.error('[ERROR]', err);

  if (err.response?.status === 429) {
    res.status(429).json({
      error: 'Too many requests to AI service',
    });
    return;
  }

  res.status(500).json({
    error: 'Internal server error',
    details: process.env.NODE_ENV === 'development' ? err.message : undefined,
  });
};
