import { MediaParameter } from '../../../protocol/core';
import { ClientMessage, OpenMessage, ServerMessage } from '../../../protocol/message';
import { Session } from '../../../session/session';
import { MessageHandler } from '../message-handler';
import { logInfo, logWarning, logError } from '../../../services/logging/logger';

export class OpenMessageHandler implements MessageHandler {
  async handleMessage(message: ClientMessage, session: Session) {
    const parsedMessage: OpenMessage = message as OpenMessage;

    if (!parsedMessage) {
      const message = 'Invalid request parameters.';
      console.log(message);
      session.sendDisconnect('error', message, {});
      return;
    }

    // Extract and log important parameters
    const conversationId = parsedMessage.parameters.conversationId;
    const ani = parsedMessage.parameters.inputVariables?.Ani;

    // Ensure ASR is initialized and session is IDLE before proceeding
    await session.initializeAfterOpen(conversationId);

    console.log('Received an Open Message.');
    console.log(`ConversationId: ${conversationId || 'MISSING'}`);
    console.log(`ANI: ${ani || 'MISSING'}`);

    // If conversationId is missing, use the session ID as a fallback
    if (!conversationId) {
      console.log('WARNING: Missing conversationId in open message, using session ID as fallback');
      // Extract session ID from the message ID
      const sessionId = message.id;
      session.setConversationId(sessionId);
    } else {
      session.setConversationId(conversationId);
    }

    // Set ANI if available
    session.setClientAni(ani);

    let selectedMedia: MediaParameter | null = null;

    parsedMessage.parameters.media.forEach((element: MediaParameter) => {
      if (element.format === 'PCMU' && element.rate === 8000) {
        selectedMedia = element;
      }
    });

    if (!selectedMedia) {
      const message = 'No supported media type was found.';
      console.log(message);
      session.sendDisconnect('error', message, {});
      return;
    }

    console.log(`Using MediaParameter ${JSON.stringify(selectedMedia)}`);

    session.setSelectedMedia(selectedMedia);

    if (parsedMessage.parameters.inputVariables) {
      session.setInputVariables(parsedMessage.parameters.inputVariables);
    }

    // Check if bot exists using the bot adapter
    const botAdapter = session.getBotAdapter();
    const exists = await botAdapter.checkIfBotExists();

    if (!exists) {
      const message = 'The specific Bot does not exist.';
      console.log(message);
      session.sendDisconnect('error', message, {});
      return;
    }

    if (selectedMedia) {
      const response: ServerMessage = session.createMessage('opened', {
        media: [selectedMedia],
      });

      session.send(response);
      // Send out the turn response for the start of a conversation.
      // Pass true to indicate this is the initial greeting
      await session.processBotStart(true);
    }
  }
}
