import { createTestSession } from '../../../../session/__tests__/test-utils';

describe('MCP Service Integration', () => {
  it('should discover and invoke getDateTime tool and store result', async () => {
    const mockMCPService = {
      listTools: jest
        .fn()
        .mockResolvedValue([{ name: 'getDateTime', arguments: { format: 'ISO' } }]),
      safeGetDateTime: jest.fn().mockResolvedValue({ time: '2025-06-04T09:00:00Z' }),
    };
    const setLastToolResult = jest.fn();
    const session = createTestSession({
      mcpService: mockMCPService,
      setLastToolResult,
    });

    // Simulate tool discovery and invocation (test-only, not in main handler)
    const tools = await session.mcpService!.listTools();
    expect(tools).toEqual([{ name: 'getDateTime', arguments: { format: 'ISO' } }]);
    if (tools.some((t: any) => t.name === 'getDateTime')) {
      const result = await session.mcpService!.safeGetDateTime();
      session.setLastToolResult(result);
      expect(result).toEqual({ time: '2025-06-04T09:00:00Z' });
      expect(setLastToolResult).toHaveBeenCalledWith({ time: '2025-06-04T09:00:00Z' });
    }
    expect(mockMCPService.listTools).toHaveBeenCalled();
    expect(mockMCPService.safeGetDateTime).toHaveBeenCalled();
  });
});
