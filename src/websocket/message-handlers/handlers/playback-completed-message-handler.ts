/**
 * <PERSON><PERSON> for playback_completed messages from the client.
 * Transitions the session state from PLAYING to IDLE.
 */
import { ClientMessage } from '../../../protocol/message';
import { Session } from '../../../session/session';
import { MessageHandler } from '../message-handler';
import { getASRService } from '../../../services/asr-service';
import { logInfo, logDebug, logWarning } from '../../../services/logging/logger';
import { SessionState } from '../../../session/session-state-manager';

export class PlaybackCompletedMessageHandler implements MessageHandler {
  async handleMessage(_message: ClientMessage, session: Session) {
    // Get current state before attempting to change it
    const currentState = session.getSessionState();
    const stateManager = session.getSessionStateManager();

    // Check if barge-in is enabled for this session
    const bargeInEnabled = session.isBargeInEnabled();

    // Only transition to IDLE if we're in PLAYING state
    if (currentState === SessionState.PLAYING) {
      // Transition to IDLE state
      await stateManager.setState(SessionState.IDLE, {
        reason: 'Client reported playback completed',
        playbackStatus: 'stopped',
        clientConfirmed: true,
      });

      logInfo('[PlaybackCompleted] Transitioned from PLAYING to IDLE state');
    } else {
      // If we're in an unexpected state, log a warning
      logWarning(
        `[PlaybackCompleted] Received playback_completed message while in ${currentState} state.`
      );
    }

    // CRITICAL FIX: Directly control the ASR service's isIgnoringAudioInput flag
    try {
      const asrService = await getASRService();

      // Always ensure audio input processing is enabled after playback completes
      asrService.stopIgnoringAudioInput();

      if (bargeInEnabled) {
        logInfo('[Event] Playback Completed - Audio input processing was already active');
      } else {
        logInfo('[Event] Playback Completed - Resuming audio input processing');
        logDebug('[BARGE-IN] ASR service isIgnoringAudioInput flag explicitly set to FALSE');
      }
    } catch (error) {
      console.error('[PlaybackCompleted] Error controlling ASR service:', error);
    }
  }
}
