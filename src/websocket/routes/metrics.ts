import { Router, Request, Response, NextFunction } from 'express';
import { PerformanceLogger } from '../../services/monitoring/performance-logger';
import { SystemMetricsCollector } from '../../services/monitoring/system-metrics-collector';

export const metricsRouter = (): Router => {
  const router = Router();
  const performanceLogger = PerformanceLogger.getInstance();
  // const trendAnalyzer = TrendAnalyzer.getInstance();
  const systemMetricsCollector = SystemMetricsCollector.getInstance();

  router.post('/', async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { type, conversationId, timeRange, duration } = req.body;

      switch (type) {
        case 'conversation':
          if (!conversationId) {
            return res.status(400).json({
              error: 'Missing required field: conversationId',
            });
          }
          const metrics = performanceLogger.getConversationMetrics(conversationId);
          if (!metrics) {
            return res.status(404).json({
              error: `No metrics found for conversation ${conversationId}`,
            });
          }
          return res.json({ metrics });

        case 'system':
          let systemMetrics;
          if (duration) {
            systemMetrics = systemMetricsCollector.getMetricsHistory(duration);
          } else {
            systemMetrics = systemMetricsCollector.getLatestMetrics();
          }
          if (!systemMetrics) {
            return res.status(404).json({
              error: 'No system metrics available',
            });
          }
          return res.json({ metrics: systemMetrics });

        // case 'trends':
        //   let trends;
        //   if (timeRange) {
        //     trends = trendAnalyzer.getTrends(timeRange);
        //     if (!trends) {
        //       return res.status(404).json({
        //         error: `No trends found for time range ${timeRange}`,
        //       });
        //     }
        //   } else {
        //     const trendMap = trendAnalyzer.getAllTrends();
        //     if (trendMap.size === 0) {
        //       return res.status(404).json({
        //         error: 'No trends available',
        //       });
        //     }
        //     trends = Object.fromEntries(trendMap);
        //   }
        //   return res.json({ metrics: trends });

        default:
          return res.status(400).json({
            error: "Invalid metrics type. Must be 'conversation', 'system', or 'trends'",
          });
      }
    } catch (error) {
      next(error);
    }
  });

  return router;
};
