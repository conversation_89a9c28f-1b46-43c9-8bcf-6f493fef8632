import dotenv from 'dotenv';
// Load environment variables from .env.development in development mode
dotenv.config({ path: process.env.NODE_ENV === 'production' ? '.env' : '.env.development' });

import { Server } from './websocket/server';
import { initializeSpeechServices } from './services/speech/initialize';
import { BotService } from './services/bot-service/index';
import { logInfo, logError } from './services/logging/logger';
import { createMCPServiceFromEnv } from './services/mcp/mcp-service';

async function main() {
  try {
    console.log(`Starting service at ${new Date().toLocaleTimeString()}`);

    // Initialize speech services before starting the server
    try {
      console.log('Initializing speech services...');
      initializeSpeechServices();
      console.log('Speech services initialized');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      throw error; // Prevent server from starting if initialization fails
    }

    // Initialize MCP service singleton
    const mcpService = createMCPServiceFromEnv();

    // Initialize services
    const botService = new BotService(mcpService);
    const server = new Server(botService);

    // Start the server
    await server.start();
    console.log('Service started successfully.');

    // Handle shutdown gracefully
    process.on('SIGTERM', () => {
      console.log('SIGTERM received. Shutting down...');
      process.exit(0);
    });

    process.on('SIGINT', () => {
      console.log('SIGINT received. Shutting down...');
      process.exit(0);
    });
  } catch (error: unknown) {
    console.error('Failed to start service:', error);
    process.exit(1);
  }
}

main();
