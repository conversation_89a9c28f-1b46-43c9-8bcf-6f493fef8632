import { IWebSocketController, WebSocketController } from './websocket-controller';
import { v4 as _uuid } from 'uuid';
import { TranscriptProcessor } from '../services/transcript/transcript-processor';
import { WebSocket } from 'ws';
import { JsonStringMap, MediaParameter } from '../protocol/core';
import { SessionState } from './session-state-manager';
import { EventDrivenStateManager } from './state-machine/event-driven-state-manager';
import { registerStateActions } from './state-machine/register-actions';
import { ISession } from './session-interface';
import { EventEmitter } from '../services/events/event-emitter';
import {
  ClientMessage,
  DisconnectParameters,
  DisconnectReason,
  ServerMessage,
  ServerMessageType,
} from '../protocol/message';
import { SessionEventType, BargeInDetectedEventData } from '../services/events/session-events';
import { BotTurnDisposition } from '../protocol/voice-bots';

import { MessageHandlerRegistry } from '../websocket/message-handlers/message-handler-registry';
import { BotService, BotResource, BotServiceAdapter } from '../services/bot-service/index';
import { Transcript } from '../services/speech';
import { AudioServiceAdapter } from '../services/audio/index';
import { DTMFManager } from '../services/dtmf/dtmf-manager';
import { BargeInManager } from '../services/barge-in/barge-in-manager';
import { EnhancedASRService } from '../services/asr-service/index';
import { PauseDetectionService } from '../services/audio/pause-detection-service';
import { loggingConfig } from '../services/logging/logging-config';

import type { PlaybackState } from '../services/barge-in/barge-in-manager';
import { RequestContext } from '../services/monitoring/performance-logger';
import { SessionMetricsAdapter } from '../services/monitoring/session-metrics-adapter';
import { callWithMeasurement } from '../services/monitoring/measure-call';
import {
  logDebug,
  logError,
  logStateTransition,
  logMetrics,
  logInfo,
  logWarning,
  logBargeIn,
} from '../services/logging';

export class Session implements ISession {
  // ISession required property: unique session identifier
  public get id(): string {
    return this.clientSessionId;
  }
  // Core components
  private transcriptProcessor: TranscriptProcessor = new TranscriptProcessor();
  private wsController: IWebSocketController;
  private messageHandlerRegistry = new MessageHandlerRegistry();

  // State management
  private stateManager: EventDrivenStateManager;
  private eventEmitter: EventEmitter;

  /**
   * Call this after Open message, before any state transitions or arming.
   * Ensures ASR is initialized and set before the session transitions to IDLE.
   */
  public async initializeAfterOpen(conversationId: string): Promise<void> {
    await this.initializeASRService(conversationId);
    await this.stateManager.setState(SessionState.IDLE, {
      reason: 'ASR initialized, session ready',
    });
    logDebug(`[Session] Session initialization complete, now IDLE`);
  }
  private isProcessingResponse = false;
  // Deferred closure flag for critical states
  private pendingClose = false;

  // Cancellation for current user turn (ASR/bot/tts/playback)
  private currentTurnAbortController: AbortController | null = null;

  // Cancellation for asynchronous processing (e.g., bot processing)
  private currentProcessingAbortController: AbortController | null = null;

  // Adapter services
  private metricsAdapter!: SessionMetricsAdapter;
  private botAdapter!: BotServiceAdapter;
  private audioAdapter!: AudioServiceAdapter;

  /**
   * Dispose of audio resources for this session
   * This method delegates to the audio adapter
   */
  public async disposeAudio(): Promise<void> {
    if (this.audioAdapter && typeof this.audioAdapter.dispose === 'function') {
      await this.audioAdapter.dispose();
    }
  }

  /**
   * Dispose of bot resources for this session
   * This method delegates to the bot adapter
   */
  public async disposeBot(): Promise<void> {
    if (this.botAdapter && typeof this.botAdapter.dispose === 'function') {
      await this.botAdapter.dispose();
    }
  }

  /**
   * Close the WebSocket connection for this session
   * This method delegates to the WebSocket controller
   */
  public closeWebSocket(): void {
    if (this.wsController && typeof this.wsController.close === 'function') {
      this.wsController.close();
    }
  }

  /**
   * Finalize metrics for this session
   * This method delegates to the metrics adapter
   */
  public finalizeMetrics(): void {
    if (this.metricsAdapter && typeof this.metricsAdapter.finalizeConversation === 'function') {
      this.metricsAdapter.finalizeConversation();
    }
  }
  // TTS service for RESPONDING state
  private ttsService?: import('../services/speech/base/base-tts-service').BaseTTSService;

  public getTTSService():
    | import('../services/speech/base/base-tts-service').BaseTTSService
    | undefined {
    return this.ttsService;
  }

  public async initializeTTSService(): Promise<void> {
    const { getTTSService } = await import('../services/tts-service');
    this.ttsService = await getTTSService();
  }
  // State machine support
  private latestBotResponse?: import('../services/bot-service/bot-response').BotResponse;

  public getLatestBotResponse():
    | import('../services/bot-service/bot-response').BotResponse
    | undefined {
    return this.latestBotResponse;
  }

  public setLatestBotResponse(
    response: import('../services/bot-service/bot-response').BotResponse | undefined
  ): void {
    this.latestBotResponse = response;
  }

  /**
   * Refresh response handling.
   * This is called when a bot response is received asynchronously after we've already
   * transitioned to RESPONDING state. It allows the session to retry playback or
   * other response handling that might have failed due to the response not being available.
   */
  public refreshResponse(): void {
    const { logInfo } = require('../services/logging/logger');

    // Check if we have a bot response now
    if (!this.latestBotResponse) {
      logInfo('[Session] refreshResponse: No bot response available, cannot refresh');
      return;
    }

    // Check if we're in RESPONDING state
    if (this.stateManager.getState() !== SessionState.RESPONDING) {
      logInfo(
        `[Session] refreshResponse: Not in RESPONDING state (current: ${this.stateManager.getState()}), cannot refresh`
      );
      return;
    }

    logInfo('[Session] refreshResponse: Retrying playback with newly available bot response');

    // Retry playback
    if (this.latestBotResponse.audioBytes) {
      logInfo(
        `[Session] refreshResponse: Sending ${this.latestBotResponse.audioBytes.length} bytes of audio`
      );
      this.sendAudio(this.latestBotResponse.audioBytes, this.getCurrentRequest())
        .then(() => {
          logInfo('[Session] refreshResponse: Successfully sent audio');
        })
        .catch(error => {
          logInfo(
            `[Session] refreshResponse: Error sending audio: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
        });
    } else if (this.latestBotResponse.text && this.ttsService) {
      const ttsService = this.ttsService;
      const ttsText = this.latestBotResponse.text;
      logInfo(
        `[Session] refreshResponse: Generating audio for text: "${ttsText.substring(0, 50)}${
          ttsText.length > 50 ? '...' : ''
        }"`
      );
      const metricsContext = this.getCurrentRequest?.();
      callWithMeasurement(metricsContext, 'TTSServiceCall', async () =>
        ttsService.getAudioBytes(ttsText)
      )
        .then(audioBytes => {
          logInfo(
            `[Session] refreshResponse: Generated ${audioBytes.length} bytes of audio, sending to client`
          );
          return this.sendAudio(audioBytes, this.getCurrentRequest());
        })
        .then(() => {
          logInfo('[Session] refreshResponse: Successfully sent generated audio');
        })
        .catch(error => {
          logInfo(
            `[Session] refreshResponse: Error generating or sending audio: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
        });
    } else {
      logInfo(
        '[Session] refreshResponse: Bot response has neither text nor audio, cannot play anything'
      );
    }
  }

  /**
   * Get the AbortSignal for the current turn (ASR/bot/tts/playback).
   * Returns a dummy signal if not set.
   */
  public getCurrentTurnAbortSignal(): AbortSignal {
    if (this.currentTurnAbortController) {
      return this.currentTurnAbortController.signal;
    }
    // Return a dummy signal that is never aborted
    return new AbortController().signal;
  }

  /**
   * Cancel the current turn (used for barge-in or session close).
   */
  public cancelCurrentTurn(): void {
    if (this.currentTurnAbortController) {
      this.currentTurnAbortController.abort();
      this.currentTurnAbortController = null;
    }
  }

  /**
   * Start a new AbortController for a new user turn.
   * Call this at the start of each new user input (ASR/DTMF).
   */
  public startNewTurnAbortController(): void {
    this.cancelCurrentTurn();
    this.currentTurnAbortController = new AbortController();
  }

  /**
   * Get the current processing abort controller, if any.
   * This is used to cancel ongoing asynchronous processing (e.g., during barge-in).
   * @returns The current AbortController or null if none exists.
   */
  public getCurrentProcessingAbortController(): AbortController | null {
    return this.currentProcessingAbortController;
  }

  /**
   * Set the current processing abort controller.
   * This is used to store a reference to an AbortController for cancelling ongoing processing.
   * @param controller The AbortController to set, or null to clear it.
   */
  public setCurrentProcessingAbortController(controller: AbortController | null): void {
    this.currentProcessingAbortController = controller;
  }

  public async sendAudio(audioBytes: Uint8Array, context?: RequestContext): Promise<void> {
    if (!this.audioAdapter) {
      throw new Error('Audio adapter not initialized');
    }
    await this.audioAdapter.sendAudio(audioBytes, context);
  }
  private currentRequest: RequestContext | null = null;
  private botService: BotService;
  // AudioManager removed: all audio/ASR/barge-in logic is now handled by adapters and managers.
  private dtmfManager: DTMFManager;
  private bargeInManager: BargeInManager;

  /* eslint-disable @typescript-eslint/no-unused-vars */
  // This property is used by the bot adapter but appears unused to the linter
  private selectedBot: BotResource | null = null;
  /* eslint-enable @typescript-eslint/no-unused-vars */

  // Session metadata
  private clientSessionId: string;
  private conversationId: string | undefined;
  private ani: string | undefined;
  private lastServerSequenceNumber = 0;
  private lastClientSequenceNumber = 0;
  private startTime: Date;

  // These properties are used by external systems but appear unused to the linter
  // They should be kept for compatibility with the protocol
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
  private _url: string;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
  private _inputVariables: JsonStringMap = {};
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, @typescript-eslint/naming-convention
  private _selectedMedia: MediaParameter | undefined;

  /**
   * Standardized error handling utility for async operations.
   * @param operation The async operation to execute.
   * @param errorMessage The error message to log and send on failure.
   * @param disconnectOnError Whether to disconnect the session on error (default: true).
   */
  private async withErrorHandling<T>(
    operation: () => Promise<T>,
    errorMessage: string,
    disconnectOnError = true
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      logError(`${errorMessage}: ${error instanceof Error ? error.message : String(error)}`);
      if (disconnectOnError) {
        this.sendDisconnect('error', errorMessage, {});
      }
      return null;
    }
  }

  /**
   * DRY helper: Awaits a promise, then only runs the callback if session is not disconnecting/closed.
   * Use to prevent invalid state transitions or updates after session close in async flows.
   */
  private async runIfActiveAfter<T>(promise: Promise<T>, fn: (result: T) => void): Promise<void> {
    const result = await promise;
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }
    fn(result);
  }

  /**
   * Start metrics tracking for a new request
   * This method delegates to the metrics adapter
   *
   * @param userInput The user input to track
   * @returns The request context
   */
  private async startMetricsRequest(userInput: string): Promise<RequestContext> {
    // Use the metrics adapter to start a new request
    this.currentRequest = await this.metricsAdapter.startRequest(userInput);
    return this.currentRequest;
  }

  /**
   * Finalize metrics tracking for the current request
   * This method delegates to the metrics adapter
   */
  private finalizeMetricsRequest(): void {
    // Use the metrics adapter to finalize the request
    const aiReply = this.latestBotResponse?.text ?? 'no value when finalized';
    this.metricsAdapter.finalizeRequest(aiReply);

    // Clear local reference
    this.currentRequest = null;
  }

  /**
   * Creates a new Session instance
   *
   * @param ws WebSocket connection to the client
   * @param sessionId Unique identifier for this session
   * @param url Connection URL
   * @param botService Service for bot interactions
   */
  constructor(ws: WebSocket, sessionId: string, url: string, botService: BotService) {
    // Initialize core properties
    this.wsController = new WebSocketController(ws);
    this.clientSessionId = sessionId;
    this._url = url;
    this.botService = botService;
    this.startTime = new Date();

    // Initialize event emitter
    this.eventEmitter = new EventEmitter();

    // Subscribe to BARGE_IN_DETECTED event (event-driven path)
    this.eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, (data: BargeInDetectedEventData) => {
      logDebug(
        `[Session] Received BARGE_IN_DETECTED event from event emitter: ${JSON.stringify(data)}`
      );
      // Only perform side effects: send Genesys notification, do not trigger state transitions
      this.sendBargeInEventToGenesys(data.source, data.text);
      logDebug('[Session] Genesys barge-in event sent (side effect only, no state transition)');
    });

    // Initialize event-driven state manager with proper lifecycle
    this.stateManager = new EventDrivenStateManager(this as unknown as ISession, this.eventEmitter);

    // Register state actions
    registerStateActions(this.stateManager, this.eventEmitter);

    // Register state change listener for logging and metrics
    this.stateManager.onStateChange((oldState, newState, metadata) => {
      // Use the colored state transition logger for better visibility
      logStateTransition(oldState, newState, 'Session', metadata);

      // Log metrics for important state transitions
      if (
        newState === SessionState.PROCESSING_INPUT ||
        newState === SessionState.PROCESSING_BOT ||
        newState === SessionState.PLAYING
      ) {
        const metadataStr = metadata ? ` (${JSON.stringify(metadata)})` : '';
        logMetrics(`[Session] State: ${newState}${metadataStr}`);
      }

      // Deferred session closure logic: if we enter a "safe" state and pendingClose is set, schedule close
      if (
        this.pendingClose &&
        (newState === SessionState.IDLE || newState === SessionState.PLAYING)
      ) {
        logInfo('[Session] Deferred close: now in safe state, scheduling session close');
        this.pendingClose = false; // reset before scheduling to avoid recursion

        // Use Promise.resolve().then() to ensure this happens after the current call stack completes
        Promise.resolve().then(() => {
          // Double-check that no transition is in progress before closing
          if (!this.stateManager.isStateTransitionInProgress()) {
            logInfo('[Session] Executing deferred session close');
            this.close();
          } else {
            logInfo('[Session] Transition still in progress, re-deferring close');
            this.pendingClose = true; // Re-defer the close
          }
        });
      }
    });

    // Initialize BargeInManager with the shared event emitter
    this.bargeInManager = new BargeInManager({ eventEmitter: this.eventEmitter });
    logInfo('[Session] BargeInManager initialized with shared event emitter');

    // No longer register onBargeIn callback: all barge-in events are handled via event emitter and state machine.

    // Initialize DTMFManager with callbacks, routing DTMF barge-in through BargeInManager
    this.dtmfManager = new DTMFManager(
      digits => this.handleDTMFComplete(digits),
      (_source, _digit) => this.bargeInManager.detectDtmfBargeIn(),
      this.bargeInManager
    );

    // Initialize adapter services
    this.initializeAdapters();

    // Set initial state to INITIALIZING; do not transition to IDLE here
    void this.stateManager.setState(SessionState.INITIALIZING, {
      reason: 'Session constructor: begin initialization',
    });

    logDebug(`[Session] Created new session (ID: ${sessionId})`);
  }

  /**
   * Initialize adapter services
   * This method creates and configures the adapter services that will be used by the session
   */
  private initializeAdapters(): void {
    // Initialize metrics adapter
    this.metricsAdapter = new SessionMetricsAdapter(this.stateManager);

    // Initialize bot adapter
    this.botAdapter = new BotServiceAdapter(this.botService, this.stateManager);

    // Initialize audio adapter
    this.audioAdapter = new AudioServiceAdapter(
      this.bargeInManager,
      this.wsController,
      this.stateManager,
      this.dtmfManager
    );

    logDebug('[Session] Adapter services initialized');
  }

  // Getter methods for private properties
  getAni(): string | undefined {
    return this.ani;
  }

  getConversationId(): string | undefined {
    return this.conversationId;
  }

  getStartTime(): Date {
    return this.startTime;
  }

  /**
   * Public getter for botAdapter (for use by state actions)
   */
  public getBotAdapter(): import('../services/bot-service/bot-service-adapter').BotServiceAdapter {
    return this.botAdapter;
  }

  /**
   * Public getter for audioAdapter (for use by SessionFactory)
   */
  public getAudioAdapter(): AudioServiceAdapter {
    return this.audioAdapter;
  }

  /**
   * Public getter for ASRAdapter (for use by SessionFactory)
   */
  public getASRAdapter(): EnhancedASRService | null {
    // We don't have direct access to the ASR service from the audio adapter
    // For now, return null and let the SessionFactory create a new one if needed
    return null;
  }

  /**
   * Public getter for bargeInManager (for use by SessionFactory)
   */
  public getBargeInManager(): BargeInManager {
    return this.bargeInManager;
  }

  /**
   * Public getter for currentRequest (for use by state actions)
   */
  public getCurrentRequest(): RequestContext {
    if (!this.currentRequest) {
      throw new Error('Request context not available in session');
    }
    return this.currentRequest;
  }

  /**
   * Set the current request context (for use by state actions)
   * @param context The new request context
   */
  public setCurrentRequest(context: RequestContext): void {
    this.currentRequest = context;
  }

  /**
   * Public getter for startMetricsRequest (for use by state actions)
   */
  public getStartMetricsRequest(): ((input: string) => void) | undefined {
    return this.startMetricsRequest?.bind(this);
  }
  /**
   * Prepare the session for new user input (e.g., after playback).
   * This is called by the state machine when entering the IDLE state.
   */
  public async prepareForUserInput(): Promise<void> {
    const sessionState = this.stateManager?.getState?.() || 'unknown';
    logInfo(`[Session] prepareForUserInput: Called in session state: ${sessionState}`);
    // Do not start ASR phase timing here; it should be started when actual user audio is processed
    if (this.audioAdapter) {
      try {
        await this.audioAdapter.armForInput();
        logInfo('[Session] AudioServiceAdapter.armForInput() completed successfully');
      } catch (error) {
        logError(
          `[Session] AudioServiceAdapter.armForInput() failed: ${
            error instanceof Error ? error.stack || error.message : String(error)
          }`
        );
        throw error;
      }
    }
  }

  /**
   * Close the session and clean up resources
   *
   * This method:
   * 1. Checks if the session is already closed
   * 2. Transitions to DISCONNECTING state if not already there
   * 3. Disposes of all services through adapters
   * 4. Closes the WebSocket connection
   * 5. Transitions to CLOSED state
   */
  close() {
    // Use state manager for closed state
    if (this.stateManager.isClosed()) {
      logDebug('[Session] Session already closed, ignoring close request');
      return;
    }

    // If in a critical state, defer closure
    const currentState = this.stateManager.getState();
    if (
      currentState === SessionState.PROCESSING_INPUT ||
      currentState === SessionState.PROCESSING_BOT ||
      currentState === SessionState.RESPONDING
    ) {
      logDebug('[Session] In critical state, deferring session close');
      this.pendingClose = true;
      return;
    }

    // If not already disconnecting, transition to DISCONNECTING state
    if (!this.stateManager.isDisconnecting()) {
      // Use void to acknowledge the unhandled promise in close method
      void this.stateManager.setState(SessionState.DISCONNECTING, {
        reason: 'Session close requested',
      });
    }

    logDebug('[Session] Closing session and cleaning up resources');

    // Dispose of audio resources through the adapter
    void this.withErrorHandling(
      async () => {
        logDebug('[Session] Disposing audio resources');
        await this.audioAdapter.dispose();
      },
      '[Session] Error disposing audio resources',
      false
    );

    // Dispose of bot resources through the adapter
    void this.withErrorHandling(
      async () => {
        logDebug('[Session] Disposing bot resources');
        await this.botAdapter.dispose();
      },
      '[Session] Error disposing bot resources',
      false
    );

    // Close the WebSocket connection
    void this.withErrorHandling(
      async () => {
        logDebug('[Session] Closing WebSocketController');
        this.wsController.close();
      },
      '[Session] Error closing WebSocketController',
      false
    );

    // Finalize metrics through the adapter
    void this.withErrorHandling(
      // TODO fix last AI reply not being recorded
      async () => {
        logDebug('[Session] Finalizing metrics');
        this.metricsAdapter.finalizeConversation();

        // Finalize any pending request metrics
        if (this.currentRequest) {
          this.finalizeMetricsRequest();
        }
      },
      '[Session] Error finalizing metrics',
      false
    );

    // Set final state to CLOSED
    // Use void to acknowledge the unhandled promise
    // Use try/catch to handle potential errors during state transition
    try {
      void this.stateManager.setState(SessionState.CLOSED, {
        reason: 'Session resources cleaned up',
      });
    } catch (error) {
      // Log the error but don't throw it to ensure session cleanup completes
      logWarning(
        `[Session] Error transitioning to CLOSED state: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    }

    logDebug('[Session] Session closed successfully');
  }

  /**
   * Set the conversation ID for this session
   * This method is called by the SetConversationIdAction and the OpenMessageHandler
   *
   * @param conversationId The conversation ID
   */
  setConversationId(conversationId: string) {
    this.conversationId = conversationId;

    // Update conversation ID in adapters
    if (this.botAdapter) {
      this.botAdapter.setConversationId(conversationId);
    }

    if (this.metricsAdapter) {
      this.metricsAdapter.setConversationId(conversationId);
    }

    // If the session is already initialized, trigger ASR initialization
    // This handles the case when conversationId is set after session creation
    if (this.stateManager && this.stateManager.getState() === SessionState.IDLE) {
      void this.initializeASRService(conversationId);
    }

    logDebug(`[Session] Set conversation ID: ${conversationId}`);
  }

  /**
   * Initialize the ASR service with the conversation ID and set it on the audio adapter
   * This method is called by the InitializeASRServiceAction
   *
   * @param conversationId The conversation ID to use for ASR
   */
  public async initializeASRService(conversationId: string): Promise<void> {
    try {
      // Create PauseDetectionService to handle pause-based transcript finalization
      const pauseDetectionService = new PauseDetectionService((transcript: Transcript) =>
        this.handleTranscript(transcript, true)
      );
      // Create an EnhancedASRService with transcript and error handlers, routed through pause detection
      const asrService = new EnhancedASRService(
        (transcript: Transcript, isFinal: boolean) =>
          pauseDetectionService.handleTranscript(transcript, isFinal),
        (error: Error | string) => this.handleError(error)
      );

      // Initialize the ASR service with the conversation ID
      await asrService.initialize(conversationId);

      // Set the ASR service on the audio adapter
      this.audioAdapter.setASRService(asrService);

      logDebug('[Session] ASR service initialized and set on audio adapter');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[Session] Failed to initialize ASR service: ${errorMessage}`);
    }
  }

  /**
   * Set the ANI (Automatic Number Identification) for this session
   * This method updates the ANI in the bot adapter
   *
   * @param ani The ANI value
   */
  setClientAni(ani: string | undefined) {
    this.ani = ani;

    // Update ANI in bot adapter
    if (ani) {
      this.botAdapter.setAni(ani);
      logDebug(`[Session] Set ANI: ${ani}`);
    }
  }

  /**
   * Set input variables for this session
   * These variables are used by external systems
   *
   * @param inputVariables The input variables
   */
  setInputVariables(inputVariables: JsonStringMap) {
    this._inputVariables = inputVariables;
  }

  /**
   * Set selected media for this session
   * This is used by external systems
   *
   * @param selectedMedia The selected media
   */
  setSelectedMedia(selectedMedia: MediaParameter) {
    this._selectedMedia = selectedMedia;
  }

  /**
   * Set the playback state
   * This method uses the audio adapter to set the playback state
   *
   * @param state The new playback state
   */
  public setPlaybackState(state: PlaybackState) {
    this.audioAdapter.setPlaybackState(state);
  }

  /**
   * Get the current session state
   * This method returns the current state from the state manager
   *
   * @returns The current session state
   */
  public getSessionState(): SessionState {
    return this.stateManager.getState();
  }

  /**
   * Get the session state manager
   * This method returns the state manager instance
   *
   * @returns The session state manager instance
   */
  public getSessionStateManager(): EventDrivenStateManager {
    return this.stateManager;
  }

  /**
   * Get the event emitter
   * This method returns the event emitter instance
   *
   * @returns The event emitter instance
   */
  public getEventEmitter(): EventEmitter {
    return this.eventEmitter;
  }

  /**
   * Get the metrics adapter
   * This method returns the metrics adapter instance
   *
   * @returns The session metrics adapter instance
   */
  public getMetricsAdapter(): SessionMetricsAdapter {
    return this.metricsAdapter;
  }

  /**
   * Get a request context by its requestId, or undefined if not found.
   */
  public getRequestById(requestId: string): RequestContext | undefined {
    return this.metricsAdapter.getRequestById(requestId);
  }

  /**
   * Enable barge-in detection
   * This method enables barge-in detection in the BargeInManager
   */
  public enableBargeIn(): void {
    this.bargeInManager.enableBargeIn();
    logDebug('[Session] Barge-in detection enabled');
  }

  /**
   * Disable barge-in detection
   * This method disables barge-in detection in the BargeInManager
   */
  public disableBargeIn(): void {
    this.bargeInManager.disableBargeIn();
    logDebug('[Session] Barge-in detection disabled');
  }

  /**
   * Check if barge-in detection is enabled
   * @returns True if barge-in detection is enabled, false otherwise
   */
  public isBargeInEnabled(): boolean {
    return this.bargeInManager.isBargeInEnabled();
  }

  /**
   * Update playback state in the session state machine
   * This method is now a wrapper around setState to maintain backward compatibility
   *
   * @param state The new playback state
   */
  public updatePlaybackStateWithoutTransition(state: PlaybackState): void {
    // Instead of updating BargeInManager, update the session state directly
    if (state === 'playing' && !this.stateManager.isPlaying()) {
      void this.stateManager.setState(SessionState.PLAYING, {
        reason: 'Audio playback state updated to playing',
        playbackStatus: state,
      });
    } else if (state === 'stopped' && this.stateManager.isPlaying()) {
      void this.stateManager.setState(SessionState.IDLE, {
        reason: 'Audio playback state updated to stopped',
        playbackStatus: state,
      });
    }

    // Log the action for debugging
    logDebug(`[Session] Updated playback state to ${state}`);
  }

  /**
   * Send a disconnect message to the client
   *
   * @param reason The reason for disconnection
   * @param info A descriptive message about the disconnect reason
   * @param outputVariables Any output variables to include in the disconnect message
   */
  public sendDisconnect(
    reason: DisconnectReason,
    info: string,
    outputVariables: JsonStringMap
  ): void {
    if (this.stateManager.isDisconnectingOrClosed()) {
      logWarning(
        '[Session] Attempted to send disconnect after session was already disconnecting or closed.'
      );
      return;
    }

    // Transition to DISCONNECTING state with metadata
    // Use void to acknowledge the unhandled promise
    void this.stateManager.setState(SessionState.DISCONNECTING, {
      reason: `Disconnect requested: ${reason}`,
      disconnectReason: reason,
      disconnectInfo: info,
    });

    // Finalize conversation metrics through the metrics adapter
    if (reason === 'completed') {
      logDebug('[Session] Finalizing conversation metrics for completed session');
      this.metricsAdapter.finalizeConversation();
    }

    // Create and send the disconnect message
    const parameters: DisconnectParameters = {
      reason,
      info,
      outputVariables,
    };

    const message = this.createMessage('disconnect', parameters);
    logDebug(`[Session] Sending disconnect message: ${reason} - ${info}`);
    this.send(message);

    // Start the process of closing the session
    setTimeout(() => {
      this.close();
    }, 500);
  }

  /**
   * Process binary audio data from the client
   * This method uses the audio adapter to process the audio data
   *
   * @param data The binary audio data to process
   */
  public async processBinaryMessage(data: Uint8Array): Promise<void> {
    // Use state manager for disconnecting/closed checks
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    // Skip audio processing if DTMF is being captured
    if (this.dtmfManager.isCapturing()) {
      logDebug('[Session] DTMF is being captured, skipping audio processing');
      return;
    }

    // Directly process audio through the audio adapter
    if (this.audioAdapter) {
      const metricsRequest = this.getMetricsAdapter().getCurrentRequest();
      if (metricsRequest) {
        await this.audioAdapter.processAudio(data, metricsRequest);
      } else {
        logError('[Session] No metrics requst available');
      }
    } else {
      logError('[Session] No AudioAdapter available to process audio');
    }
  }

  /**
   * Process text messages from the client
   * This method parses the message and routes it to the appropriate handler
   *
   * @param data The text message data as a string
   */
  public processTextMessage(data: string): void {
    if (this.stateManager.isClosed()) {
      return;
    }

    try {
      const message = JSON.parse(data);

      // Skip logging for ping messages
      if (message.type !== 'ping' || loggingConfig.logPingMessages) {
        logDebug(`Received a ${message.type} message.`);
      }

      if (!message.version) {
        logWarning('Missing version field in message.');
        this.sendDisconnect('error', 'Missing version field in message.', {});
        return;
      }

      if (message.seq !== this.lastClientSequenceNumber + 1) {
        logWarning(`Invalid client sequence number: ${message.seq}.`);
        this.sendDisconnect('error', 'Invalid client sequence number.', {});
        return;
      }

      this.lastClientSequenceNumber = message.seq;

      if (message.serverseq > this.lastServerSequenceNumber) {
        logWarning(`Invalid server sequence number: ${message.serverseq}.`);
        this.sendDisconnect('error', 'Invalid server sequence number.', {});
        return;
      }

      if (message.id !== this.clientSessionId) {
        logWarning(`Invalid Client Session ID: ${message.id}.`);
        this.sendDisconnect('error', 'Invalid ID specified.', {});
        return;
      }

      const handler = this.messageHandlerRegistry.getHandler(message.type);

      if (!handler) {
        logWarning(`Cannot find a message handler for '${message.type}'.`);
        return;
      }

      handler.handleMessage(message as ClientMessage, this);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[Session] Error processing text message: ${errorMessage}`);
      this.sendDisconnect('error', 'Invalid message format', {});
    }
  }

  /**
   * Process a DTMF digit from the client
   * This method uses the DTMF manager to process the digit
   *
   * @param digit The DTMF digit to process
   */
  public processDTMF(digit: string): void {
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    logDebug(`[Session] Processing DTMF digit: ${digit}`);
    this.dtmfManager.processDigit(digit);
  }

  /**
   * Handle a complete DTMF sequence
   * This method is called when the DTMF manager has a complete sequence
   * It delegates to the state machine via state transition
   *
   * @param digits The complete DTMF sequence
   */
  private async handleDTMFComplete(digits: string): Promise<void> {
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    const currentState = this.stateManager.getState();
    if (currentState === SessionState.IDLE) {
      // Route through LISTENING before PROCESSING_INPUT
      await this.stateManager.setState(SessionState.LISTENING, {
        reason: 'DTMF input received, transitioning from IDLE to LISTENING',
        inputType: 'dtmf',
        digits,
      });
      // After transitioning to LISTENING, now transition to PROCESSING_INPUT
      await this.stateManager.setState(SessionState.PROCESSING_INPUT, {
        reason: 'DTMF input received after LISTENING',
        inputType: 'dtmf',
        digits,
      });
    } else {
      await this.stateManager.setState(SessionState.PROCESSING_INPUT, {
        reason: 'DTMF input received',
        inputType: 'dtmf',
        digits,
      });
    }
  }

  /**
   * Handle a transcript from the ASR service
   * This method processes both interim and final transcripts
   * It delegates to the state machine via state transition
   *
   * @param transcript The transcript object
   * @param isFinal Whether this is a final transcript
   */
  private async handleTranscript(transcript: Transcript, isFinal: boolean): Promise<void> {
    logDebug(
      `[Session] handleTranscript called | text="${transcript?.text ?? ''}" | isFinal=${isFinal}`
    );
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    // Check for empty transcript
    if (!transcript.text || transcript.text.trim() === '') {
      return;
    }

    const currentState = this.stateManager.getState();

    // Check for barge-in during PLAYING or RESPONDING states
    // Now using both interim and final transcripts for barge-in detection
    if (currentState === SessionState.PLAYING || currentState === SessionState.RESPONDING) {
      logDebug(`[Session] Barge-in detected during ${currentState} state: "${transcript.text}"`);
      this.bargeInManager.detectAudioBargeIn(transcript.text);

      // After barge-in, transition to LISTENING and let the state machine handle user input injection
      const listeningMetadata = {
        reason: 'Barge-in: transition to LISTENING after barge-in',
        isBargeIn: true,
        bargeInText: transcript.text,
      };
      logInfo(`[Session] Transitioning to LISTENING after barge-in: "${transcript.text}"`);
      await this.stateManager.setState(SessionState.LISTENING, listeningMetadata);
      return;
    }

    // For regular input processing, still only use final transcripts
    if (!isFinal) {
      return;
    }

    // End ASR phase timing when final transcript is processed
    this.metricsAdapter?.endPhase?.('speechToText');

    // Start a new cancellation token for this user turn
    this.startNewTurnAbortController();

    // Only process user input if in IDLE state.
    // Barge-in during PLAYING is detected and routed via BargeInManager, not here.
    // Per state machine architectural guidance, only process transcripts in IDLE.
    // See src/session/state-machine.md section 3 and 4.
    if (currentState !== SessionState.IDLE) {
      logDebug(
        `[Session] Dropping transcript received in state ${currentState}: "${transcript.text}". This is intentional and aligns with state machine best practices (see state-machine.md#3-anti-pattern-forced-transitions).`
      );
      return;
    }

    // Regular transcript processing (IDLE only)
    const processingInputMetadata = {
      reason: 'Speech input received',
      inputType: 'speech',
      transcript,
    };

    logDebug(`[Session] Processing transcript: "${transcript.text}"`);
    // If current state is IDLE, route through LISTENING before PROCESSING_INPUT
    if (currentState === SessionState.IDLE) {
      await this.stateManager.setState(SessionState.LISTENING, {
        reason: 'Speech input received, transitioning from IDLE to LISTENING',
        inputType: 'speech',
        transcript,
      });
      await this.stateManager.setState(SessionState.PROCESSING_INPUT, processingInputMetadata);
    } else {
      await this.stateManager.setState(SessionState.PROCESSING_INPUT, processingInputMetadata);
    }

    // End ASR phase timing after state transition to PROCESSING_INPUT
    this.metricsAdapter?.endPhase?.('speechToText');

    // No longer explicitly transitioning to PROCESSING_BOT here
    // Let the TransitionToBotProcessingAction (exit action) handle the transition
    // after it has received the bot response
    logDebug(`[Session] Finished processing transcript: "${transcript.text}"`);
  }

  /**
   * Handle a barge-in event
   * This method is called when a barge-in is detected
   * It delegates to the state machine via state transition with metadata
   * The actual handling logic is now in HandleBargeInAction
   *
   * @param source The source of the barge-in (speech or dtmf)
   * @param text The text that triggered the barge-in (for speech)
   */
  private async handleBargeIn(source: 'speech' | 'dtmf', text?: string): Promise<void> {
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    // Cancel all in-progress actions for the current turn (ASR/bot/tts/playback)
    this.cancelCurrentTurn();

    const currentState = this.stateManager.getState();
    logDebug(`[Session] Handling barge-in from ${source} in state ${currentState}`);

    // The state transition will trigger the HandleBargeInAction
    // which will handle the barge-in based on the current state
    if (currentState === SessionState.PLAYING) {
      const interruptionMeta = {
        nextState: SessionState.IDLE,
        reason: `Barge-in from ${source}`,
        bargeInSource: source,
        bargeInText: text,
      };
      if (this.stateManager.isStateTransitionInProgress()) {
        this.stateManager.requestInterruption(interruptionMeta);
      } else {
        await this.stateManager.setState(SessionState.IDLE, interruptionMeta);
      }
    } else if (currentState === SessionState.RESPONDING) {
      const interruptionMeta = {
        nextState: SessionState.PROCESSING_INPUT,
        reason: `Barge-in from ${source} during response preparation`,
        bargeInSource: source,
        bargeInText: text,
        isBargeIn: true,
      };
      if (this.stateManager.isStateTransitionInProgress()) {
        this.stateManager.requestInterruption(interruptionMeta);
      } else {
        await this.stateManager.setState(SessionState.PROCESSING_INPUT, interruptionMeta);
      }
    } else {
      logWarning(`[Session] Barge-in received in unexpected state: ${currentState}`);
    }

    logDebug(`[Session] Finished handling barge-in from ${source}`);
  }

  /**
   * Handle an error from any service
   * This method logs the error and disconnects the session if necessary
   *
   * @param error The error object or message
   */
  private handleError(error: Error | string): void {
    const errorMessage = error instanceof Error ? error.message : error;
    logError(`[Session] Error: ${errorMessage}`);

    // Only disconnect if the session is still active
    if (this.stateManager.isActive()) {
      this.sendDisconnect('error', `Internal error: ${errorMessage}`, {});
    }
  }

  /**
   * Send a message to the client
   * This method uses the WebSocket controller to send the message
   *
   * @param message The message to send
   */
  public send(message: ServerMessage): void {
    if (this.stateManager.isDisconnectingOrClosed()) {
      logWarning('[Session] Attempted to send message after session was disconnecting or closed.');
      return;
    }

    this.wsController.send(message);
  }

  /**
   * Send a barge-in event to Genesys Cloud (side effect only, no state transition)
   * @param source The source of the barge-in (speech or dtmf)
   * @param text The text that triggered the barge-in (for speech)
   */
  private sendBargeInEventToGenesys(source: 'speech' | 'dtmf', text?: string): void {
    // Construct the Genesys barge-in event entity
    const bargeInEvent: import('../protocol/voice-bots').EventEntityBargeIn = {
      type: 'barge_in',
      data: {},
    };
    const message = this.createMessage('event', {
      entities: [bargeInEvent],
    });

    logBargeIn(
      `[Session] [Genesys] Sending barge-in event to Genesys Cloud: source=${source}${
        text ? `, text="${text}"` : ''
      }`
    );
    this.send(message);
  }

  /**
   * Create a server message with the appropriate sequence numbers
   * This method creates a message with the correct session ID and sequence numbers
   *
   * @param type The message type
   * @param parameters The message parameters
   * @returns The created message
   */
  public createMessage<T extends ServerMessageType>(
    type: T,
    parameters: Record<string, unknown>
  ): ServerMessage {
    this.lastServerSequenceNumber++;

    // Create the base message with sequence numbers
    const message = {
      version: '2',
      type,
      id: this.clientSessionId,
      seq: this.lastServerSequenceNumber,
      clientseq: this.lastClientSequenceNumber,
    };

    return {
      ...message,
      parameters,
    } as ServerMessage;
  }

  /**
   * Send a bot turn response event to the client
   * This method creates and sends a bot turn response event
   *
   * @param text The text response from the bot
   * @param disposition The disposition of the bot turn
   */
  public sendBotTurnResponse(text: string, disposition: BotTurnDisposition): void {
    if (this.stateManager.isDisconnectingOrClosed()) {
      return;
    }

    // Create and send the event
    const parameters = {
      entity: 'bot-turn-response',
      data: {
        text,
        disposition,
      },
    };

    const message = this.createMessage('event', parameters);
    this.send(message);
  }

  // The checkIfBotExists method has been removed as it's now handled by the BotServiceAdapter

  /**
   * Process the bot start
   * This method is called by the ProcessBotStartAction and the OpenMessageHandler
   * It delegates to the bot adapter to get the initial response
   *
   * @param isInitialGreeting Whether this is the initial greeting (no user input)
   */
  public async processBotStart(isInitialGreeting = false): Promise<void> {
    // For initial greeting, transition directly to PROCESSING_BOT
    if (isInitialGreeting && this.stateManager.getState() === SessionState.IDLE) {
      logDebug('[Session] Processing initial greeting, preparing to transition to PROCESSING_BOT');

      // Ensure metrics context is created and set for the initial greeting
      if (!this.currentRequest) {
        const initialGreetingContext = await this.metricsAdapter.startRequest(
          'Initial greeting',
          true
        );
        this.setCurrentRequest(initialGreetingContext);
      }

      // Get the initial response from the bot adapter, then only update state if session is still active
      const metricsContext = this.currentRequest;
      if (!metricsContext) {
        throw new Error('No metrics context available when processing bot start');
      }
      await this.runIfActiveAfter(
        this.botAdapter.getInitialResponse(metricsContext, undefined),
        async response => {
          if (response) {
            logDebug(
              `[Session] Received bot response (${response.text?.length || 0} chars, ${
                response.audioBytes?.length || 0
              } bytes audio)`
            );
            this.setLatestBotResponse(response);

            // Now transition to PROCESSING_BOT with the isInitialGreeting flag
            await this.stateManager.setState(SessionState.PROCESSING_BOT, {
              reason: 'Processing initial bot greeting',
              isInitialGreeting: true,
              metricsRequestId: this.currentRequest?.requestId,
            });
          } else {
            logDebug('[Session] No bot response received');
          }
        }
      );
      return; // Prevent duplicate state transitions below
    }

    // For non-initial-greeting, do not call getInitialResponse.
    logDebug(
      '[Session] processBotStart called with isInitialGreeting = false; skipping initial greeting logic and not calling getInitialResponse.'
    );
    // All subsequent user input should be handled by normal user input processing, not by processBotStart.
  }
}
