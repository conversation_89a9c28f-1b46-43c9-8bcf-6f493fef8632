# Implementation Prompt for State Machine Pattern

## Task Overview

Implement a true state machine pattern for our session management system following the plan in `src/session/state-machine.md`. The goal is to move business logic from the `Session` class into state-specific actions that execute on state entry and exit.

## Key Requirements

1. Follow the phased approach outlined in `state-machine.md`
2. Prioritize maintainability and clean code over clever optimizations
3. Ensure backward compatibility during the transition
4. Add comprehensive tests for each component
5. Focus on one phase at a time, ensuring it works completely before moving to the next

## Implementation Guidelines

### Code Organization

- Create a new directory structure: `src/session/state-machine/`
- Place all state action interfaces in `src/session/state-machine/interfaces.ts`
- Implement the enhanced state manager in `src/session/state-machine/enhanced-state-manager.ts`
- Group related actions in subdirectories by state or functionality
- Use clear, descriptive naming for all classes and methods

### Coding Standards

- Follow TypeScript best practices with proper typing
- Use async/await for asynchronous operations
- Implement proper error handling with specific error types
- Keep methods small and focused on a single responsibility
- Add JSDoc comments for all public methods and classes
- Use dependency injection to improve testability
- Avoid side effects in state actions when possible

### Testing Approach

- Write unit tests for each action class in isolation
- Create integration tests for state transitions
- Use mocks for external dependencies
- Test both success and error scenarios
- Verify that state transitions trigger the correct actions

## Phase 1 Implementation Details

Start by implementing Phase 1 from the plan:

1. Define the core interfaces for state actions and contexts
2. Create the enhanced state manager that extends the current one
3. Add support for registering and executing entry/exit actions
4. Add unit tests for the enhanced state manager
5. Integrate with the existing state manager without changing behavior

Focus on getting the foundation right before moving to action implementation.

## Example Implementation Structure

```
src/session/
├── state-machine/
│   ├── interfaces.ts                    # Core interfaces
│   ├── enhanced-state-manager.ts        # Enhanced state manager
│   ├── actions/                         # Action implementations
│   │   ├── common/                      # Common actions
│   │   ├── initializing/                # INITIALIZING state actions
│   │   ├── idle/                        # IDLE state actions
│   │   └── ...                          # Other state actions
│   └── tests/                           # Tests for state machine
├── session-state-manager.ts             # Existing state manager
└── session.ts                           # Session class
```

## Specific Implementation Notes

1. The `StateContext` should provide access to all necessary session components
2. Actions should be composable and reusable where possible
3. Consider using a factory pattern for creating state actions
4. Log all state transitions and action executions for debugging
5. Implement a mechanism to handle action failures gracefully

## Deliverables

For Phase 1:

1. Core interfaces for the state machine pattern
2. Enhanced state manager implementation
3. Unit tests for the enhanced state manager
4. Integration with the existing code without changing behavior

## Success Criteria

1. All tests pass
2. No regression in existing functionality
3. Code follows the structure and patterns described in `state-machine.md`
4. Implementation is clean, maintainable, and well-documented

## Additional Resources

- Refer to the existing `SessionStateManager` implementation for current behavior
- Review the state diagram in `current-state-machine-flowchart.md` for state transitions
- Use `src/session/session.ts` to identify business logic that needs to be moved
- Follow patterns in `src/services/barge-in/barge-in-manager.ts` for callback-based APIs
