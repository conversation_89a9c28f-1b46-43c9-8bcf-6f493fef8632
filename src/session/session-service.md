# Session Service: Technical Overview

## Purpose

The Session Service orchestrates the lifecycle of a voice interaction session, coordinating audio, speech recognition, bot logic, DTMF input, barge-in, and communication with clients over WebSocket. Its design emphasizes explicit state management, clear separation of concerns, robust error handling, and extensibility.

---

## Key Responsibilities

- Manage the full lifecycle of a voice session, from initialization to disconnect
- Coordinate audio input/output, speech-to-text (ASR), bot interactions, DTMF, and barge-in detection
- Maintain explicit session state and metrics for observability and debugging
- Handle all client communication via WebSocket, including audio and control messages
- Ensure robust error handling and resource cleanup

---

## Core Components and Their Roles

### Session (`src/session/session.ts`)

- Central coordinator for the session lifecycle
- Delegates responsibilities to specialized adapter components
- Maintains session state, metrics, and context
- Handles high-level error and disconnect logic

### WebSocketController (`src/session/websocket-controller.ts`)

- Encapsulates all WebSocket communication
- Handles sending/receiving messages, audio, and connection lifecycle
- Manages WebSocket state and error handling

### SessionStateManager (`src/session/session-state-manager.ts`)

- Maintains explicit session state with validated transitions
- Provides helper methods for state checks and transitions
- Notifies listeners of state changes
- Tracks state history for debugging

---

## Adapter Components

The Session Service uses the Adapter pattern to delegate responsibilities to specialized components:

### SessionMetricsAdapter

- Handles all metrics tracking for the session
- Integrates with PerformanceLogger
- Tracks request phases and metrics

### BotServiceAdapter

- Handles all bot interactions
- Manages bot selection and initialization
- Processes user input and bot responses

### AudioServiceAdapter

- Handles all audio processing and playback
- Integrates with ASR and TTS services
- Manages audio state and events

---

## Testing and Mocking

- For **unit tests**, use `createMockSession` to create a minimal mock session object with only the required methods/properties.
- For **integration tests**, use `createTestSession` to instantiate a real `Session` with all required dependencies mocked (including WebSocket, BotService, and optional MCP service/tool result setter).
- This approach ensures type safety and realistic behavior in integration tests, while keeping unit tests fast and focused.

**Example:**
```typescript
import { createTestSession } from './test-utils';

const session = createTestSession({
  mcpService: mockMCPService,
  setLastToolResult: jest.fn(),
});
```

---
---
## MCP Integration and Tool-Agnostic Session Design (2025)

### Overview

- The session now supports integration with MCPService for tool invocation (e.g., getDateTime).
- The session interface is extended with `setLastToolResult` and `getLastToolResult` methods.
- Tool results are stored in the session and can be accessed by bot logic or other components.
- This design is tool-agnostic: when new tools (such as customer lookup) become available on the MCP server, they can be integrated with minimal changes.

### Testing and Mocking

- A `createMockSession` factory is provided in `src/session/__tests__/test-utils.ts` for consistent and maintainable session mocking in tests.
- This enables robust unit and integration testing of tool integration, session logic, and bot responses.

### End-to-End Flow

1. Session is initialized with an MCPService dependency.
2. On session start or specific prompt, the session (or OpenMessageHandler) invokes a tool (e.g., getDateTime) via MCPService.
3. The tool result is stored in the session and can be used in bot responses or other logic.
4. All error scenarios (e.g., MCP unavailable) are handled gracefully, with fallback logic as needed.

---
---

## Session States

The Session Service defines explicit states for the session lifecycle:

1. **INITIALIZING**: Session is being initialized
2. **IDLE**: Session is waiting for user input
3. **PROCESSING_INPUT**: Session is processing user input (ASR/DTMF)
4. **PROCESSING_BOT**: Session is waiting for bot response
5. **RESPONDING**: Session is preparing response (TTS)
6. **PLAYING**: Session is playing audio to user
7. **DISCONNECTING**: Session is in the process of disconnecting
8. **CLOSED**: Session is closed and resources released

---

## Component Interactions: Session Lifecycle

1. **Initialization**: Session is created, state is set to INITIALIZING, and all core components are instantiated
2. **Idle/Ready**: Session enters IDLE, awaiting user input
3. **Audio/DTMF Input**: AudioManager and DTMFManager process incoming audio and DTMF events
4. **ASR Processing**: ASRService transcribes audio, emitting transcript events
5. **Transcript Handling**: TranscriptProcessor deduplicates and flags transcripts; Session coordinates further processing
6. **Bot Interaction**: BotService processes user input, generates responses, and manages session flow
7. **Playback/Barge-In**: AudioManager and BargeInManager handle playback and detect barge-in events, allowing interruption and state transitions
8. **Metrics Logging**: PerformanceLogger tracks all phases and metrics for each request
9. **State Management**: SessionStateManager enforces valid state transitions throughout the lifecycle
10. **WebSocket Communication**: WebSocketController manages all client communication, including audio, control, and disconnect messages
11. **Error Handling**: Errors are consistently handled, triggering state transitions and resource cleanup as needed
12. **Session End**: Session transitions to DISCONNECTING and then CLOSED, ensuring all resources are released

---

## Design Principles

- **Explicit State Management**: All session states and transitions are clearly defined and validated
- **Separation of Concerns**: Each component has a focused responsibility, reducing complexity and improving maintainability
- **Adapter Pattern**: Specialized adapters encapsulate specific functionality
- **Consistent Error Handling**: Standardized patterns ensure robust recovery and resource cleanup
- **Metrics and Observability**: All key operations are tracked for performance and debugging
- **Extensibility**: The architecture supports adding new features with minimal impact on existing code

---

## File Structure

- `src/session/session.ts` – Session class (central coordinator)
- `src/session/session-state-manager.ts` – State management logic
- `src/session/websocket-controller.ts` – WebSocket communication
- `src/services/monitoring/session-metrics-adapter.ts` – Metrics adapter
- `src/services/bot-service/bot-service-adapter.ts` – Bot service adapter
- `src/services/audio/audio-service-adapter.ts` – Audio service adapter

---

## Main Benefits

- **Simplified State Management**: Explicit, validated transitions reduce bugs and ambiguity
- **Focused Components**: Each part of the system is easier to understand, test, and extend
- **Cleaner Barge-In Handling**: Barge-in is a clear state transition, not a scattered flag
- **Consistent Error Handling**: Predictable recovery and cleanup across the session lifecycle
- **Improved Testability**: Components can be tested in isolation
- **Reduced Complexity**: The Session class is a coordinator, not a "god object"
- **Better Separation of Concerns**: Each service handles its own domain logic
