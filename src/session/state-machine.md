# State Machine: Event-Driven Architecture

## Core Concepts
- **Transitions** are triggered by events, not direct calls.
- **Single authority:** Only the state manager changes state, based on events.
- **Decoupled:** Components emit/subscribe to events, not each other.

## Key Events & State Transitions

| From            | Event                        | To                |
|-----------------|-----------------------------|-------------------|
| INITIALIZING    | SESSION_INITIALIZED          | IDLE              |
| IDLE            | USER_INPUT_RECEIVED          | LISTENING         |
| IDLE            | INITIAL_GREETING_REQUESTED   | PROCESSING_BOT    |
| LISTENING       | FINAL_TRANSCRIPT_READY       | PROCESSING_INPUT  |
| PROCESSING_INPUT| USER_INPUT_PROCESSED         | PROCESSING_BOT    |
| PROCESSING_BOT  | BOT_RESPONSE_RECEIVED        | RESPONDING        |
| RESPONDING      | RESPONSE_PREPARATION_COMPLETED| PLAYING          |
| PLAYING         | PLAYBACK_COMPLETED           | IDLE              |
| PLAYING         | BARGE_IN_DETECTED            | LISTENING         |
| RESPONDING      | BARGE_IN_DETECTED            | LISTENING         |
| *any*           | SESSION_CLOSE_REQUESTED      | DISCONNECTING     |
| DISCONNECTING   | DISCONNECTION_COMPLETED      | CLOSED            |

## State List
- INITIALIZING: Setup
- IDLE: Waiting for user input
- LISTENING: Actively listening for user input; ASR is running. Entry starts `speechToText` phase, exit ends it. All user input and barge-in transitions route through here.
- PROCESSING_INPUT: User input (transcript/DTMF) is being processed.
- PROCESSING_BOT: Bot/LLM processing.
- RESPONDING: TTS/response prep.
- PLAYING: Audio playback.
- DISCONNECTING: Cleanup.
- CLOSED: Ended.

## Event-Driven Flow
1. Components emit events (e.g., user input, bot response, playback).
2. State manager subscribes, validates, and transitions state.
3. Entry/exit actions are executed per state.

## Example Event Emission
```typescript
eventEmitter.emit(SessionEventType.USER_INPUT_RECEIVED, {
  input: userTranscript,
  inputType: 'speech',
  timestamp: Date.now(),
});
```

## Metrics Integration (Phases)
- `speechToText`: ASR (LISTENING). Phase starts on entry to LISTENING, ends on exit (before PROCESSING_INPUT). Ensures idempotent context handling.
- `llmProcessing`: Bot/LLM (PROCESSING_BOT).
- `textToSpeech`: TTS/playback (RESPONDING/PLAYING).
- Each phase: `metricsAdapter.startPhase/endPhase(phase)`

## Barge-In & Pause
- Barge-in: `BARGE_IN_DETECTED` (from PLAYING/RESPONDING → LISTENING → PROCESSING_INPUT). Ensures ASR phase is started cleanly and metrics are accurate.
- Pause: `PAUSE_DETECTED` (finalizes transcript, triggers USER_INPUT_PROCESSED)

## Mermaid Diagram
```mermaid
flowchart LR
  INIT[INITIALIZING] -->|SESSION_INITIALIZED| IDLE
  IDLE -->|USER_INPUT_RECEIVED| LISTENING
  IDLE -->|INITIAL_GREETING_REQUESTED| PROCESSING_BOT
  LISTENING -->|FINAL_TRANSCRIPT_READY| PROCESSING_INPUT
  PROCESSING_INPUT -->|USER_INPUT_PROCESSED| PROCESSING_BOT
  PROCESSING_BOT -->|BOT_RESPONSE_RECEIVED| RESPONDING
  RESPONDING -->|RESPONSE_PREPARATION_COMPLETED| PLAYING
  PLAYING -->|PLAYBACK_COMPLETED| IDLE
  PLAYING -->|BARGE_IN_DETECTED| LISTENING
  RESPONDING -->|BARGE_IN_DETECTED| LISTENING
  * -->|SESSION_CLOSE_REQUESTED| DISCONNECTING
  DISCONNECTING -->|DISCONNECTION_COMPLETED| CLOSED
