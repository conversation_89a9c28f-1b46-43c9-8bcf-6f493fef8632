# Session Architecture

## Overview

The Session component has been refactored to follow a clean architecture pattern with a state machine at its core. This document explains the new architecture, the responsibilities of each component, and how they interact.

## Architecture Components

### 1. Session Class

The Session class is now a coordinator that:

- Manages the lifecycle of a session
- Delegates specific responsibilities to specialized adapters
- Coordinates state transitions through the state machine
- Handles WebSocket communication with the client

### 2. State Machine

The state machine is implemented through:

- `SessionStateManager`: Base class that manages state transitions and validation
- `EnhancedSessionStateManager`: Extended class that adds support for entry and exit actions
- State actions: Small, focused classes that implement specific behaviors for each state

#### States

The session can be in one of the following states:

- `INITIALIZING`: Session is being initialized
- `IDLE`: Session is waiting for user input
- `PROCESSING_INPUT`: Session is processing user input (ASR/DTMF)
- `PROCESSING_BOT`: Session is waiting for bot response
- `RESPONDING`: Session is preparing response (TTS)
- `PLAYING`: Session is playing audio to user
- `DISCONNECTING`: Session is in the process of disconnecting
- `CLOSED`: Session is closed and resources released

#### State Actions

Each state has associated entry and exit actions:

- Entry actions: Executed when entering a state
- Exit actions: Executed when leaving a state

For example, the `PROCESSING_INPUT` state has:

- Entry actions: `LogStateTransitionAction`, `StartMetricsRequestAction`
- Exit actions: `ProcessUserInputAction`, `TransitionToBotProcessingAction`

### 3. Adapter Services

The Session class delegates specific responsibilities to specialized adapters:

#### SessionMetricsAdapter

- Handles all metrics tracking for the session
- Tracks request phases, durations, and metrics
- Provides methods like `startRequest()`, `startPhase()`, and `logMetricsMessage()`

#### BotServiceAdapter

- Handles all bot interactions
- Manages bot selection, conversation context, and processing user input
- Provides methods like `processUserInput()`, `getInitialResponse()`, and `checkIfBotExists()`

#### AudioServiceAdapter

- Handles all audio processing
- Manages ASR service, audio playback, and DTMF processing
- Provides methods like `processAudio()`, `sendAudio()`, and `setPlaybackState()`

### 4. Session Interface

The `ISession` interface defines the public API of the Session class that is used by state actions. This ensures that state actions only depend on a well-defined interface rather than the concrete Session class.

## Flow of Control

1. **Initialization**:

   - Session is created with a WebSocket connection
   - State manager and adapters are initialized
   - State is set to INITIALIZING, then IDLE

2. **Initial Greeting**:

   - When session is opened, state transitions directly from IDLE to PROCESSING_BOT
   - This special transition is used only for the initial greeting (no user input)
   - Entry actions for PROCESSING_BOT are executed with isInitialGreeting flag
   - Bot provides initial greeting response

3. **Processing User Input**:

   - Audio data is received and processed by the audio adapter
   - When a final transcript is available, state transitions to PROCESSING_INPUT
   - Entry actions for PROCESSING_INPUT are executed
   - User input is processed and sent to the bot
   - Exit actions transition to PROCESSING_BOT

4. **Processing Bot Response**:

   - Entry actions for PROCESSING_BOT are executed
   - Bot response is processed
   - Exit actions handle the response and transition to RESPONDING

5. **Responding to User**:

   - Entry actions for RESPONDING are executed
   - Audio is generated from text if needed
   - Audio is sent to the client
   - State transitions to PLAYING
   - If user starts speaking during response preparation, can transition to PROCESSING_INPUT

6. **Playing Audio**:

   - Entry actions for PLAYING are executed
   - When playback completes, exit actions transition to IDLE

7. **Closing Session**:
   - When session is closed, state transitions to DISCONNECTING
   - Resources are cleaned up
   - State transitions to CLOSED

## Benefits of the New Architecture

1. **Clearer Code Organization**: Business logic is organized around states and adapters
2. **Improved Maintainability**: Each component has a single responsibility
3. **Better Testability**: Components can be tested in isolation
4. **Reduced Complexity**: Session class is a lighter coordinator
5. **More Explicit State Machine**: True state machine pattern implementation
6. **DRY Principles**: Reuse of common functionality through adapters

## Future Improvements

1. **Complete Removal of Duplicated Logic**: Some methods in the Session class still contain logic that could be moved to adapters or state actions
2. **Enhanced Error Handling**: More robust error handling and recovery mechanisms
3. **Performance Optimization**: Profiling and optimizing critical paths
4. **Comprehensive Integration Tests**: Tests that verify the entire session lifecycle
