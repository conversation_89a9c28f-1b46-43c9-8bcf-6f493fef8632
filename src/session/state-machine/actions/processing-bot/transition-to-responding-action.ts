/**
 * TransitionToRespondingAction
 *
 * This action is deprecated and now a no-op.
 * It was previously used to transition from PROCESSING_BOT to RESPONDING state,
 * but this functionality has been moved to HandleBotResponseAction.
 *
 * It is kept for backward compatibility with tests.
 */

import type { StateAction, StateContext } from '../../interfaces';

export class TransitionToRespondingAction implements StateAction {
  /**
   * Execute the action
   * @param context The state context
   * @returns A promise that resolves when the action is complete
   */
  async execute(_context: StateContext): Promise<void> {
    // This action is now a no-op
    // The transition is handled by HandleBotResponseAction
    return;
  }
}
