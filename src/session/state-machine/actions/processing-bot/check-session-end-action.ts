/**
 * Action to check if the session should end after PROCESSING_BOT.
 * Intended for use as an exit action for the PROCESSING_BOT state.
 *
 * @module session/state-machine/actions/processing-bot/check-session-end-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class CheckSessionEndAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session } = context;
    const botResponse = session.getLatestBotResponse();
    if (!botResponse) {
      return;
    }

    // If the bot response indicates the session should end, close the session
    if (botResponse.endSession) {
      session.close();
    }
  }
}
