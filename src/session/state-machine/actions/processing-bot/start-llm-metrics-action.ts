/**
 * Action to start LLM processing metrics for the PROCESSING_BOT state.
 * Intended for use as an entry action for the PROCESSING_BOT state.
 *
 * @module session/state-machine/actions/processing-bot/start-llm-metrics-action
 */

import { logMetrics } from '../../../../services/logging';
import type { StateAction, StateContext } from '../../interfaces';

export class StartLLMMetricsAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session } = context;
    const metricsAdapter = session.getMetricsAdapter?.();
    if (!metricsAdapter) {
      throw new Error(
        'StartLLMMetricsAction requires session.getMetricsAdapter() to return a metrics adapter'
      );
    }

    // End ASR phase timing before starting LLM phase
    const currentRequest = session.getCurrentRequest?.();
    if (currentRequest && !currentRequest.isInitialGreeting) {
      currentRequest.endPhase('speechToText');
      // Use logMetrics for robust, clear logging
      const { requestId } = currentRequest;
      logMetrics(
        `[StartLLMMetricsAction] ASR phase ended (speechToText) for requestId=${requestId}`
      );
    } else {
      logMetrics(
        `[StartLLMMetricsAction] ASR phase NOT ended: no current request or initial greeting`
      );
    }

    // Removed: LLM phase metrics should be started at the true async boundary in the bot service, not in the state machine.
  }
}
