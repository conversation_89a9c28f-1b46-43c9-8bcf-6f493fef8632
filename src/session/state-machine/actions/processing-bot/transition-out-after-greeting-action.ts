import { StateAction, StateContext } from '../../interfaces';
// import { SessionState } from '../../../session-state-manager'; // Unused import removed
import { EventEmitter, EventPriority } from '../../../../services/events/event-emitter';
import { SessionEventType } from '../../../../services/events/session-events';

/**
 * Action to transition out of PROCESSING_BOT after the initial greeting.
 * Emits an event to trigger transition to RESPONDING state.
 */
export class TransitionOutAfterGreetingAction implements StateAction {
  private eventEmitter: EventEmitter;

  constructor(options?: { eventEmitter?: EventEmitter }) {
    // Get the event emitter from options or create a new one
    this.eventEmitter = options?.eventEmitter || new EventEmitter();
  }

  async execute(context: StateContext): Promise<void> {
    const { metadata, session } = context;

    // Only trigger for the initial greeting
    if (metadata?.isInitialGreeting === true) {
      // Extract botResponse from metadata if available
      const botResponse = metadata?.botResponse || session.getLatestBotResponse();

      // Emit event to trigger transition to RESPONDING
      this.eventEmitter.emit(
        SessionEventType.BOT_RESPONSE_RECEIVED,
        {
          response: botResponse || {
            text: '',
            disposition: 'match',
            endSession: false,
          },
          timestamp: Date.now(),
          isInitialGreeting: true,
        },
        EventPriority.HIGH
      );
    }
    // Otherwise, do nothing
  }
}
