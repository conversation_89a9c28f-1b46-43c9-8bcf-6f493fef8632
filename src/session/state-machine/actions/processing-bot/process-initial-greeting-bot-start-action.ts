import type { StateAction, StateContext } from '../../interfaces';
import { logInfo, logError } from '../../../../services/logging/logger';

/**
 * Action to process the initial greeting bot start after metrics context is created.
 * Only runs if isInitialGreeting is set in the state metadata.
 */
export class ProcessInitialGreetingBotStartAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    try {
      const { session, metadata } = context;
      if (metadata?.isInitialGreeting) {
        logInfo('[ProcessInitialGreetingBotStartAction] Processing initial greeting bot start');
        await session.processBotStart(true);
      }
    } catch (err) {
      logError(
        `[ProcessInitialGreetingBotStartAction] Error processing initial greeting bot start: ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
    }
  }
}
