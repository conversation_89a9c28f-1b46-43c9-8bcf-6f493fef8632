/**
 * Action to handle the bot response (audio and text) after PROCESSING_BOT.
 * Intended for use as an exit action for the PROCESSING_BOT state.
 *
 * This action is responsible for:
 * 1. Sending the bot turn response to the client
 * 2. Emitting an event to transition to the RESPONDING state if there is audio or text to play
 *
 * @module session/state-machine/actions/processing-bot/handle-bot-response-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import { SessionState } from '../../../session-state-manager';
import { logDebug } from '../../../../services/logging/logger';
import { EventEmitter, EventPriority } from '../../../../services/events/event-emitter';
import { SessionEventType } from '../../../../services/events/session-events';

export class HandleBotResponseAction implements StateAction {
  private eventEmitter: EventEmitter;

  constructor(options?: { eventEmitter?: EventEmitter }) {
    // Get the event emitter from options or create a new one
    this.eventEmitter = options?.eventEmitter || new EventEmitter();
  }

  async execute(context: StateContext): Promise<void> {
    const { session } = context;
    const botResponse = session.getLatestBotResponse();

    // Simple check for bot response
    if (!botResponse) {
      logDebug('[HandleBotResponseAction] No bot response available');
      return;
    }

    // Ensure the latest bot response is set on the session for playback
    if (typeof session.setLatestBotResponse === 'function') {
      session.setLatestBotResponse(botResponse);
      logDebug('[HandleBotResponseAction] Set latest bot response on session');
    }

    // Send text response if available
    if (botResponse.text && session.sendBotTurnResponse) {
      logDebug(
        `[HandleBotResponseAction] Sending bot turn response: "${botResponse.text.substring(
          0,
          50
        )}${botResponse.text.length > 50 ? '...' : ''}"`
      );
      session.sendBotTurnResponse(botResponse.text, botResponse.disposition);
    }

    // Emit an event to trigger transition to RESPONDING if needed
    if (!botResponse.endSession && (botResponse.audioBytes || botResponse.text)) {
      // Check if we're already transitioning to RESPONDING
      if (context.nextState === SessionState.RESPONDING) {
        logDebug(
          '[HandleBotResponseAction] Already transitioning to RESPONDING, not emitting event'
        );
        return;
      }

      logDebug(
        '[HandleBotResponseAction] Emitting event to request transition to RESPONDING state'
      );
      this.eventEmitter.emit(
        SessionEventType.BOT_RESPONSE_RECEIVED,
        {
          response: {
            text: botResponse.text,
            audioBytes: botResponse.audioBytes,
            disposition: botResponse.disposition,
            endSession: botResponse.endSession,
            endSessionReason: botResponse.endSessionReason,
          },
          timestamp: Date.now(),
        },
        EventPriority.HIGH
      );
    } else {
      logDebug(
        '[HandleBotResponseAction] No content to play or session is ending, not requesting transition to RESPONDING'
      );
    }
    // Finalize metrics for this user interaction
    const metricsAdapter = session.getMetricsAdapter?.();
    if (metricsAdapter && typeof metricsAdapter.finalizeRequest === 'function') {
      // Immediately set the AI reply in the metrics context to avoid race conditions with session cleanup.
      metricsAdapter.setAiReply?.(botResponse.text);
      // Removed endPhase('llmProcessing'); now handled at true async boundary in bot-service.
      void metricsAdapter.finalizeRequest?.(botResponse.text).catch(err => {
        console.error('[Metrics] Error finalizing request:', err);
      });
    }
  }
}
