/**
 * Action to dispose of all session resources (audio, bot, WebSocket, metrics) on DISCONNECTING.
 * Intended for use as an entry action for the DISCONNECTING state.
 *
 * @module session/state-machine/actions/common/dispose-resources-action
 */

import type { StateAction, StateContext } from '../../interfaces';

export class DisposeResourcesAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session } = context;
    await session.disposeAudio();
    await session.disposeBot();
    session.closeWebSocket();
    session.finalizeMetrics();
  }
}
