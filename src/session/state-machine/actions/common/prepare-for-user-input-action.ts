/**
 * Action to prepare the session for new user input (e.g., after playback).
 * Intended for use as an entry action for the IDLE state.
 *
 * @module session/state-machine/actions/common/prepare-for-user-input-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import { ASRServiceNotReadyError } from '../../../../services/audio/errors';
import { logError } from '../../../../services/logging/logger';

export class PrepareForUserInputAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    // No longer arms ASR here; arming is now handled in StartListeningAction to ensure correct ordering after barge-in.
    // This action is now a no-op, but retained for state machine compatibility.
    return;
  }
}
