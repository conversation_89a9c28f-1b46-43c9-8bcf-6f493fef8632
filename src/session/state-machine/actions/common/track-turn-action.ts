import type { StateAction, StateContext } from '../../interfaces';
import { logError, logMetrics } from '../../../../services/logging/logger';

/**
 * Action to start or end the turn timer for a request.
 * Use this to measure the total turn duration (from LISTENING to PLAYING).
 */
export class TrackTurnAction implements StateAction {
  private readonly operation: 'start' | 'end';

  constructor(operation: 'start' | 'end') {
    this.operation = operation;
  }

  async execute(context: StateContext): Promise<void> {
    try {
      const { session } = context;
      const metricsContext = session.getCurrentRequest?.();
      if (!metricsContext) {
        logError(
          `[TrackTurnAction] No metrics context available, cannot ${this.operation} turn timer`
        );
        return;
      }

      if (this.operation === 'start') {
        metricsContext.startTurnTimer?.();
        logMetrics(
          `[TrackTurnAction] Turn timer started for requestId=${metricsContext.requestId}`
        );
      } else {
        metricsContext.endTurnTimer?.();
        logMetrics(`[TrackTurnAction] Turn timer ended for requestId=${metricsContext.requestId}`);
      }
    } catch (err) {
      logError(
        `[TrackTurnAction] Error during ${this.operation} turn timer: ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
    }
  }
}
