import type { StateAction, StateContext } from '../../interfaces';

/**
 * A no-operation action that does nothing.
 * Useful as a placeholder or for testing state transitions.
 */
export class NoOpAction implements StateAction {
  /**
   * Executes the no-op action.
   * @param _context The state context (unused)
   */
  async execute(_context: StateContext): Promise<void> {
    // Intentionally does nothing
  }
}
