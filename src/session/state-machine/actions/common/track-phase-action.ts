import type { StateAction, StateContext } from '../../interfaces';
import { logMetrics, logError } from '../../../../services/logging/logger';

/**
 * Generic action to start or end a metrics phase (speechToText, llmProcessing, textToSpeech).
 * Use this for all phase tracking in the state machine for clarity and maintainability.
 */
export class TrackPhaseAction implements StateAction {
  private readonly phase: 'speechToText' | 'llmProcessing' | 'textToSpeech';
  private readonly operation: 'start' | 'end';

  constructor(
    phase: 'speechToText' | 'llmProcessing' | 'textToSpeech',
    operation: 'start' | 'end'
  ) {
    this.phase = phase;
    this.operation = operation;
  }

  async execute(context: StateContext): Promise<void> {
    try {
      const { session } = context;
      const metricsContext = session.getCurrentRequest?.();
      if (!metricsContext) {
        logError(
          `[TrackPhaseAction] No metrics context available, cannot ${this.operation} phase "${this.phase}"`
        );
        return;
      }

      if (this.operation === 'start') {
        metricsContext.trackPhase?.(this.phase);
        logMetrics(
          `[TrackPhaseAction] Phase "${this.phase}" started for requestId=${metricsContext.requestId}`
        );
      } else {
        metricsContext.endPhase?.(this.phase);
        logMetrics(
          `[TrackPhaseAction] Phase "${this.phase}" ended for requestId=${metricsContext.requestId}`
        );
      }
    } catch (err) {
      logError(
        `[TrackPhaseAction] Error during ${this.operation} phase "${this.phase}": ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
    }
  }
}
