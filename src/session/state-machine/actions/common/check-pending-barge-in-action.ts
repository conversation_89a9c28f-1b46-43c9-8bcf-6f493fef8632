/**
 * Action to check for a pending barge-in and immediately transition to LISTENING if set.
 * This must be the first entry action for the IDLE state.
 *
 * @module session/state-machine/actions/common/check-pending-barge-in-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import { SessionState } from '../../../session-state-manager';
import { logBargeIn, logInfo, logWarning } from '../../../../services/logging/logger';

export class CheckPendingBargeInAction implements StateAction {
  async execute(context: StateContext & { stateManager: any }): Promise<void> {
    logBargeIn('[CheckPendingBargeInAction] Executing pending barge-in check for IDLE state');
    if (context.stateManager.isPendingBargeInToListening()) {
      logBargeIn(
        '[CheckPendingBargeInAction] Pending barge-in detected, scheduling transition to LISTENING (non-blocking)'
      );
      context.stateManager.clearPendingBargeInToListening();

      // Schedule the transition asynchronously to avoid mutex deadlock
      setImmediate(() => {
        context.stateManager.setState(SessionState.LISTENING, {
          reason: 'Barge-in: transition to LISTENING after IDLE',
          isBargeIn: true,
          ...(context.metadata && context.metadata.bargeInText
            ? { bargeInText: context.metadata.bargeInText }
            : {}),
        });
      });
    }
    // Do NOT attempt to transition to PROCESSING_INPUT here; this must be done from LISTENING entry.
  }
}
