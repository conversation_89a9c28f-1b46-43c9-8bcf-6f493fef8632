import type { StateAction, StateContext } from '../../interfaces';
import { logInfo, logWarning } from '../../../../services/logging/logger';

/**
 * Action to set the conversation ID on the session and log the operation.
 * Intended for use as an entry action for the INITIALIZING state.
 *
 * NOTE: Updating adapters and loggers should be handled by dedicated actions or
 * via public methods in a future refactor phase.
 */
export class SetConversationIdAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    // Check if conversationId is in metadata
    const conversationId = context.metadata?.conversationId;

    // If no conversationId in metadata, check if it's already set on the session
    if (!conversationId) {
      // Get the conversationId from the session if available
      const sessionConversationId = context.session.getConversationId?.();

      if (sessionConversationId) {
        logInfo(
          `[SetConversationIdAction] Using existing conversation ID: ${sessionConversationId}`
        );
        return;
      }

      // If no conversationId is available, log a warning but don't throw an error
      // This allows the session to continue initializing
      logWarning('[SetConversationIdAction] No conversation ID available in metadata or session');
      return;
    }

    // Set the conversationId on the session
    context.session.setConversationId(conversationId as string);
  }
}
