import type { StateAction, StateContext } from '../../interfaces';
import { logDebug, logMetrics, logError } from '../../../../services/logging/logger';

/**
 * Action to start the ASR (speechToText) phase on entry to the LISTENING state.
 * Ensures the phase is started exactly once per utterance, idempotently.
 */
export class StartListeningAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    try {
      const { session } = context;
      const metricsContext = session.getCurrentRequest?.();
      if (!metricsContext) {
        logError('[StartListeningAction] No metrics context available, cannot start ASR phase');
        return;
      }

      // Start the ASR phase for this request (idempotent: underlying implementation guards against double-starts)
      metricsContext.trackPhase?.('speechToText');
      logMetrics(
        `[StartListeningAction] ASR phase started (speechToText) for requestId=${metricsContext.requestId}`
      );

      // Arm the ASR/audio pipeline for new user input (moved from PrepareForUserInputAction)
      await session.prepareForUserInput();
      logDebug('[StartListeningAction] ASR/audio pipeline armed for new user input');
    } catch (err) {
      logError(
        `[StartListeningAction] Error starting ASR phase or arming ASR: ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
    }
  }
}
