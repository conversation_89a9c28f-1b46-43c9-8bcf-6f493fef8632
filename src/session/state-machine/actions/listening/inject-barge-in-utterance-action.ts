import type { StateAction, StateContext } from '../../interfaces';
import { SessionState } from '../../../session-state-manager';
import { logBargeIn } from '../../../../services/logging/logger';
import { EventDrivenStateManager } from '../../event-driven-state-manager';

/**
 * Entry action for LISTENING: if a barge-in utterance is present, inject it as user input.
 */
export class InjectBargeInUtteranceAction implements StateAction {
  async execute(context: StateContext & { stateManager: EventDrivenStateManager }): Promise<void> {
    const currentState = context.stateManager.getState();
    // Only inject if in LISTENING and bargeInText is present
    if (currentState !== SessionState.LISTENING) {
      return;
    }
    if (context.metadata && context.metadata.bargeInText) {
      const bargeInText = context.metadata.bargeInText;
      setImmediate(() => {
        context.stateManager.setState(SessionState.PROCESSING_INPUT, {
          reason: 'Speech input received after barge-in',
          inputType: 'speech',
          transcript: {
            text: bargeInText,
          },
          isBargeIn: true,
        });
        if (context.metadata) {
          context.metadata.bargeInText = undefined;
        }
      });
    }
  }
}
