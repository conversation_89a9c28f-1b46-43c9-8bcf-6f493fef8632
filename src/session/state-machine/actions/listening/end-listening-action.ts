import type { StateAction, StateContext } from '../../interfaces';
import { logMetrics, logError } from '../../../../services/logging/logger';

/**
 * Action to end the ASR (speechToText) phase on exit from the LISTENING state.
 * Ensures the phase is ended exactly once per utterance, idempotently.
 */
export class EndListeningAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    try {
      const { session } = context;
      const metricsContext = session.getCurrentRequest?.();
      if (!metricsContext) {
        logError('[EndListeningAction] No metrics context available, cannot end ASR phase');
        return;
      }

      metricsContext.endPhase?.('speechToText');
      logMetrics(
        `[EndListeningAction] ASR phase ended (speechToText) for requestId=${metricsContext.requestId}`
      );
    } catch (err) {
      logError(
        `[EndListeningAction] Error ending ASR phase: ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
    }
  }
}
