/**
 * Action to transition to IDLE state when playback stops.
 * Intended for use as an exit action for the PLAYING state.
 *
 * @module session/state-machine/actions/audio/transition-to-idle-on-playback-stopped-action
 */

import type { StateAction, StateContext } from '../../interfaces';
import { SessionState } from '../../../session-state-manager';
import { logDebug } from '../../../../services/logging/logger';

export class TransitionToIdleOnPlaybackStoppedAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    const { session, metadata } = context;
    // If playback has stopped, transition to IDLE
    if (
      typeof session.getSessionState === 'function' &&
      session.getSessionState() === SessionState.PLAYING
    ) {
      // Transition to IDLE state
      const stateManager = session.getSessionStateManager();
      if (stateManager) {
        await stateManager.setState(SessionState.IDLE, {
          reason: 'Audio playback stopped',
        });

        // (Removed: pendingBargeIn check and transition to LISTENING. This is now handled exclusively by CheckPendingBargeInAction in IDLE entry actions.)
      }
    }

    // Clear the latest bot response after playback completes to prevent repeated playback
    if (typeof session.setLatestBotResponse === 'function') {
      session.setLatestBotResponse(undefined);
    }
  }
}
