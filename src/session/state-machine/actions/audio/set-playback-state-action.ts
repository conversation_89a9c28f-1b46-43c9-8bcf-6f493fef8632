import type { StateAction, StateContext } from '../../interfaces';
import type { PlaybackState } from '../../../../services/barge-in/barge-in-manager';

/**
 * Action to set the playback state for the session.
 * Intended for use as an entry or exit action for audio-related states.
 */
export class SetPlaybackStateAction implements StateAction {
  constructor(private readonly playbackState: PlaybackState) {}

  /**
   * Executes the playback state change.
   * @param context The state context (must provide session)
   */
  async execute(context: StateContext): Promise<void> {
    // The ISession interface guarantees setPlaybackState exists
    // This check is only needed for tests or if the interface changes
    if (typeof context.session.setPlaybackState !== 'function') {
      throw new Error('Session does not implement setPlaybackState');
    }
    context.session.setPlaybackState(this.playbackState);
  }
}
