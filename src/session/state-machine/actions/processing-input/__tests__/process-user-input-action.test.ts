/**
 * Tests for ProcessUserInputAction
 */

import { ProcessUserInputAction } from '../process-user-input-action';
import { SessionState } from '../../../../session-state-manager';
import type { StateContext } from '../../../interfaces';
import { EventEmitter } from '../../../../../services/events/event-emitter';

// Mock the logging module
jest.mock('../../../../../services/logging/logger', () => ({
  logInfo: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('ProcessUserInputAction', () => {
  it('processes speech input', async () => {
    // Create mock bot adapter
    const mockBotAdapter = {
      processUserInput: jest.fn().mockResolvedValue({ success: true }),
    };

    // Create mock session
    const mockSession = {
      getBotAdapter: jest.fn().mockReturnValue(mockBotAdapter),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
      setCurrentProcessingAbortController: jest.fn(),
    };

    // Create mock context with speech input
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();

    // Create and execute the action
    const action = new ProcessUserInputAction({ eventEmitter: mockEventEmitter });
    await action.execute(context);

    // Verify processUserInput was called with the transcript object and metrics context
    expect(mockBotAdapter.processUserInput).toHaveBeenCalledWith(
      { text: 'Hello' },
      { requestId: 'test-request-id' },
      expect.any(Object) // AbortSignal
    );
  });

  it('processes DTMF input', async () => {
    // Create mock bot adapter
    const mockBotAdapter = {
      processUserInput: jest.fn().mockResolvedValue({ success: true }),
    };

    // Create mock session
    const mockSession = {
      getBotAdapter: jest.fn().mockReturnValue(mockBotAdapter),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
      setCurrentProcessingAbortController: jest.fn(),
    };

    // Create mock context with DTMF input
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'dtmf',
        digits: '123',
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();

    // Create and execute the action
    const action = new ProcessUserInputAction({ eventEmitter: mockEventEmitter });
    await action.execute(context);

    // DTMF input processing is not yet implemented, so processUserInput should not be called
    expect(mockBotAdapter.processUserInput).not.toHaveBeenCalled();
  });

  it('handles errors gracefully', async () => {
    // Create mock bot adapter that throws an error
    const mockBotAdapter = {
      processUserInput: jest.fn().mockRejectedValue(new Error('Test error')),
    };

    // Create mock session
    const mockSession = {
      getBotAdapter: jest.fn().mockReturnValue(mockBotAdapter),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
      setCurrentProcessingAbortController: jest.fn(),
    };

    // Create mock context with speech input
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();

    // Create and execute the action
    const action = new ProcessUserInputAction({ eventEmitter: mockEventEmitter });

    // This should not throw an error
    await expect(action.execute(context)).resolves.not.toThrow();

    // Verify the error was logged
    const { logError } = require('../../../../../services/logging/logger');
    expect(logError).toHaveBeenCalledWith(
      expect.stringContaining('Error in asynchronous bot processing')
    );
  });
});
