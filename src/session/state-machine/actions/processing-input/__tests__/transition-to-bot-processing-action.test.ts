/**
 * Tests for TransitionToBotProcessingAction
 */

import { TransitionToBotProcessingAction } from '../transition-to-bot-processing-action';
import { SessionState } from '../../../../session-state-manager';
import type { StateContext } from '../../../interfaces';
import { EventEmitter } from '../../../../../services/events/event-emitter';
import { SessionEventType } from '../../../../../services/events/session-events';

// Mock the logging module
jest.mock('../../../../../services/logging/logger', () => ({
  logInfo: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('TransitionToBotProcessingAction', () => {
  it('transitions to PROCESSING_BOT state', async () => {
    // Create mock state manager
    const mockStateManager = {
      setState: jest.fn().mockResolvedValue(undefined),
    };

    // Create mock session
    const mockSession = {
      getSessionStateManager: jest.fn().mockReturnValue(mockStateManager),
      getBotAdapter: jest.fn().mockReturnValue({
        processUserInput: jest.fn().mockResolvedValue({
          success: true,
          response: { text: 'Bot response' },
        }),
      }),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
    };

    // Create mock context
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();
    jest.spyOn(mockEventEmitter, 'emit');

    // Create and execute the action
    const action = new TransitionToBotProcessingAction({ eventEmitter: mockEventEmitter });
    await action.execute(context);

    // Verify the event emitter was called with the right parameters
    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      SessionEventType.BOT_PROCESSING_REQUESTED,
      expect.objectContaining({
        inputType: 'speech',
        input: { text: 'Hello' },
      }),
      expect.any(Number)
    );

    // Verify setLatestBotResponse was called
    expect(mockSession.setLatestBotResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        text: 'Bot response',
      })
    );
  });

  it('includes metadata in the transition', async () => {
    // Create mock state manager
    const mockStateManager = {
      setState: jest.fn().mockResolvedValue(undefined),
    };

    // Create mock session
    const mockSession = {
      getSessionStateManager: jest.fn().mockReturnValue(mockStateManager),
      getBotAdapter: jest.fn().mockReturnValue({
        processUserInput: jest.fn().mockResolvedValue({
          success: true,
          response: { text: 'Bot response' },
        }),
      }),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
    };

    // Create mock context with additional metadata
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
        requestId: 'test-request-id',
        customField: 'custom-value',
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();
    jest.spyOn(mockEventEmitter, 'emit');

    // Create and execute the action
    const action = new TransitionToBotProcessingAction({ eventEmitter: mockEventEmitter });
    await action.execute(context);

    // Verify the event emitter was called with the right parameters including the additional metadata
    expect(mockEventEmitter.emit).toHaveBeenCalledWith(
      SessionEventType.BOT_PROCESSING_REQUESTED,
      expect.objectContaining({
        inputType: 'speech',
        input: { text: 'Hello' },
        requestId: 'test-request-id',
      }),
      expect.any(Number)
    );

    // Verify setLatestBotResponse was called
    expect(mockSession.setLatestBotResponse).toHaveBeenCalledWith(
      expect.objectContaining({
        text: 'Bot response',
      })
    );
  });

  it('handles errors gracefully', async () => {
    // Create mock state manager that throws an error
    const mockStateManager = {
      setState: jest.fn().mockRejectedValue(new Error('Test error')),
    };

    // Create mock session
    const mockSession = {
      getSessionStateManager: jest.fn().mockReturnValue(mockStateManager),
      getBotAdapter: jest.fn().mockReturnValue({
        processUserInput: jest.fn().mockResolvedValue({
          success: true,
          response: { text: 'Bot response' },
        }),
      }),
      getCurrentRequest: jest.fn().mockReturnValue({ requestId: 'test-request-id' }),
      setLatestBotResponse: jest.fn(),
    };

    // Create mock context
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
      },
      session: mockSession as any,
    };

    // Create a mock event emitter
    const mockEventEmitter = new EventEmitter();
    jest.spyOn(mockEventEmitter, 'emit');

    // Create and execute the action
    const action = new TransitionToBotProcessingAction({ eventEmitter: mockEventEmitter });

    // Mock the bot adapter to throw an error
    mockSession.getBotAdapter().processUserInput.mockRejectedValue(new Error('Test error'));

    // This should not throw an error
    await expect(action.execute(context)).resolves.not.toThrow();

    // Verify the error was logged
    const { logWarning } = require('../../../../../services/logging/logger');
    expect(logWarning).toHaveBeenCalledWith(expect.stringContaining('Error:'));
  });
});
