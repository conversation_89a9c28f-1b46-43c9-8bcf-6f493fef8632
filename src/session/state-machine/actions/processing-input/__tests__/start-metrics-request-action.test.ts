/**
 * Tests for StartMetricsRequestAction
 */

import { StartMetricsRequestAction } from '../start-metrics-request-action';
import { SessionState } from '../../../../session-state-manager';
import type { StateContext } from '../../../interfaces';
import type { SessionMetricsAdapter } from '../../../../../services/monitoring/session-metrics-adapter';

// Mock the logging module
jest.mock('../../../../../services/logging/logger', () => ({
  logInfo: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('StartMetricsRequestAction', () => {
  it('starts metrics tracking for speech input', async () => {
    // Create mock metrics adapter
    const mockMetricsAdapter = {
      startRequest: jest.fn().mockReturnValue({ setUserInput: jest.fn() }),
      startPhase: jest.fn(),
      endPhase: jest.fn(),
      setUserInput: jest.fn(),
      setAiReply: jest.fn(),
      finalizeRequest: jest.fn(),
    } as unknown as SessionMetricsAdapter;

    // Create mock session
    const mockSession = {
      getMetricsAdapter: jest.fn().mockReturnValue(mockMetricsAdapter),
      setCurrentRequest: jest.fn(),
    };

    // Create mock context with speech input
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'speech',
        transcript: { text: 'Hello' },
      },
      session: mockSession as any,
    };

    // Create and execute the action
    const action = new StartMetricsRequestAction();
    await action.execute(context);

    // Verify startRequest was called with the transcript text and speech flag
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledWith('Hello', true);
    expect(mockSession.setCurrentRequest).toHaveBeenCalledWith(
      expect.objectContaining({ setUserInput: expect.any(Function) })
    );
  });

  it('starts metrics tracking for DTMF input', async () => {
    // Create mock metrics adapter
    const mockMetricsAdapter = {
      startRequest: jest.fn().mockReturnValue({ setUserInput: jest.fn() }),
      startPhase: jest.fn(),
      endPhase: jest.fn(),
      setUserInput: jest.fn(),
      setAiReply: jest.fn(),
      finalizeRequest: jest.fn(),
    } as unknown as SessionMetricsAdapter;

    // Create mock session
    const mockSession = {
      getMetricsAdapter: jest.fn().mockReturnValue(mockMetricsAdapter),
      setCurrentRequest: jest.fn(),
    };

    // Create mock context with DTMF input
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        inputType: 'dtmf',
        digits: '123',
      },
      session: mockSession as any,
    };

    // Create and execute the action
    const action = new StartMetricsRequestAction();
    await action.execute(context);

    // Verify startRequest was called with the DTMF digits and speech flag false
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledWith('123', false);
    expect(mockSession.setCurrentRequest).toHaveBeenCalledWith(
      expect.objectContaining({ setUserInput: expect.any(Function) })
    );
  });

  it('handles initial connection', async () => {
    // Create mock metrics adapter
    const mockMetricsAdapter = {
      startRequest: jest.fn().mockReturnValue({ setUserInput: jest.fn() }),
      startPhase: jest.fn(),
      endPhase: jest.fn(),
      setUserInput: jest.fn(),
      setAiReply: jest.fn(),
      finalizeRequest: jest.fn(),
    } as unknown as SessionMetricsAdapter;

    // Create mock session
    const mockSession = {
      getMetricsAdapter: jest.fn().mockReturnValue(mockMetricsAdapter),
      setCurrentRequest: jest.fn(),
    };

    // Create mock context for initial connection
    const context: StateContext = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {
        reason: 'Processing initial connection',
      },
      session: mockSession as any,
    };

    // Create and execute the action
    const action = new StartMetricsRequestAction();
    await action.execute(context);

    // Verify startRequest was called with the initial greeting text and speech flag undefined
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledWith('Initial greeting', true);
    expect(mockSession.setCurrentRequest).toHaveBeenCalledWith(
      expect.objectContaining({ setUserInput: expect.any(Function) })
    );
  });
});
