/**
 * Integration tests for ASR measurement logic across user turns.
 * Covers: multiple turns, error/fallback, and double/missed finalization.
 */

import { StartMetricsRequestAction } from '../start-metrics-request-action';
import { SessionState } from '../../../../session-state-manager';
import { HandleBotResponseAction } from '../../processing-bot/handle-bot-response-action';
import { createMockSession } from '../../../__tests__/test-utils';

describe('ASR Measurement Integration', () => {
  let mockMetricsAdapter: any;
  let session: any;
  let context: any;

  beforeEach(() => {
    mockMetricsAdapter = {
      startRequest: jest.fn().mockReturnValue({
        setUserInput: jest.fn(),
        trackPhase: jest.fn(),
        endPhase: jest.fn(),
      }),
      finalizeRequest: jest.fn().mockResolvedValue(undefined),
    };
    session = createMockSession();
    session.getMetricsAdapter = jest.fn(() => mockMetricsAdapter);
    session.setCurrentRequest = jest.fn();
    session.getAudioAdapter = jest.fn(); // Patch for ISession interface
    context = {
      state: SessionState.PROCESSING_INPUT,
      metadata: {},
      session,
    };
  });

  function simulateUserTurn(inputText = 'Hello', botReply = 'Hi there') {
    // User input phase
    context.metadata = {
      inputType: 'speech',
      transcript: { text: inputText },
    };
    const startAction = new StartMetricsRequestAction();
    // Bot response phase: set up session to return the bot reply
    session.getLatestBotResponse = jest.fn(() => ({ text: botReply }));
    const botContext = {
      ...context,
      state: SessionState.PROCESSING_BOT,
      metadata: { ...context.metadata },
    };
    const botAction = new HandleBotResponseAction();
    return startAction.execute(context).then(() => botAction.execute(botContext));
  }

  it('should start and finalize ASR metrics for each user turn', async () => {
    await simulateUserTurn('Hello', 'Hi');
    await simulateUserTurn('How are you?', 'I am fine.');
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledTimes(2);
    expect(mockMetricsAdapter.finalizeRequest).toHaveBeenCalledTimes(2);
    expect(mockMetricsAdapter.startRequest).toHaveBeenNthCalledWith(1, 'Hello', true);
    expect(mockMetricsAdapter.startRequest).toHaveBeenNthCalledWith(2, 'How are you?', true);
    expect(mockMetricsAdapter.finalizeRequest).toHaveBeenNthCalledWith(1, 'Hi');
    expect(mockMetricsAdapter.finalizeRequest).toHaveBeenNthCalledWith(2, 'I am fine.');
  });

  it('should start ASR metrics for DTMF input with correct arguments', async () => {
    context.metadata = {
      inputType: 'dtmf',
      digits: '1234',
    };
    const startAction = new StartMetricsRequestAction();
    await startAction.execute(context);
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledWith('1234', false);
  });

  it('should start ASR metrics for initial greeting with correct arguments', async () => {
    context.metadata = {
      reason: 'Processing initial connection',
    };
    const startAction = new StartMetricsRequestAction();
    await startAction.execute(context);
    expect(mockMetricsAdapter.startRequest).toHaveBeenCalledWith('Initial greeting', true);
  });

  it('should not double-finalize or miss finalization in normal flow', async () => {
    await simulateUserTurn('Test1', 'Bot1');
    await simulateUserTurn('Test2', 'Bot2');
    // No extra calls
    expect(mockMetricsAdapter.finalizeRequest).toHaveBeenCalledTimes(2);
    // No double calls per turn
    expect(mockMetricsAdapter.finalizeRequest.mock.calls[0][0]).toBe('Bot1');
    expect(mockMetricsAdapter.finalizeRequest.mock.calls[1][0]).toBe('Bot2');
  });

  it('should handle errors in finalizeRequest and continue', async () => {
    // Make finalizeRequest throw on first call
    mockMetricsAdapter.finalizeRequest
      .mockRejectedValueOnce(new Error('fail'))
      .mockResolvedValueOnce(undefined);
    await simulateUserTurn('ErrorTest', 'BotError');
    await simulateUserTurn('NextTest', 'BotNext');
    // Both turns should still call finalizeRequest
    expect(mockMetricsAdapter.finalizeRequest).toHaveBeenCalledTimes(2);
    expect(mockMetricsAdapter.finalizeRequest.mock.calls[0][0]).toBe('BotError');
    expect(mockMetricsAdapter.finalizeRequest.mock.calls[1][0]).toBe('BotNext');
  });

  it('should not finalize if startRequest was never called (missing input)', async () => {
    // Remove inputType to simulate missing input
    context.metadata = {};
    const startAction = new StartMetricsRequestAction();
    await expect(startAction.execute(context)).rejects.toThrow();
    expect(mockMetricsAdapter.startRequest).not.toHaveBeenCalled();
    expect(mockMetricsAdapter.finalizeRequest).not.toHaveBeenCalled();
  });
});
