import { ISession } from '../../../session-interface';
import type { StateAction, StateContext } from '../../interfaces';
import type { Transcript } from '../../../../services/speech/base/types';
import {
  logDebug,
  logError,
  logMetrics,
  logWarning,
  prepareMetadataForLogging,
} from '../../../../services/logging/logger';
import { EventEmitter, EventPriority } from '../../../../services/events/event-emitter';
import { SessionEventType } from '../../../../services/events/session-events';

/**
 * Action to process user input (speech) through the bot adapter.
 * Intended for use as an entry action for the PROCESSING_INPUT state.
 *
 * Expects context.metadata to contain one of:
 *   - inputType: 'speech' and transcript (as a Transcript object)
 *   - reason: 'Processing initial connection' (for initial bot greeting)
 */
export class ProcessUserInputAction implements StateAction {
  private eventEmitter: EventEmitter;

  constructor(options?: { eventEmitter?: EventEmitter }) {
    // Get the event emitter from options or create a new one
    this.eventEmitter = options?.eventEmitter || new EventEmitter();
  }

  async execute(context: StateContext): Promise<void> {
    // Log the metadata object (excluding binary data)
    const loggableMetadata = prepareMetadataForLogging(context.metadata);
    logDebug(
      `[ProcessUserInputAction] Received context metadata: ${JSON.stringify(loggableMetadata)}`
    );

    const { inputType, transcript, reason } = context.metadata || {};
    const session = context.session as ISession;
    const botAdapter = session.getBotAdapter();
    const metricsContext = session.getCurrentRequest();

    // Start the speechToText phase at the beginning of user input processing
    const metricsAdapter = session.getMetricsAdapter?.();
    // Removed speechToText phase timing; now handled at true async boundary in audio-service-adapter.ts

    try {
      // Special case for initial connection (no user input yet)
      if (reason === 'Processing initial connection') {
        logDebug(
          '[ProcessUserInputAction] Processing initial connection - skipping user input processing'
        );
        return;
      }

      // Strict validation: must have a transcript with text for speech input
      if (inputType === 'speech') {
        if (!transcript || typeof (transcript as Transcript).text !== 'string') {
          logError(
            '[ProcessUserInputAction] ERROR: Missing or invalid transcript for speech input. Metadata: ' +
              JSON.stringify(loggableMetadata)
          );
          return;
        }

        const transcriptObj = transcript as Transcript;
        logDebug(`[ProcessUserInputAction] Processing speech input: "${transcriptObj.text}"`);
        // End the speechToText phase after final transcript is processed

        // Start bot processing asynchronously without blocking the state transition
        if (botAdapter) {
          try {
            // Create an AbortController for cancellation support
            const abortController = new AbortController();

            // Store the AbortController on the session for potential cancellation (e.g., barge-in)
            if (
              typeof session.setCurrentProcessingAbortController === 'function' &&
              session.setCurrentProcessingAbortController !== undefined
            ) {
              session.setCurrentProcessingAbortController(abortController);
            }

            logDebug('[ProcessUserInputAction] Starting asynchronous bot processing');

            // Start processing in the background without awaiting the result
            // This prevents blocking the state transition
            void (async () => {
              try {
                const result = await botAdapter.processUserInput(
                  transcriptObj,
                  metricsContext,
                  abortController.signal
                );

                // Only store the result if not aborted
                if (!abortController.signal.aborted) {
                  if (result.success && result.response) {
                    logDebug(
                      `[ProcessUserInputAction] Received bot response asynchronously (${
                        result.response.text?.length || 0
                      } chars, ${result.response.audioBytes?.length || 0} bytes audio)`
                    );

                    // Store the bot response on the session
                    session.setLatestBotResponse(result.response);

                    // Always emit BOT_RESPONSE_RECEIVED event to trigger transition to RESPONDING
                    // Removed state check to ensure event is always emitted
                    logDebug('[ProcessUserInputAction] Emitting BOT_RESPONSE_RECEIVED event');
                    this.eventEmitter.emit(
                      SessionEventType.BOT_RESPONSE_RECEIVED,
                      {
                        response: result.response,
                        timestamp: Date.now(),
                        requestId: metricsContext?.requestId,
                        fromAsyncProcessing: true, // Flag to indicate this is from async processing
                      },
                      EventPriority.HIGH
                    );
                  } else {
                    logDebug(
                      '[ProcessUserInputAction] No bot response received or processing failed'
                    );
                  }
                } else {
                  logDebug('[ProcessUserInputAction] Bot processing was cancelled');
                }
              } catch (error) {
                logError(
                  `[ProcessUserInputAction] Error in asynchronous bot processing: ${
                    error instanceof Error ? error.message : String(error)
                  }`
                );
              }
            })();
          } catch (error) {
            logError(
              `[ProcessUserInputAction] Error setting up asynchronous processing: ${
                error instanceof Error ? error.message : String(error)
              }`
            );
          }
        } else {
          logError('[ProcessUserInputAction] No bot adapter available');
        }

        // Emit PROCESSING_INPUT_COMPLETED event immediately to continue the state machine flow
        // This allows the transition to PROCESSING_BOT to complete without waiting for bot processing
        // Fetch the latest request context to ensure the correct requestId is used
        const latestMetricsContext = session.getCurrentRequest?.();
        const latestRequestId = latestMetricsContext?.requestId;
        this.eventEmitter.emit(
          SessionEventType.PROCESSING_INPUT_COMPLETED,
          {
            input: transcriptObj,
            inputType: 'speech',
            timestamp: Date.now(),
            requestId: latestRequestId,
            emitter: 'ProcessUserInputAction',
          },
          EventPriority.HIGH
        );

        // ASR phase finalization is now handled by EndListeningAction on exit from LISTENING state.
        return;
      }

      // Handle DTMF input (future implementation)
      if (inputType === 'dtmf') {
        logDebug('[ProcessUserInputAction] DTMF input processing not yet implemented');
        return;
      }

      // Handle undefined inputType with more context
      if (inputType === undefined) {
        // If we have a transcript but no inputType, try to process it as speech
        if (transcript && typeof (transcript as Transcript).text === 'string') {
          const transcriptObj = transcript as Transcript;
          logDebug(
            `[ProcessUserInputAction] Found transcript without inputType, processing as speech: "${transcriptObj.text}"`
          );

          // Start bot processing asynchronously without blocking the state transition
          if (botAdapter) {
            try {
              // Create an AbortController for cancellation support
              const abortController = new AbortController();

              // Store the AbortController on the session for potential cancellation (e.g., barge-in)
              if (
                typeof session.setCurrentProcessingAbortController === 'function' &&
                session.setCurrentProcessingAbortController !== undefined
              ) {
                session.setCurrentProcessingAbortController(abortController);
              }

              logDebug(
                '[ProcessUserInputAction] Starting asynchronous bot processing for transcript without inputType'
              );

              // Start processing in the background without awaiting the result
              // This prevents blocking the state transition
              void (async () => {
                try {
                  const result = await botAdapter.processUserInput(
                    transcriptObj,
                    metricsContext,
                    abortController.signal
                  );

                  // Only store the result if not aborted
                  if (!abortController.signal.aborted) {
                    if (result.success && result.response) {
                      logDebug(
                        `[ProcessUserInputAction] Received bot response asynchronously (${
                          result.response.text?.length || 0
                        } chars, ${result.response.audioBytes?.length || 0} bytes audio)`
                      );

                      // Store the bot response on the session
                      session.setLatestBotResponse(result.response);

                      // Always emit BOT_RESPONSE_RECEIVED event to trigger transition to RESPONDING
                      // Removed state check to ensure event is always emitted
                      logDebug('[ProcessUserInputAction] Emitting BOT_RESPONSE_RECEIVED event');
                      this.eventEmitter.emit(
                        SessionEventType.BOT_RESPONSE_RECEIVED,
                        {
                          response: result.response,
                          timestamp: Date.now(),
                          requestId: metricsContext?.requestId,
                          fromAsyncProcessing: true, // Flag to indicate this is from async processing
                        },
                        EventPriority.HIGH
                      );
                    } else {
                      logDebug(
                        '[ProcessUserInputAction] No bot response received or processing failed'
                      );
                    }
                  } else {
                    logDebug('[ProcessUserInputAction] Bot processing was cancelled');
                  }
                } catch (error) {
                  logError(
                    `[ProcessUserInputAction] Error in asynchronous bot processing: ${
                      error instanceof Error ? error.message : String(error)
                    }`
                  );
                }
              })();
            } catch (error) {
              logError(
                `[ProcessUserInputAction] Error setting up asynchronous processing: ${
                  error instanceof Error ? error.message : String(error)
                }`
              );
            }
          } else {
            logError('[ProcessUserInputAction] No bot adapter available');
          }

          // Emit PROCESSING_INPUT_COMPLETED event immediately to continue the state machine flow
          logDebug(
            '[ProcessUserInputAction] Emitting PROCESSING_INPUT_COMPLETED event for transcript without inputType'
          );
          this.eventEmitter.emit(
            SessionEventType.PROCESSING_INPUT_COMPLETED,
            {
              input: transcriptObj,
              inputType: 'speech',
              timestamp: Date.now(),
              requestId: metricsContext?.requestId,
              emitter: 'ProcessUserInputAction-noInputType',
            },
            EventPriority.HIGH
          );
          // ASR phase finalization is now handled exclusively by the audio service adapter for robust timing.
          return;
        }

        // No transcript or inputType, skip processing
        logError(
          `[ProcessUserInputAction] ERROR: No input type specified and no valid transcript in metadata: ${JSON.stringify(
            loggableMetadata
          )}, skipping processing`
        );
        return;
      }

      // If we get here, the input type is not supported
      logError(`[ProcessUserInputAction] ERROR: Unsupported input type: ${inputType}`);
      return;
    } catch (err) {
      logError(
        `[ProcessUserInputAction] ERROR: Exception during user input processing: ${
          err instanceof Error ? err.stack : String(err)
        }`
      );
      // Do not throw, just log and return to allow state machine to continue
      return;
    }
    // DEBUG: End of ProcessUserInputAction entry action
    logDebug('[ProcessUserInputAction] ENTRY ACTION COMPLETED');
  }
}
