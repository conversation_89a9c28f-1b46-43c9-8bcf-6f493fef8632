import type { StateAction, StateContext } from '../../interfaces';

/**
 * Action to reset/clear all barge-in, interruption, and pending flags
 * from the session and context metadata when entering PROCESSING_INPUT.
 * This ensures a clean slate for the new user turn after barge-in or interruption.
 */
export class ResetBargeInAndInterruptionFlagsAction implements StateAction {
  async execute(context: StateContext): Promise<void> {
    // Remove barge-in/interruption flags from metadata
    if (context.metadata) {
      delete context.metadata.isBargeIn;
      delete context.metadata.bargeInSource;
      delete context.metadata.bargeInText;
      delete context.metadata.interrupted;
      delete context.metadata.pendingBargeIn;
      // Add any other flags that should be cleared here
    }

    // If the session has any known public properties for barge-in or interruption, clear them here.
    // Example: if (context.session._pendingBargeInToListening !== undefined) context.session._pendingBargeInToListening = false;
    // (No such public properties are documented, so we only clear metadata flags.)
  }
}
