# Event-Driven Architecture for Session State Management

This document describes the event-driven architecture implemented for session state management, with a focus on the `EventDrivenStateManager` component.

## Overview

The event-driven architecture replaces direct method calls with events for state transitions, providing a more loosely coupled and maintainable system.

## Key Components

### EventDrivenStateManager

The `EventDrivenStateManager` is the central component that:

1. Subscribes to events that trigger state transitions
2. Executes entry and exit actions for each state
3. Validates state transitions against a defined transition graph
4. Emits events when state changes occur
5. Provides helper methods for checking the current state

### Event Types

The system defines various event types in `SessionEventType` enum:

- Session lifecycle events (initialization, close)
- User input events (speech, DTMF)
- Bot processing events (responses, completions)
- Playback events (started, completed)
- Barge-in events
- Interruption events

### State Actions

Each state can have associated entry and exit actions:

- **Entry Actions**: Executed when entering a state
- **Exit Actions**: Executed when leaving a state

Actions are registered with the state manager and executed in sequence during state transitions.

## State Checking Design

The state manager provides a clean design for state checking:

```typescript
// Generic state checking method
public isInState(state: SessionState): boolean {
  return this.state === state;
}

// Convenience methods for common state checks
public isPlaying(): boolean {
  return this.isInState(SessionState.PLAYING);
}
```

This design allows for clean, maintainable code while still providing convenience methods for common state checks.

## Usage Examples

### Creating a State Manager

```typescript
// Create an event emitter
const eventEmitter = new EventEmitter();

// Create a state manager
const stateManager = new EventDrivenStateManager(session, eventEmitter);
```

### Registering State Actions

```typescript
// Register entry and exit actions for a state
stateManager.registerStateActions(SessionState.PROCESSING_INPUT, {
  onEnter: [new LogStateTransitionAction(), new StartMetricsAction()],
  onExit: [new ProcessUserInputAction()],
});
```

### Transitioning States

State transitions are typically triggered by events, but can also be triggered manually:

```typescript
// Manual state transition
await stateManager.setState(SessionState.PROCESSING_BOT, {
  reason: 'User input processed',
  inputType: 'speech',
  transcript: { text: 'Hello', confidence: 0.9 },
});
```

### Checking States

```typescript
// Using the generic method
if (stateManager.isInState(SessionState.IDLE)) {
  // Ready to accept user input
}

// Using convenience methods
if (stateManager.isPlaying()) {
  // Audio is currently playing
}
```

## Event Flow

The event flow for a typical conversation is:

1. `SESSION_INITIALIZED`: INITIALIZING → IDLE
2. `USER_INPUT_RECEIVED`: IDLE → PROCESSING_INPUT
3. `USER_INPUT_PROCESSED`: PROCESSING_INPUT → PROCESSING_BOT
4. `BOT_RESPONSE_RECEIVED`: PROCESSING_BOT → RESPONDING
5. `PLAYBACK_STARTED`: RESPONDING → PLAYING
6. `PLAYBACK_COMPLETED`: PLAYING → IDLE

Interruptions like barge-in can override this flow:

- `BARGE_IN_DETECTED`: PLAYING → PROCESSING_INPUT
- `INTERRUPTION_REQUESTED`: Any state → Specified state

## Benefits of Event-Driven Architecture

- **Loose Coupling**: Components communicate through events rather than direct method calls
- **Clear Responsibility**: Each component has a well-defined role in the system
- **Testability**: Event-based architecture is easier to test in isolation
- **Flexibility**: New components can be added without modifying existing ones
- **Maintainability**: Clean design with generic methods reduces code duplication

## Implementation Details

The `EventDrivenStateManager` implements the following pattern for state checking:

1. A generic `isInState()` method that checks if the current state matches a specified state
2. Specific helper methods (like `isPlaying()`) that use the generic method
3. Compound state checks (like `isDisconnectingOrClosed()`) that combine multiple state checks

This approach follows clean design principles by:

- Reducing duplication
- Improving maintainability
- Providing flexibility
- Following single responsibility
- Maintaining backward compatibility
