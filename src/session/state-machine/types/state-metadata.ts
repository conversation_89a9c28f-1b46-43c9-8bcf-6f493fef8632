/**
 * Standardized metadata for state transitions in the session state machine.
 * This interface defines all possible properties that can be passed during state transitions.
 *
 * @module session/state-machine/types/state-metadata
 */

import { SessionState } from '../../session-state-manager';
import type { Transcript } from '../../../services/speech/base/types';
import type { BotResponse } from '../../../services/bot-service';

/**
 * Standardized metadata for state transitions.
 * This ensures consistent metadata structure across all state transitions.
 */
export interface StateTransitionMetadata {
  /**
   * The reason for the state transition.
   * This should be a descriptive string that explains why the transition is happening.
   * Optional in tests but should be provided in production code.
   */
  reason?: string;

  /**
   * The previous state, if applicable.
   * This is useful for tracking the state history and for actions that need to know where we came from.
   */
  previousState?: SessionState;

  /**
   * The type of input that triggered this transition, if applicable.
   * This is used for processing user input in the appropriate way.
   */
  inputType?: 'speech' | 'dtmf';

  /**
   * The transcript of the user's speech, if applicable.
   * This is used when processing speech input.
   */
  transcript?: Transcript;

  /**
   * The DTMF digits entered by the user, if applicable.
   * This is used when processing DTMF input.
   */
  digits?: string;

  /**
   * Flag indicating whether this is the initial greeting.
   * This is used to handle the special case of the initial bot greeting.
   */
  isInitialGreeting?: boolean;

  /**
   * Flag indicating whether this is a barge-in event.
   * This is used to handle barge-in scenarios differently.
   */
  isBargeIn?: boolean;

  /**
   * The source of a barge-in event, if applicable.
   * This indicates whether the barge-in came from speech or DTMF.
   */
  bargeInSource?: 'speech' | 'dtmf';

  /**
   * The text that triggered a barge-in, if applicable.
   * This is the text of the speech that caused the barge-in.
   */
  bargeInText?: string;

  /**
   * The bot response to be processed, if applicable.
   * This is used when transitioning to the RESPONDING state.
   */
  botResponse?: BotResponse;

  /**
   * Flag to force a transition even if it would normally be invalid.
   * This should be used sparingly, only for emergency situations.
   */
  forceTransition?: boolean;

  /**
   * Flag to indicate this is part of a recursive transition.
   * This is used to prevent infinite loops in state transitions.
   */
  isRecursiveTransition?: boolean;

  /**
   * Catch-all for any other properties that might be needed.
   * This allows for flexibility while maintaining type safety for known properties.
   */
  [key: string]: unknown;
}

/**
 * Validates and standardizes state transition metadata.
 * This ensures that all required properties are present and have appropriate values.
 *
 * @param metadata The raw metadata to standardize
 * @returns Standardized metadata with all required properties
 */
// We're removing the ProcessingInputToBotMetadata interface as it's causing
// type compatibility issues with the base StateTransitionMetadata interface.
// Instead, we'll use type assertions in the handler.

export function standardizeMetadata(metadata?: Record<string, unknown>): StateTransitionMetadata {
  // Start with defaults
  const standardized: StateTransitionMetadata = {
    ...metadata,
  };

  // Add default reason if not provided
  if (!standardized.reason) {
    standardized.reason = 'Unspecified reason';
  }

  // Ensure inputType is set if transcript is present
  if (standardized.transcript && !standardized.inputType) {
    standardized.inputType = 'speech';
  }

  return standardized;
}
