/**
 * Defines the valid state transitions for the session state machine.
 * This file serves as the single source of truth for what transitions are allowed.
 *
 * @module session/state-machine/types/state-transitions
 */

import { SessionState } from '../../session-state-manager';
import type { StateTransitionMetadata } from './state-metadata';

/**
 * Interface for metadata validation rules.
 * Used to validate that the metadata contains the required properties for a specific transition.
 */
export interface MetadataValidationRule {
  /**
   * Required properties that must be present in the metadata.
   */
  requiredProperties?: Array<keyof StateTransitionMetadata>;

  /**
   * Custom validation function for more complex validation rules.
   * Returns true if the metadata is valid, false otherwise.
   */
  validate?: (metadata: StateTransitionMetadata) => boolean;

  /**
   * Error message to display if the validation fails.
   */
  errorMessage?: string;
}

/**
 * Interface for a state transition definition.
 * Defines the from and to states, and optional metadata validation rules.
 */
export interface StateTransitionDefinition {
  /**
   * The source state of the transition.
   */
  from: SessionState;

  /**
   * The target state of the transition.
   */
  to: SessionState;

  /**
   * Optional description of the transition.
   * Useful for documentation and debugging.
   */
  description?: string;

  /**
   * Optional metadata validation rules.
   * Used to validate that the metadata contains the required properties.
   */
  metadataValidation?: MetadataValidationRule;
}

/**
 * Type for the transition graph.
 * Maps from states to arrays of allowed target states.
 */
export type TransitionGraph = Record<SessionState, SessionState[]>;

/**
 * The valid state transitions for the session state machine.
 * This is the single source of truth for what transitions are allowed.
 */
export const STATE_TRANSITIONS: StateTransitionDefinition[] = [
  // From INITIALIZING
  {
    from: SessionState.INITIALIZING,
    to: SessionState.IDLE,
    description: 'Session initialization completed',
  },
  {
    from: SessionState.INITIALIZING,
    to: SessionState.DISCONNECTING,
    description: 'Session initialization failed',
  },

  // From IDLE
  {
    from: SessionState.IDLE,
    to: SessionState.LISTENING,
    description: 'Session is ready for input and begins listening for user audio',
    metadataValidation: {
      // READY_FOR_INPUT, PLAYBACK_COMPLETED, or similar events
      requiredProperties: [],
    },
  },
  {
    from: SessionState.IDLE,
    to: SessionState.PROCESSING_BOT,
    description: 'Initial bot greeting',
    metadataValidation: {
      requiredProperties: ['isInitialGreeting'],
      validate: metadata => metadata.isInitialGreeting === true,
      errorMessage: 'Transition to PROCESSING_BOT from IDLE requires isInitialGreeting=true',
    },
  },
  // {
  //   from: SessionState.IDLE,
  //   to: SessionState.RESPONDING,
  //   description: 'Direct response without input processing',
  // },
  {
    from: SessionState.IDLE,
    to: SessionState.PLAYING,
    description: 'Direct playback without input processing',
  },
  {
    from: SessionState.IDLE,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested',
  },
  {
    from: SessionState.IDLE,
    to: SessionState.CLOSED,
    description: 'Clean shutdown without disconnection phase',
  },

  // From LISTENING
  {
    from: SessionState.LISTENING,
    to: SessionState.PROCESSING_INPUT,
    description: 'User audio detected, begin processing input',
    metadataValidation: {
      requiredProperties: ['inputType', 'transcript'],
      errorMessage:
        'Transition to PROCESSING_INPUT from LISTENING requires inputType and transcript',
    },
  },
  {
    from: SessionState.LISTENING,
    to: SessionState.IDLE,
    description: 'Listening timed out or was cancelled, return to idle',
  },
  {
    from: SessionState.LISTENING,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested during listening',
  },

  // From PROCESSING_INPUT
  {
    from: SessionState.PROCESSING_INPUT,
    to: SessionState.PROCESSING_BOT,
    description: 'User input processed, transitioning to bot processing',
    metadataValidation: {
      validate: metadata => {
        // Require inputType
        if (metadata.inputType === undefined) {
          return false;
        }

        // For speech input, require transcript
        if (metadata.inputType === 'speech') {
          return metadata.transcript !== undefined;
        }

        // For DTMF input, require either transcript or input
        if (metadata.inputType === 'dtmf') {
          return metadata.transcript !== undefined || metadata.input !== undefined;
        }

        return false;
      },
      errorMessage:
        'Transition to PROCESSING_BOT requires inputType and either transcript (for speech) or input (for DTMF)',
    },
  },
  {
    from: SessionState.PROCESSING_INPUT,
    to: SessionState.IDLE,
    description: 'User input processing completed or cancelled',
  },
  {
    from: SessionState.PROCESSING_INPUT,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested during input processing',
  },

  // From PROCESSING_BOT
  {
    from: SessionState.PROCESSING_BOT,
    to: SessionState.RESPONDING,
    description: 'Bot processing completed, preparing response',
    metadataValidation: {
      // Don't require botResponse in metadata to avoid logging binary data
      // The response should be stored on the session directly
      requiredProperties: ['hasText', 'hasAudio'],
      errorMessage: 'Transition to RESPONDING requires hasText and hasAudio flags',
    },
  },
  {
    from: SessionState.PROCESSING_BOT,
    to: SessionState.IDLE,
    description: 'Bot processing completed or cancelled without response',
  },
  {
    from: SessionState.PROCESSING_BOT,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested during bot processing',
  },

  // From RESPONDING
  {
    from: SessionState.RESPONDING,
    to: SessionState.PLAYING,
    description: 'Response prepared, starting playback',
  },
  {
    from: SessionState.RESPONDING,
    to: SessionState.PROCESSING_INPUT,
    description: 'Barge-in during response preparation',
    metadataValidation: {
      requiredProperties: ['isBargeIn'],
      validate: metadata => metadata.isBargeIn === true,
      errorMessage: 'Transition to PROCESSING_INPUT from RESPONDING requires isBargeIn=true',
    },
  },
  {
    from: SessionState.RESPONDING,
    to: SessionState.IDLE,
    description: 'Response preparation completed or cancelled',
  },
  {
    from: SessionState.RESPONDING,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested during response preparation',
  },

  // From PLAYING
  {
    from: SessionState.PLAYING,
    to: SessionState.PROCESSING_INPUT,
    description: 'Barge-in during playback',
    metadataValidation: {
      requiredProperties: ['bargeInSource'],
      errorMessage: 'Transition to PROCESSING_INPUT from PLAYING requires bargeInSource',
    },
  },
  {
    from: SessionState.PLAYING,
    to: SessionState.IDLE,
    description: 'Playback completed',
  },
  {
    from: SessionState.PLAYING,
    to: SessionState.DISCONNECTING,
    description: 'Session disconnection requested during playback',
  },

  // From DISCONNECTING
  {
    from: SessionState.DISCONNECTING,
    to: SessionState.CLOSED,
    description: 'Disconnection completed',
  },

  // From CLOSED (no valid transitions)
];

/**
 * Builds a transition graph from the state transitions.
 * @returns A record mapping from states to arrays of allowed target states.
 */
export function buildTransitionGraph(): TransitionGraph {
  const graph: TransitionGraph = Object.values(SessionState).reduce(
    (acc, state) => ({ ...acc, [state]: [] }),
    {} as TransitionGraph
  );

  // Add all transitions to the graph
  for (const transition of STATE_TRANSITIONS) {
    if (!graph[transition.from].includes(transition.to)) {
      graph[transition.from].push(transition.to);
    }
  }

  // Special case: DISCONNECTING is always a valid target state from any active state
  Object.values(SessionState).forEach(state => {
    if (state !== SessionState.DISCONNECTING && state !== SessionState.CLOSED) {
      if (!graph[state].includes(SessionState.DISCONNECTING)) {
        graph[state].push(SessionState.DISCONNECTING);
      }
    }
  });

  return graph;
}

/**
 * The transition graph for the session state machine.
 * Maps from states to arrays of allowed target states.
 */
export const TRANSITION_GRAPH: TransitionGraph = buildTransitionGraph();

/**
 * Finds a transition definition for the given from and to states.
 * @param from The source state.
 * @param to The target state.
 * @returns The transition definition, or undefined if no matching transition is found.
 */
export function findTransitionDefinition(
  from: SessionState,
  to: SessionState
): StateTransitionDefinition | undefined {
  return STATE_TRANSITIONS.find(transition => transition.from === from && transition.to === to);
}

/**
 * Validates that a transition is allowed.
 * @param from The source state.
 * @param to The target state.
 * @param metadata The metadata for the transition.
 * @returns An object with a valid flag and an optional error message.
 */
export function validateTransition(
  from: SessionState,
  to: SessionState,
  metadata: StateTransitionMetadata
): { valid: boolean; errorMessage?: string } {
  // Special case: forced transitions are always allowed
  if (metadata.forceTransition === true) {
    return { valid: true };
  }

  // Special case: DISCONNECTING is always a valid target state from any active state
  if (to === SessionState.DISCONNECTING) {
    return { valid: true };
  }

  // Check if the transition is allowed
  if (!TRANSITION_GRAPH[from]?.includes(to)) {
    return {
      valid: false,
      errorMessage: `Invalid transition: ${from} → ${to}`,
    };
  }

  // Find the transition definition
  const transitionDef = findTransitionDefinition(from, to);

  // If there's no definition, but the graph allows it, it's valid
  if (!transitionDef) {
    return { valid: true };
  }

  // Check metadata validation rules
  const { metadataValidation } = transitionDef;
  if (metadataValidation) {
    // Check required properties
    if (metadataValidation.requiredProperties) {
      for (const prop of metadataValidation.requiredProperties) {
        if (metadata[prop] === undefined) {
          return {
            valid: false,
            errorMessage:
              metadataValidation.errorMessage ||
              `Missing required metadata property: ${String(prop)}`,
          };
        }
      }
    }

    // Check custom validation
    if (metadataValidation.validate && !metadataValidation.validate(metadata)) {
      return {
        valid: false,
        errorMessage: metadataValidation.errorMessage || 'Metadata validation failed',
      };
    }
  }

  return { valid: true };
}
