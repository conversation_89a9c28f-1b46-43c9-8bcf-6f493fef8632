import { ProcessBotStartAction } from '../actions/processing-bot/process-bot-start-action';
// Unused imports removed
import { SessionState } from '../../session-state-manager';
import { EventEmitter } from '../../../services/events/event-emitter';

// Mock the logging module
jest.mock('../../../services/logging/logger', () => ({
  logInfo: jest.fn(),
  logWarning: jest.fn(),
  logError: jest.fn(),
  logDebug: jest.fn(),
}));

describe('ProcessBotStartAction', () => {
  // Using any for test simplicity
  let session: any;
  let context: any;

  beforeEach(() => {
    // Create a session with mock methods
    session = {
      id: 'test-session-id',
      getSessionState: jest.fn().mockReturnValue(SessionState.PROCESSING_BOT),
      getStartTime: jest.fn().mockReturnValue(new Date()),
      getLatestBotResponse: jest.fn().mockReturnValue(null),
      getSessionStateManager: jest.fn().mockReturnValue({
        getState: jest.fn().mockReturnValue(SessionState.PROCESSING_BOT),
        setState: jest.fn().mockResolvedValue(undefined),
        registerStateActions: jest.fn(),
        getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
      }),
    };

    // Create a context with our session
    context = {
      state: SessionState.PROCESSING_BOT,
      metadata: { reason: 'Test reason' },
      session,
    };
  });

  it('skips processing for initial greeting', async () => {
    // Set the metadata to indicate this is an initial greeting
    context.metadata = { isInitialGreeting: true };

    const action = new ProcessBotStartAction();
    await action.execute(context);

    // Verify that the logDebug function was called with the expected message
    const { logDebug } = require('../../../services/logging/logger');
    expect(logDebug).toHaveBeenCalledWith(expect.stringContaining('Skipping for initial greeting'));
  });

  it('logs info if bot response is already available', async () => {
    // Set up the session to return a bot response
    const mockBotResponse = {
      text: 'Hello',
      audioBytes: new Uint8Array([1, 2, 3]),
    };
    session.getLatestBotResponse = jest.fn().mockReturnValue(mockBotResponse);

    const action = new ProcessBotStartAction();
    await action.execute(context);

    // Verify that the logInfo function was called with the expected message
    const { logInfo } = require('../../../services/logging/logger');
    expect(logInfo).toHaveBeenCalledWith(expect.stringContaining('Bot response already available'));
  });

  it('logs info if no bot response is available yet', async () => {
    // Set up the session to return no bot response
    session.getLatestBotResponse = jest.fn().mockReturnValue(null);

    const action = new ProcessBotStartAction();
    await action.execute(context);

    // Verify that the logInfo function was called with the expected message
    const { logInfo } = require('../../../services/logging/logger');
    expect(logInfo).toHaveBeenCalledWith(expect.stringContaining('No bot response available yet'));
  });
});
