/**
 * Type definitions for test files to avoid using 'any' type.
 * These types are simplified versions of the actual types used in the application.
 */

import type { BotTurnDisposition } from '../../../protocol/voice-bots';
import { BotResponse } from '../../../services/bot-service';
import { BaseTTSService } from '../../../services/speech/base/base-tts-service';

/**
 * Simplified bot response type for tests that extends the actual BotResponse class
 */
export class TestBotResponse extends BotResponse {
  constructor(props: {
    text?: string;
    audioBytes?: Uint8Array;
    disposition?: BotTurnDisposition;
    endSession?: boolean;
  }) {
    super(props.disposition || 'match', props.text || '');
    if (props.audioBytes) {
      this.audioBytes = props.audioBytes;
    }
    if (props.endSession !== undefined) {
      this.endSession = props.endSession;
    }
  }

  // Implement the required methods from BotResponse
  withConfidence(confidence: number): TestBotResponse {
    this.confidence = confidence;
    return this;
  }

  withAudioBytes(audioBytes: Uint8Array): TestBotResponse {
    this.audioBytes = audioBytes;
    return this;
  }

  withEndSession(endSession: boolean, reason?: 'end' | 'agent'): TestBotResponse {
    this.endSession = endSession;
    if (reason) {
      this.endSessionReason = reason;
    }
    return this;
  }
}

/**
 * Simplified TTS service type for tests that extends BaseTTSService
 */
export class TestTTSService extends BaseTTSService {
  constructor(private mockAudioBytes?: Uint8Array) {
    super();
  }

  // Implement required abstract methods
  protected async initializeService(): Promise<void> {
    // No initialization needed for test
    return Promise.resolve();
  }

  protected async synthesizeAudio(text: string): Promise<Uint8Array> {
    return this.mockAudioBytes || new Uint8Array([1, 2, 3]);
  }

  dispose(): void {
    // No cleanup needed for test
  }
}
