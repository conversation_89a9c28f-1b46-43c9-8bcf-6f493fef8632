import { SessionState } from '../../session-state-manager';
import { StartLLMMetricsAction } from '../actions/processing-bot/start-llm-metrics-action';
import { HandleBotResponseAction } from '../actions/processing-bot/handle-bot-response-action';
import { CheckSessionEndAction } from '../actions/processing-bot/check-session-end-action';
import { TransitionToRespondingAction } from '../actions/processing-bot/transition-to-responding-action';
import { createMockSession, createMockStateContext } from './test-utils';
import { TestBotResponse } from './test-types';
import { EventEmitter } from '../../../services/events/event-emitter';

describe('PROCESSING_BOT state actions', () => {
  let session: ReturnType<typeof createMockSession>;
  let context: ReturnType<typeof createMockStateContext>;

  beforeEach(() => {
    // Create a session with mock methods
    session = {
      id: 'test-session-id',
      getSessionState: jest.fn().mockReturnValue(SessionState.PROCESSING_BOT),
      getStartTime: jest.fn().mockReturnValue(new Date()),
      setPlaybackState: jest.fn(),
      updatePlaybackStateWithoutTransition: jest.fn(),
      enableBargeIn: jest.fn(),
      disableBargeIn: jest.fn(),
      isBargeInEnabled: jest.fn().mockReturnValue(true),
      initializeASRService: jest.fn().mockResolvedValue(undefined),
      processBotStart: jest.fn().mockResolvedValue(undefined),
      getConversationId: jest.fn().mockReturnValue('test-conversation-id'),
      setConversationId: jest.fn(),
      setClientAni: jest.fn(),
      close: jest.fn(),
      send: jest.fn(),
      sendBotTurnResponse: jest.fn(),
      processBinaryMessage: jest.fn().mockResolvedValue(undefined),
      getSessionStateManager: jest.fn().mockReturnValue({
        getState: jest.fn(),
        setState: jest.fn().mockResolvedValue(undefined),
        registerStateActions: jest.fn(),
        getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
      }),
      getMetricsAdapter: jest.fn().mockReturnValue({
        startPhase: jest.fn(),
        endPhase: jest.fn(),
        setUserInput: jest.fn(),
        setAiReply: jest.fn(),
        finalizeRequest: jest.fn(),
      }),
      getBotAdapter: jest.fn(),
      getCurrentRequest: jest.fn(),
      getStartMetricsRequest: jest.fn(),
      getLatestBotResponse: jest.fn(),
      setLatestBotResponse: jest.fn(),
      sendAudio: jest.fn().mockResolvedValue(undefined),
      getTTSService: jest.fn(),
      disposeAudio: jest.fn().mockResolvedValue(undefined),
      disposeBot: jest.fn().mockResolvedValue(undefined),
      closeWebSocket: jest.fn(),
      finalizeMetrics: jest.fn(),
      prepareForUserInput: jest.fn().mockResolvedValue(undefined),
      getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
    };

    // Create a context with our session
    context = {
      state: SessionState.PROCESSING_BOT,
      metadata: { reason: 'Test reason' },
      session,
    };
  });

  describe('StartLLMMetricsAction', () => {
    it('starts the llmProcessing phase in metrics', async () => {
      const action = new StartLLMMetricsAction();
      await action.execute(context);
      expect(session.getMetricsAdapter().startPhase).toHaveBeenCalledWith('llmProcessing');
    });
  });

  describe('HandleBotResponseAction', () => {
    it('sends text if present', async () => {
      // Create a bot response with text
      const botResponse = new TestBotResponse({
        audioBytes: new Uint8Array([1, 2, 3]),
        text: 'hi',
        disposition: 'match',
      });

      // Set up the session to return the bot response
      session.getLatestBotResponse = () => botResponse;

      // Create the action with the session's event emitter
      const sessionEventEmitter = session.getEventEmitter();
      const action = new HandleBotResponseAction({ eventEmitter: sessionEventEmitter });

      // Execute the action
      await action.execute(context);

      // Verify that the bot turn response was sent
      expect(session.sendBotTurnResponse).toHaveBeenCalledWith('hi', 'match');
    });

    it('does nothing if no bot response', async () => {
      const action = new HandleBotResponseAction();
      session.getLatestBotResponse = () => undefined;
      await action.execute(context);
      expect(session.sendBotTurnResponse).not.toHaveBeenCalled();
    });
  });

  describe('CheckSessionEndAction', () => {
    it('closes session if endSession is true', async () => {
      // Create a bot response with endSession=true
      const botResponse = new TestBotResponse({ endSession: true });

      // Set up the session to return the bot response
      session.getLatestBotResponse = () => botResponse;

      // Create the action
      const action = new CheckSessionEndAction();

      // Execute the action
      await action.execute(context);

      // Verify that the session was closed
      expect(session.close).toHaveBeenCalled();
    });

    it('does not close session if endSession is false', async () => {
      // Create a bot response with endSession=false
      const botResponse = new TestBotResponse({ endSession: false });

      // Set up the session to return the bot response
      session.getLatestBotResponse = () => botResponse;

      // Create the action
      const action = new CheckSessionEndAction();

      // Execute the action
      await action.execute(context);

      // Verify that the session was not closed
      expect(session.close).not.toHaveBeenCalled();
    });
  });

  describe('TransitionToRespondingAction', () => {
    it('is a no-op (deprecated)', async () => {
      // This action is now a no-op, so we just verify it doesn't throw
      const action = new TransitionToRespondingAction();
      await expect(action.execute(context)).resolves.not.toThrow();
    });
  });
});

/**
 * Metrics robustness and decoupling tests
 * Ensures metrics errors do not affect state transitions or cause duplication/out-of-order transitions.
 */
describe('Metrics robustness and decoupling', () => {
  let session: ReturnType<typeof createMockSession>;
  let context: ReturnType<typeof createMockStateContext>;

  beforeEach(() => {
    // Explicitly type metrics adapter methods as jest.Mock for TS compatibility
    const startPhase: jest.Mock = jest.fn();
    const endPhase: jest.Mock = jest.fn();
    const setUserInput: jest.Mock = jest.fn();
    const setAiReply: jest.Mock = jest.fn();
    const finalizeRequest: jest.Mock = jest.fn();

    session = {
      ...createMockSession(),
      getSessionState: jest.fn().mockReturnValue(SessionState.PROCESSING_BOT),
      getMetricsAdapter: jest.fn().mockReturnValue({
        startPhase,
        endPhase,
        setUserInput,
        setAiReply,
        finalizeRequest,
      }),
      close: jest.fn(),
      getLatestBotResponse: jest.fn(),
      sendBotTurnResponse: jest.fn(),
      getEventEmitter: jest.fn().mockReturnValue(new EventEmitter()),
    };
    context = {
      state: SessionState.PROCESSING_BOT,
      metadata: { reason: 'Test reason' },
      session,
    };
  });

  it('should not duplicate or skip state transitions if metrics startPhase throws', async () => {
    // Arrange: startPhase throws
    const metricsAdapter = session.getMetricsAdapter();
    (metricsAdapter.startPhase as jest.Mock).mockImplementation(() => {
      throw new Error('metrics error');
    });

    // Action under test
    const action = new StartLLMMetricsAction();

    // Should not throw to caller
    await expect(action.execute(context)).resolves.not.toThrow();

    // Simulate a state transition after metrics
    const botResponse = new TestBotResponse({ text: 'hi', disposition: 'match' });
    session.getLatestBotResponse = () => botResponse;
    const handleAction = new HandleBotResponseAction({ eventEmitter: session.getEventEmitter() });
    await expect(handleAction.execute(context)).resolves.not.toThrow();

    // State transition (sendBotTurnResponse) should still occur once
    expect(session.sendBotTurnResponse).toHaveBeenCalledTimes(1);
    expect(session.sendBotTurnResponse).toHaveBeenCalledWith('hi', 'match');
  });

  it('should not interfere with session close if metrics throws', async () => {
    // Arrange: finalizeRequest throws
    const metricsAdapter = session.getMetricsAdapter();
    (metricsAdapter.finalizeRequest as jest.Mock).mockImplementation(() => {
      throw new Error('metrics error');
    });

    // Simulate a bot response that ends the session
    const botResponse = new TestBotResponse({ endSession: true });
    session.getLatestBotResponse = () => botResponse;

    // Action under test
    const checkEndAction = new CheckSessionEndAction();
    await expect(checkEndAction.execute(context)).resolves.not.toThrow();

    // Session close should still be called
    expect(session.close).toHaveBeenCalledTimes(1);
  });
});
