import { SessionState } from '../../session-state-manager';
import { DisposeResourcesAction } from '../actions/common/dispose-resources-action';
import { createMockSession, createMockStateContext } from './test-utils';

describe('DISCONNECTING and CLOSED state actions', () => {
  let session: ReturnType<typeof createMockSession>;
  let context: ReturnType<typeof createMockStateContext>;

  beforeEach(() => {
    // Create a session with mock methods
    session = createMockSession();

    // Override the mock methods
    session.disposeAudio = jest.fn().mockResolvedValue(undefined);
    session.disposeBot = jest.fn().mockResolvedValue(undefined);
    session.closeWebSocket = jest.fn();
    session.finalizeMetrics = jest.fn();

    // Set the session state
    (session.getSessionState as jest.Mock).mockReturnValue(SessionState.DISCONNECTING);

    // Create a context with our session
    context = createMockStateContext(SessionState.DISCONNECTING, {}, session);
  });

  describe('DisposeResourcesAction', () => {
    it('calls all cleanup methods on session', async () => {
      const action = new DisposeResourcesAction();
      await action.execute(context);
      expect(session.disposeAudio).toHaveBeenCalled();
      expect(session.disposeBot).toHaveBeenCalled();
      expect(session.closeWebSocket).toHaveBeenCalled();
      expect(session.finalizeMetrics).toHaveBeenCalled();
    });
  });

  // CLOSED state is just logging, covered by LogStateTransitionAction tests elsewhere
});
