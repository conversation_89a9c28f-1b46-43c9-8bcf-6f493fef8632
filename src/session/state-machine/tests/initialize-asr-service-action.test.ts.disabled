import { InitializeASRServiceAction } from '../actions/initializing/initialize-asr-service-action';
import { createMockStateContext } from './test-utils';
import { SessionState } from '../../session-state-manager';

describe('InitializeASRServiceAction', () => {
  it('calls initializeASRService on the session with the conversationId', async () => {
    const initializeASRService = jest.fn().mockResolvedValue(undefined);
    const context = createMockStateContext(
      SessionState.INITIALIZING,
      { conversationId: 'abc123' },
      { initializeASRService }
    );

    const action = new InitializeASRServiceAction();
    await action.execute(context);

    expect(initializeASRService).toHaveBeenCalledWith('abc123');
  });

  it('does not throw an error if conversationId is missing', async () => {
    // Create a session with getConversationId returning null
    const getConversationId = jest.fn().mockReturnValue(null);

    // Create context without conversationId
    const context = createMockStateContext(
      SessionState.INITIALIZING,
      {}, // No conversationId in metadata
      { getConversationId }
    );

    const action = new InitializeASRServiceAction();

    // This should not throw an error
    await expect(action.execute(context)).resolves.not.toThrow();
  });

  it('throws if session does not implement initializeASRService', async () => {
    // Create a session without initializeASRService
    const session = createMockStateContext(SessionState.INITIALIZING, {
      conversationId: 'abc123',
    }).session;

    // Remove the initializeASRService method
    // Using type assertion to unknown first to avoid direct any usage
    delete (session as unknown as Record<string, unknown>).initializeASRService;

    // Create a new context with this session
    const context = {
      state: SessionState.INITIALIZING,
      metadata: { conversationId: 'abc123' },
      session,
    };

    const action = new InitializeASRServiceAction();
    await expect(action.execute(context)).rejects.toThrow();
  });
});
