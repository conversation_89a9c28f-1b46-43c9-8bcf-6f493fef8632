import { SetPlaybackStateAction } from '../actions/audio/set-playback-state-action';
import { createMockStateContext } from './test-utils';
import { SessionState } from '../../session-state-manager';
import type { PlaybackState } from '../../../services/barge-in/barge-in-manager';

/**
 * Tests for SetPlaybackStateAction using properly typed mocks
 */
describe('SetPlaybackStateAction', () => {
  it('calls setPlaybackState on the session with the correct state', async () => {
    const setPlaybackState = jest.fn();
    const context = createMockStateContext(SessionState.PLAYING, {}, { setPlaybackState });

    const action = new SetPlaybackStateAction('playing' as PlaybackState);
    await action.execute(context);

    expect(setPlaybackState).toHaveBeenCalledWith('playing');
  });

  it('throws if session does not implement setPlaybackState', async () => {
    // Create a context with a session that has setPlaybackState removed
    const context = createMockStateContext(
      SessionState.PLAYING,
      {},
      { setPlaybackState: undefined as unknown as never }
    );

    const action = new SetPlaybackStateAction('stopped' as PlaybackState);
    await expect(action.execute(context)).rejects.toThrow(
      'Session does not implement setPlaybackState'
    );
  });
});
