import { EventDrivenStateManager } from '../event-driven-state-manager';
import { SessionState } from '../../session-state-manager';
import { LogStateTransitionAction } from '../actions/common/log-state-transition-action';
import { createMockSession } from './test-utils';
import { EventEmitter } from '../../../services/events/event-emitter';

jest.mock('../../../services/logging/logger', () => ({
  logInfo: jest.fn(),
  logError: jest.fn(),
  logDebug: jest.fn(),
  logWarning: jest.fn(),
  logStateTransition: jest.fn(),
  prepareMetadataForLogging: jest.fn().mockImplementation(metadata => metadata),
}));

describe('IDLE state entry actions', () => {
  it('executes LogStateTransitionAction on entry to IDLE', async () => {
    const mockSession = createMockSession();
    const logAction = new LogStateTransitionAction();
    const eventEmitter = new EventEmitter();

    const manager = new EventDrivenStateManager(mockSession, eventEmitter);
    manager.registerStateActions(SessionState.IDLE, {
      onEnter: [logAction],
    });

    await manager.setState(SessionState.IDLE, { reason: 'Test idle entry' });

    const { logStateTransition } = require('../../../services/logging/logger');
    expect(logStateTransition).toHaveBeenCalled();
  });
});
