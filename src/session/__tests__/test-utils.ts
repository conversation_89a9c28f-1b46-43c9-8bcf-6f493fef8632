/**
 * Factory for creating a mock Session object for unit tests.
 * Accepts overrides for methods/properties as needed.
 */
export function createMockSession(overrides: Partial<any> = {}) {
  return {
    setLastToolResult: jest.fn(),
    getLastToolResult: jest.fn(),
    ...overrides,
  };
}

/**
 * Type for MCP service used in tests.
 */
export interface TestMCPService {
  listTools: () => Promise<any>;
  safeGetDateTime: () => Promise<any>;
}

/**
 * TestSession extends Session with a typed mcpService property for test safety.
 */
import { Session } from '../session';
import { WebSocket } from 'ws';
import { BotService } from '../../services/bot-service/bot-service';

export class TestSession extends Session {
  public mcpService?: TestMCPService;
  constructor(
    ws: WebSocket,
    sessionId: string,
    url: string,
    botService: BotService,
    mcpService?: TestMCPService
  ) {
    super(ws, sessionId, url, botService);
    if (mcpService) {
      this.mcpService = mcpService;
    }
  }
}

export function createTestSession({
  mcpService,
  setLastToolResult,
  getBotIfExists,
}: {
  mcpService?: TestMCPService;
  setLastToolResult?: (result: any) => void;
  getBotIfExists?: (...args: any[]) => Promise<any>;
} = {}): TestSession {
  const ws = {} as unknown as WebSocket;
  const sessionId = 'test-session';
  const url = 'ws://localhost';
  const botService = {
    getBotIfExists: getBotIfExists || jest.fn().mockResolvedValue(null),
  } as unknown as BotService;

  const session = new TestSession(ws, sessionId, url, botService, mcpService);

  // Attach tool result setter if provided
  if (setLastToolResult) {
    session.setLastToolResult = setLastToolResult;
  }

  return session;
}
