import { createMockSession } from './test-utils';

describe('Session (tool integration)', () => {
  it('should store and retrieve the last tool result', () => {
    const session = createMockSession();
    const toolResult = { time: '2025-06-04T09:00:00Z' };
    session.setLastToolResult(toolResult);
    session.getLastToolResult.mockReturnValue(toolResult);

    expect(session.setLastToolResult).toHaveBeenCalledWith(toolResult);
    expect(session.getLastToolResult()).toEqual(toolResult);
  });
});
