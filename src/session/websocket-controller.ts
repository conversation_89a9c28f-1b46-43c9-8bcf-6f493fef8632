// WebSocketController: Encapsulates WebSocket operations for Session
import { WebSocket } from 'ws';
import { ServerMessage } from '../protocol/message';
import { logInfo, logError, logDebug } from '../services/logging/logger';
import { loggingConfig, shouldLogMessage } from '../services/logging/logging-config';

export interface IWebSocketController {
  send(message: ServerMessage): void;
  sendAudio(bytes: Uint8Array): Promise<void>;
  close(): void;
  getState(): number;
}

export class WebSocketController implements IWebSocketController {
  private ws: WebSocket;
  private readonly MAXIMUM_BINARY_MESSAGE_SIZE = 64000;

  constructor(ws: WebSocket) {
    this.ws = ws;
  }

  send(message: ServerMessage): void {
    try {
      const messageJson = JSON.stringify(message);
      this.ws.send(messageJson);

      // Use rate limiting for WebSocket message logs
      if (shouldLogMessage(`websocket_${message.type}`, loggingConfig.webSocketMessageInterval)) {
        logDebug(`[WebSocketController] Sent message: ${message.type}`);
      }
    } catch (err) {
      logError(
        `[WebSocketController] Error sending message: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  async sendAudio(bytes: Uint8Array): Promise<void> {
    try {
      // Always log when sending audio (this is important for debugging)
      // Only log the length of the audio data, not the data itself
      logInfo(`[WebSocketController] Sending ${bytes.length} bytes of audio data to client`);

      if (bytes.length <= this.MAXIMUM_BINARY_MESSAGE_SIZE) {
        await this.sendAudioChunk(bytes);
        logDebug(`[WebSocketController] Audio chunk sent successfully (${bytes.length} bytes)`);
      } else {
        logInfo(`[WebSocketController] Splitting audio into chunks (total: ${bytes.length} bytes)`);
        let currentPosition = 0;
        let chunkCount = 0;
        while (currentPosition < bytes.length) {
          const sendBytes = bytes.slice(
            currentPosition,
            currentPosition + this.MAXIMUM_BINARY_MESSAGE_SIZE
          );
          await this.sendAudioChunk(sendBytes);
          currentPosition += this.MAXIMUM_BINARY_MESSAGE_SIZE;
          chunkCount++;

          // Log progress for large audio files, but only at debug level
          if (chunkCount % 10 === 0) {
            logDebug(
              `[WebSocketController] Sent ${chunkCount} chunks (${currentPosition} of ${bytes.length} bytes)`
            );
          }

          // Add a small delay between chunks to prevent overwhelming the WebSocket
          await new Promise(resolve => setTimeout(resolve, 10));
        }
        logInfo(`[WebSocketController] Finished sending all ${chunkCount} audio chunks`);
      }
    } catch (error) {
      logError(
        `[WebSocketController] Error sending audio: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  private async sendAudioChunk(chunk: Uint8Array): Promise<void> {
    // Check WebSocket state before sending
    const readyState = this.ws.readyState;
    if (readyState !== 1) {
      // 1 = OPEN
      const stateNames = ['CONNECTING', 'OPEN', 'CLOSING', 'CLOSED'];
      const stateName = stateNames[readyState] || 'UNKNOWN';
      logError(
        `[WebSocketController] Cannot send audio chunk - WebSocket is in ${stateName} state (${readyState})`
      );
      throw new Error(`WebSocket not in OPEN state, current state: ${stateName} (${readyState})`);
    }

    // Use debug level for individual chunk sending to reduce log noise
    // Avoid logging the binary data itself to prevent console flooding
    logDebug(`[WebSocketController] Sending audio chunk of ${chunk.length} bytes via WebSocket`);

    return new Promise<void>((resolve, reject) => {
      this.ws.send(chunk, { binary: true }, error => {
        if (error) {
          logError(
            `[WebSocketController] Error sending audio chunk: ${
              error instanceof Error ? error.message : String(error)
            }`
          );
          reject(error);
        } else {
          // Use debug level for success messages to reduce log noise
          logDebug(`[WebSocketController] Successfully sent audio chunk of ${chunk.length} bytes`);
          resolve();
        }
      });
    });
  }

  close(): void {
    try {
      this.ws.close();
      logInfo('[WebSocketController] WebSocket closed');
    } catch (err) {
      logError(
        `[WebSocketController] Error closing WebSocket: ${
          err instanceof Error ? err.message : String(err)
        }`
      );
    }
  }

  getState(): number {
    return this.ws.readyState;
  }
}
