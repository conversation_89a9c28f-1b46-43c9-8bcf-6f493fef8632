/**
 * Session Module
 *
 * This module provides the core session functionality for the application,
 * orchestrating the lifecycle of a voice interaction session.
 */

export { Session } from './session';
export { SessionState } from './session-state-manager';
export type { StateTransition, StateChangeListener } from './session-state-manager';
export { IWebSocketController, WebSocketController } from './websocket-controller';
