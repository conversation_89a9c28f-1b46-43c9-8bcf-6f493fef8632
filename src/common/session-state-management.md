# Session State Management: Implementation Guide

## Overview

This document outlines the implementation of robust state management for the Session component using a state machine pattern. The state management system provides explicit states, validated transitions, and comprehensive tracking of the session lifecycle.

## State Machine Design

### States

The session lifecycle is represented by the following states:

```typescript
export enum SessionState {
  INITIALIZING = 'INITIALIZING', // Session is being initialized
  IDLE = 'IDLE', // Session is waiting for user input
  LISTENING = 'LISTENING', // Session is actively listening for user input (ASR running). Entry starts speechToText phase, exit ends it. All user input and barge-in transitions route through here.
  PROCESSING_INPUT = 'PROCESSING_INPUT', // Session is processing user input (transcript/DTMF)
  PROCESSING_BOT = 'PROCESSING_BOT', // Session is waiting for bot response
  RESPONDING = 'RESPONDING', // Session is preparing response (TTS)
  PLAYING = 'PLAYING', // Session is playing audio to user
  DISCONNECTING = 'DISCONNECTING', // Session is in the process of disconnecting
  CLOSED = 'CLOSED', // Session is closed and resources released
}
```

### Valid Transitions

The state machine enforces valid transitions between states:

```
INITIALIZING → IDLE, DISCONNECTING
IDLE → LISTENING, DISCONNECTING
LISTENING → PROCESSING_INPUT, DISCONNECTING
PROCESSING_INPUT → PROCESSING_BOT, IDLE, DISCONNECTING
PROCESSING_BOT → RESPONDING, IDLE, DISCONNECTING
RESPONDING → PLAYING, IDLE, DISCONNECTING
PLAYING → LISTENING, IDLE, DISCONNECTING
DISCONNECTING → CLOSED
CLOSED → (no valid transitions)
```

Additionally, any state can transition to DISCONNECTING in case of errors.

### State Transition Metadata

Each state transition can include metadata to provide context:

```typescript
this.stateManager.setState(SessionState.PROCESSING_BOT, {
  reason: 'Processing transcript with bot',
  transcript: transcript.text,
});
```

This metadata is logged and can be used for debugging and metrics.

## Key Components

### SessionStateManager

The `SessionStateManager` class is responsible for:

1. Tracking the current state
2. Validating state transitions
3. Maintaining state history
4. Notifying listeners of state changes
5. Providing helper methods for state checks

### Session Integration

The Session class uses the state manager for:

1. Initializing the session
2. Processing user input
3. Handling barge-in
4. Managing audio playback
5. Closing the session

## Usage Examples

### State Transitions

```typescript
// Transition to PROCESSING_INPUT state
this.stateManager.setState(SessionState.PROCESSING_INPUT, {
  reason: 'Final transcript received',
  transcript: transcript.text,
});

// Try to transition (won't throw if invalid)
this.stateManager.trySetState(SessionState.IDLE, {
  reason: 'No bot response received',
});
```

### State Checks

```typescript
// Check if the session can accept input
if (!this.stateManager.canAcceptInput()) {
  logInfo(`Cannot accept input in current state: ${this.stateManager.getState()}`);
  return;
}

// Check if the session is in a specific state
if (this.stateManager.isProcessingBot()) {
  // Do something specific to this state
}
```

### State Change Listeners

```typescript
// Register a state change listener
this.stateManager.onStateChange((oldState, newState, metadata) => {
  logInfo(`State transition: ${oldState} → ${newState}`);

  // Log metrics for important state transitions
  if (newState === SessionState.PROCESSING_BOT) {
    logMetrics(`Processing bot response: ${metadata?.transcript}`);
  }
});
```

## Benefits

1. **Explicit State Management**: All session states and transitions are clearly defined and validated. The explicit `LISTENING` state acts as the async boundary for ASR (speechToText) phase management, ensuring robust, idempotent context handling and clear phase metrics.
2. **Improved Debugging**: State transitions are logged with metadata for easier troubleshooting.
3. **Reduced Bugs**: Invalid state transitions are prevented, reducing state-related bugs.
4. **Better Metrics**: State transitions can be tracked for performance monitoring.
5. **Cleaner Code**: State-dependent logic is centralized and consistent.

## Next Steps

1. Complete unit tests for the state machine
2. Refactor remaining Session methods to use state management
3. Extract additional responsibilities to specialized services
4. Add visualization tools for state transitions
