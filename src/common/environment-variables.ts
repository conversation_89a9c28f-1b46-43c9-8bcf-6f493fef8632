const DEFAULT_PORT = 8080;

export function getPort(): number {
  const envPort: string | undefined = process.env.PORT;

  if (envPort) {
    return Number(envPort);
  }

  return DEFAULT_PORT;
}

/**
 * Determines if barge-in functionality is enabled
 * @returns true if barge-in is enabled, false otherwise
 */
export function isBargeInEnabled(): boolean {
  return process.env.ENABLE_BARGE_IN === 'true';
}

/**
 * Gets the confidence threshold for speech barge-in detection
 * @returns The confidence threshold (0.0-1.0)
 */
export function getBargeInConfidenceThreshold(): number {
  return parseFloat(process.env.BARGE_IN_CONFIDENCE_THRESHOLD || '0.6');
}

/**
 * Gets the stability threshold for interim transcripts to be considered for barge-in
 * @returns The stability threshold (0.0-1.0)
 */
export function getInterimTranscriptStabilityThreshold(): number {
  return parseFloat(process.env.INTERIM_TRANSCRIPT_STABILITY_THRESHOLD || '0.5');
}

/**
 * Gets the pause threshold in milliseconds for stable stream detection
 * @returns The pause threshold in milliseconds
 */
export function getPauseThresholdMs(): number {
  return parseInt(process.env.PAUSE_THRESHOLD_MS || '1500', 10);
}

/**
 * Determines if stable stream processing is enabled
 * @returns true if stable stream is enabled, false otherwise
 */
export function isStableStreamEnabled(): boolean {
  return process.env.ENABLE_STABLE_STREAM === 'true';
}

/**
 * Gets the ASR streaming mode configuration
 * @returns The ASR streaming mode: 'standard', 'hybrid', or 'continuous'
 */
export function getASRStreamingMode(): 'standard' | 'hybrid' | 'continuous' {
  const mode = process.env.ASR_STREAMING_MODE?.toLowerCase();

  if (mode === 'continuous') {
    return 'continuous';
  } else if (mode === 'hybrid') {
    return 'hybrid';
  } else {
    return 'standard';
  }
}

/**
 * Determines if barge-in debug logging is enabled
 * @returns true if barge-in debug logging is enabled, false otherwise
 */
export function isBargeInDebugEnabled(): boolean {
  return process.env.DEBUG_BARGE_IN === 'true';
}

/**
 * Determines if ASR raw responses should be logged
 * @returns true if ASR raw responses should be logged, false otherwise
 */
export function logASRRawResponses(): boolean {
  return process.env.LOG_ASR_RAW_RESPONSES === 'true';
}

/**
 * Determines if ASR parsed results should be logged
 * @returns true if ASR parsed results should be logged, false otherwise
 */
export function logASRParsedResults(): boolean {
  return process.env.LOG_ASR_PARSED_RESULTS === 'true';
}

/**
 * Determines if unstable ASR transcripts should be logged
 * @returns true if unstable ASR transcripts should be logged, false otherwise
 */
export function logASRUnstableTranscripts(): boolean {
  return process.env.LOG_ASR_UNSTABLE_TRANSCRIPTS === 'true';
}
