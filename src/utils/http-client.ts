import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { getProxyConfig } from './proxy-config';

/**
 * Creates an axios instance with proper proxy configuration
 * @param baseURL Optional base URL for the client
 * @param defaultConfig Optional default configuration
 * @returns Configured axios instance
 */
export function createHttpClient(
  baseURL?: string,
  defaultConfig: AxiosRequestConfig = {}
): AxiosInstance {
  const config: AxiosRequestConfig = {
    ...defaultConfig,
    baseURL,
    // Always set proxy to false by default to ensure direct connections
    // This will be overridden if a proxy is explicitly configured
    proxy: false,
  };

  return axios.create(config);
}

/**
 * Creates an axios instance for external API calls with proper proxy handling
 * @param url The URL for the request (used to determine proxy configuration)
 * @param defaultConfig Optional default configuration
 * @returns Configured axios instance
 *
 * NOTE: When using this client, you should still pass the full URL to the request methods
 * to be explicit, e.g., client.get(url) instead of client.get("").
 */
export function createExternalHttpClient(
  url: string,
  defaultConfig: AxiosRequestConfig = {}
): AxiosInstance {
  const proxyConfig = getProxyConfig(url);

  console.log(
    `[HTTP] Creating client for ${url}${proxyConfig ? ' with proxy' : ' with direct connection'}`
  );

  const config: AxiosRequestConfig = {
    ...defaultConfig,
    // Use proxy configuration if available, otherwise force direct connection
    proxy: proxyConfig || false,
  };

  return axios.create(config);
}
