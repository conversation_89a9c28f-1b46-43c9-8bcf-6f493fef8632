# Utilities

This folder contains utility functions that are used throughout the application.

## Proxy Configuration

The `proxy-config.ts` file provides a utility for handling HTTP proxy configuration in the application.

### Features

- Automatically bypasses proxy for localhost and internal requests
- Uses environment variables (HTTP_PROXY, HTTPS_PROXY) for external requests
- Provides consistent proxy handling across the application

### Environment Variables

- `HTTP_PROXY` - HTTP proxy URL (e.g., "http://proxy.example.com:8080")
- `HTTPS_PROXY` - HTTPS proxy URL (takes precedence over HTTP_PROXY for HTTPS requests)
- `NO_PROXY` - Comma-separated list of hosts to bypass proxy (e.g., "localhost,127.0.0.1")

The utility automatically bypasses proxy for localhost regardless of the NO_PROXY setting.
