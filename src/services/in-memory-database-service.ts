// Database record types matching existing schemas
interface DbChatRecord {
  ani: string;
  conversation_id: string;
  message_text: string;
  is_bot: boolean;
  created_at: string;
}

interface DbUserProfileRecord {
  ani: string;
  name: string;
  created_at: string;
  updated_at: string;
}

interface DbSummaryRecord {
  ani: string;
  conversation_id: string;
  start_time: string;
  end_time: string;
  summary_text: string;
  message_count: number;
  duration_seconds: number;
  created_at: string;
}

// Application interface types
interface ChatMessage {
  ani: string;
  conversationId: string;
  messageText: string;
  isBot: boolean;
  createdAt?: Date;
}

interface UserProfile {
  ani: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

interface ConversationSummary {
  ani: string;
  conversationId: string;
  startTime: Date;
  endTime: Date;
  summaryText: string;
  messageCount: number;
  durationSeconds: number;
  createdAt?: Date;
}

class InMemoryDatabaseService {
  private static instance: InMemoryDatabaseService;
  private chatHistory: Map<string, DbChatRecord[]> = new Map();
  private summaries: DbSummaryRecord[] = [];
  private userProfiles: DbUserProfileRecord[] = [];
  private readonly MESSAGE_LIMIT = 50;

  private constructor() {
    console.log('[DB] Initializing in-memory database service');
  }

  static getInstance(): InMemoryDatabaseService {
    if (!InMemoryDatabaseService.instance) {
      InMemoryDatabaseService.instance = new InMemoryDatabaseService();
    }
    return InMemoryDatabaseService.instance;
  }

  private enforceMessageLimit(conversationId: string): void {
    const messages = this.chatHistory.get(conversationId);
    if (messages && messages.length > this.MESSAGE_LIMIT) {
      // Remove oldest messages to maintain limit
      this.chatHistory.set(conversationId, messages.slice(-this.MESSAGE_LIMIT));
      console.log(`[DB] Trimmed conversation ${conversationId} to ${this.MESSAGE_LIMIT} messages`);
    }
  }

  async storeMessage(message: ChatMessage): Promise<void> {
    try {
      const record: DbChatRecord = {
        ani: message.ani,
        conversation_id: message.conversationId,
        message_text: message.messageText,
        is_bot: message.isBot,
        created_at: new Date().toISOString(),
      };

      const conversationId = message.conversationId;
      const existingMessages = this.chatHistory.get(conversationId) || [];

      this.chatHistory.set(conversationId, [...existingMessages, record]);
      this.enforceMessageLimit(conversationId);

      console.log('[DB] Message stored successfully');
    } catch (error) {
      console.error('[DB] Error storing message:', error);
      throw error;
    }
  }

  async getConversationHistory(ani: string, conversationId: string): Promise<ChatMessage[]> {
    try {
      const messages = this.chatHistory.get(conversationId) || [];
      const filteredMessages = messages.filter(record => record.ani === ani);

      return filteredMessages.map(record => ({
        ani: record.ani,
        conversationId: record.conversation_id,
        messageText: record.message_text,
        isBot: record.is_bot,
        createdAt: new Date(record.created_at),
      }));
    } catch (error) {
      console.error('[DB] Error fetching conversation history:', error);
      throw error;
    }
  }

  // getChatHistoryNonVoice method removed - chat functionality no longer needed

  async getLastSummaries(ani: string, limit = 5): Promise<ConversationSummary[]> {
    try {
      const records = this.summaries
        .filter(record => record.ani === ani)
        .sort((a, b) => b.end_time.localeCompare(a.end_time))
        .slice(0, limit);

      return records.map(record => ({
        ani: record.ani,
        conversationId: record.conversation_id,
        startTime: new Date(record.start_time),
        endTime: new Date(record.end_time),
        summaryText: record.summary_text,
        messageCount: record.message_count,
        durationSeconds: record.duration_seconds,
        createdAt: new Date(record.created_at),
      }));
    } catch (error) {
      console.error('[DB] Error fetching conversation summaries:', error);
      throw error;
    }
  }

  async getLastSummary(ani: string): Promise<ConversationSummary | null> {
    try {
      const record =
        this.summaries
          .filter(record => record.ani === ani)
          .sort((a, b) => b.end_time.localeCompare(a.end_time))[0] || null;

      if (!record) {
        return null;
      }

      return {
        ani: record.ani,
        conversationId: record.conversation_id,
        startTime: new Date(record.start_time),
        endTime: new Date(record.end_time),
        summaryText: record.summary_text,
        messageCount: record.message_count,
        durationSeconds: record.duration_seconds,
        createdAt: new Date(record.created_at),
      };
    } catch (error) {
      console.error('[DB] Error fetching last summary:', error);
      throw error;
    }
  }

  async storeSummary(summary: ConversationSummary): Promise<void> {
    try {
      const record: DbSummaryRecord = {
        ani: summary.ani,
        conversation_id: summary.conversationId,
        start_time: summary.startTime.toISOString(),
        end_time: summary.endTime.toISOString(),
        summary_text: summary.summaryText,
        message_count: summary.messageCount,
        duration_seconds: summary.durationSeconds,
        created_at: new Date().toISOString(),
      };

      this.summaries.push(record);
      console.log('[DB] Summary stored successfully');
    } catch (error) {
      console.error('[DB] Error storing summary:', error);
      throw error;
    }
  }

  async storeUserProfile(profile: UserProfile): Promise<void> {
    try {
      const record: DbUserProfileRecord = {
        ani: profile.ani,
        name: profile.name,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Update existing or add new
      const existingIndex = this.userProfiles.findIndex(p => p.ani === profile.ani);
      if (existingIndex >= 0) {
        this.userProfiles[existingIndex] = record;
      } else {
        this.userProfiles.push(record);
      }

      console.log('[DB] User profile stored successfully');
    } catch (error) {
      console.error('[DB] Error storing user profile:', error);
      throw error;
    }
  }

  async getUserProfile(ani: string): Promise<UserProfile | null> {
    try {
      const record = this.userProfiles.find(p => p.ani === ani);
      if (!record) {
        return null;
      }

      return {
        ani: record.ani,
        name: record.name,
        createdAt: new Date(record.created_at),
        updatedAt: new Date(record.updated_at),
      };
    } catch (error) {
      console.error('[DB] Error fetching user profile:', error);
      throw error;
    }
  }

  async checkHealth(): Promise<{ status: string; error?: string }> {
    return { status: 'healthy' };
  }

  // Method for testing and debugging
  getStats(): {
    conversationCount: number;
    totalMessages: number;
    summaryCount: number;
    userProfileCount: number;
  } {
    let totalMessages = 0;
    this.chatHistory.forEach(messages => {
      totalMessages += messages.length;
    });

    return {
      conversationCount: this.chatHistory.size,
      totalMessages,
      summaryCount: this.summaries.length,
      userProfileCount: this.userProfiles.length,
    };
  }
}

export { InMemoryDatabaseService, type ChatMessage, type ConversationSummary, type UserProfile };
