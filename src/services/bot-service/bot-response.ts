import { BotTurnDisposition } from '../../protocol/voice-bots';

/**
 * Represents a response from the bot
 */
export class BotResponse {
  disposition: BotTurnDisposition;
  text: string;
  confidence?: number;
  audioBytes?: Uint8Array;
  endSession?: boolean;
  endSessionReason?: 'end' | 'agent';

  /**
   * Create a new bot response
   * @param disposition The disposition of the response
   * @param text The text of the response
   */
  constructor(disposition: BotTurnDisposition, text: string) {
    this.disposition = disposition;
    this.text = text;
  }

  /**
   * Set the confidence level of the response
   * @param confidence The confidence level (0-1)
   * @returns This BotResponse instance for chaining
   */
  withConfidence(confidence: number): BotResponse {
    this.confidence = confidence;
    return this;
  }

  /**
   * Set the audio bytes for the response
   * @param audioBytes The audio bytes
   * @returns This BotResponse instance for chaining
   */
  withAudioBytes(audioBytes: Uint8Array): BotResponse {
    this.audioBytes = audioBytes;
    return this;
  }

  /**
   * Set whether the session should end
   * @param endSession Whether to end the session
   * @param reason The reason for ending the session
   * @returns This BotResponse instance for chaining
   */
  withEndSession(endSession: boolean, reason?: 'end' | 'agent'): BotResponse {
    this.endSession = endSession;
    if (reason) {
      this.endSessionReason = reason;
    }
    return this;
  }
}
