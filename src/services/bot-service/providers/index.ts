import { LLMProvider } from '../types';
import { AzureOpenAIProvider } from './azure-openai';
import { LiteLLMProvider } from './litellm';

/**
 * Available LLM provider types
 */
export type LLMProviderType = 'azure' | 'litellm';

/**
 * Factory for creating LLM providers
 */
export class LLMProviderFactory {
  /**
   * Get an LLM provider instance based on the specified type
   * @param type The type of LLM provider to create
   * @returns An instance of the specified LLM provider
   */
  static getProvider(type: LLMProviderType): LLMProvider {
    switch (type) {
      case 'azure':
        return new AzureOpenAIProvider();
      case 'litellm':
        return new LiteLLMProvider();
      default:
        throw new Error(`Unknown LLM provider type: ${type}`);
    }
  }
}
