import { LLMProvider, LLMProviderOptions, ChatMessage } from '../types';
import { createExternalHttpClient } from '../../../utils/http-client';
import axios from 'axios';
import * as https from 'https';
import {
  logInfo,
  logDebug,
  logError,
  logOpenAIRequest,
  logOpenAIResponse,
  shouldLogOpenAIRequests,
  shouldLogOpenAIResponses,
} from '../../../services/logging/logger';

/**
 * LiteLLM implementation of the LLM provider interface
 */
export class LiteLLMProvider implements LLMProvider {
  private apiKey: string | null = null;
  private endpoint: string | null = null;
  private model: string | null = null;

  /**
   * Initialize the LiteLLM provider with configuration from environment variables
   */
  async initialize(): Promise<void> {
    try {
      const key = process.env.LITELLM_API_KEY;
      const endpoint = process.env.LITELLM_ENDPOINT;
      const model = process.env.LITELLM_MODEL || 'gpt-4';

      if (!key || !endpoint) {
        throw new Error(
          'Missing required LiteLLM configuration. Check LITELLM_API_KEY and LITELLM_ENDPOINT environment variables.'
        );
      }

      this.apiKey = key;
      this.endpoint = endpoint;
      this.model = model;

      logDebug(`[LLM] LiteLLM provider initialized with model: ${this.model}`);
    } catch (error) {
      logError(
        `Failed to initialize LiteLLM provider: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  /**
   * Get a chat completion from LiteLLM
   * @param messages The conversation history
   * @param options Options for the completion request
   * @returns The LLM's response text
   */
  async getChatCompletion(messages: ChatMessage[], options?: LLMProviderOptions): Promise<string> {
    if (!this.apiKey || !this.endpoint || !this.model) {
      throw new Error('LiteLLM provider not properly initialized');
    }

    // Check if the request has been aborted
    if (options?.abortSignal && options.abortSignal.aborted) {
      logDebug('[LLM] LiteLLM request aborted before sending');
      throw new Error('Request aborted');
    }

    try {
      // LiteLLM API endpoint for chat completions
      const url = `${this.endpoint}/v1/chat/completions`;

      // LiteLLM follows OpenAI API format
      const requestPayload = {
        model: this.model,
        messages,
        temperature: options?.temperature ?? 0.7,
        max_tokens: options?.maxTokens ?? 800,
        top_p: options?.topP ?? 0.95,
        frequency_penalty: options?.frequencyPenalty ?? 0,
        presence_penalty: options?.presencePenalty ?? 0,
        stop: options?.stop ?? null,
      };

      // Log request details if enabled
      if (shouldLogOpenAIRequests) {
        // Get the log format from environment variables
        const logFormat = process.env.OPENAI_LOG_FORMAT || 'standard';

        if (logFormat === 'minimal') {
          // For minimal format, just log the last user message
          const userMessages = messages
            .filter(msg => msg.role === 'user')
            .map(msg => msg.content)
            .filter(Boolean);

          const lastUserMessage =
            userMessages.length > 0 ? userMessages[userMessages.length - 1] : '';
          logDebug(
            `[LLM] User: "${lastUserMessage ? lastUserMessage.substring(0, 30) + '...' : ''}"`
          );
        } else {
          logOpenAIRequest(`[LLM] LiteLLM Request: ${this.model}`);

          // Extract just the user messages for a more concise log
          const userMessages = messages
            .filter(msg => msg.role === 'user')
            .map(msg => msg.content)
            .filter(Boolean);

          logDebug(`[LLM] User messages: ${userMessages.join(' | ')}`);
        }
      }

      // Create a client specifically for this external API call
      const client = createExternalHttpClient(url, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      // Log the request URL if enabled
      if (shouldLogOpenAIRequests) {
        logDebug(`[LLM] Making LiteLLM request to ${url}`);
      }

      // Create axios request config with abort signal
      const requestConfig: any = {};
      if (options?.abortSignal) {
        requestConfig.signal = options.abortSignal;
      }

      /**
       * WARNING: The following block disables SSL certificate verification for LiteLLM requests
       * if the environment variable LITELLM_INSECURE_SSL is set to 'true'.
       * This should ONLY be used for development or testing purposes.
       * NEVER use this in production as it makes HTTPS connections insecure and vulnerable to MITM attacks.
       */
      if (process.env.LITELLM_INSECURE_SSL === 'true') {
        logDebug('[LLM] Using insecure connection to LiteLLM');
        requestConfig.httpsAgent = new https.Agent({ rejectUnauthorized: false });
      }

      // Check again if the request has been aborted
      if (options?.abortSignal && options.abortSignal.aborted) {
        logDebug('[LLM] LiteLLM request aborted before sending');
        throw new Error('Request aborted');
      }

      // Pass the full URL to be explicit
      const response = await client.post(url, requestPayload, requestConfig);

      // Check if the request has been aborted after receiving response
      if (options?.abortSignal && options.abortSignal.aborted) {
        logDebug('[LLM] LiteLLM request aborted after receiving response');
        throw new Error('Request aborted');
      }

      // LiteLLM follows OpenAI response format
      const result = response.data;
      const responseMessage = result.choices?.[0]?.message;

      if (!responseMessage?.content) {
        throw new Error('No response from LiteLLM');
      }

      // Log the response if enabled
      if (shouldLogOpenAIResponses) {
        // Get the log format from environment variables
        const logFormat = process.env.OPENAI_LOG_FORMAT || 'standard';

        if (logFormat === 'minimal') {
          // For minimal format, just log a truncated response
          logDebug(
            `[LLM] AI: "${
              responseMessage.content.length > 30
                ? responseMessage.content.substring(0, 30) + '...'
                : responseMessage.content
            }"`
          );
        } else {
          logOpenAIResponse(`[LLM] LiteLLM response: ${responseMessage.content}`);
        }
      }

      return responseMessage.content;
    } catch (error) {
      // If the error is due to an aborted request, handle it gracefully
      if (options?.abortSignal && options.abortSignal.aborted) {
        logDebug('[LLM] LiteLLM request aborted');
        throw new Error('Request aborted');
      }

      if (axios.isAxiosError(error) && error.response) {
        logError(
          `LiteLLM Error Response: Status ${error.response.status} - ${error.response.statusText}`
        );
        // Log more details at debug level if needed
      }
      logError(
        `Error getting LiteLLM response: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  /**
   * Clean up resources used by the provider
   */
  async dispose(): Promise<void> {
    this.apiKey = null;
    this.endpoint = null;
    this.model = null;
    logDebug('[LLM] LiteLLM provider disposed');
  }
}
