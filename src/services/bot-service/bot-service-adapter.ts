/**
 * BotServiceAdapter
 *
 * Adapts the Session class to the BotService infrastructure.
 * This adapter encapsulates all bot-related functionality that was previously
 * embedded in the Session class, providing a clean interface for bot interactions.
 */

import { BotService, BotResource, BotResponse } from './index';
import { SessionState } from '../../session/session-state-manager';
import { EventDrivenStateManager } from '../../session/state-machine/event-driven-state-manager';
import { logInfo, logWarning, logDebug, logError } from '../logging/logger';
import { Transcript } from '../speech';
import { JsonStringMap } from '../../protocol/core';
import { loggingConfig, shouldLogMessage } from '../logging/logging-config';
import { RequestContext } from '../monitoring/performance-logger';
import { Session } from '../../session/session';

export class BotServiceAdapter {
  private botService: BotService;
  private selectedBot: BotResource | null = null;
  private stateManager: EventDrivenStateManager;
  private conversationId?: string;
  private ani?: string;
  private isProcessingResponse = false;
  private session: any; // Reference to the session object

  constructor(botService: BotService, stateManager: EventDrivenStateManager) {
    this.botService = botService;
    this.stateManager = stateManager;

    // Get the session from the state manager
    // This is a bit of a hack, but it's the easiest way to get the session
    this.session = stateManager['session'];
  }

  /**
   * Set the conversation ID for bot interactions
   * @param conversationId The conversation ID to use for bot interactions
   */
  setConversationId(conversationId: string): void {
    this.conversationId = conversationId;
    logDebug(`[BotServiceAdapter] Set conversation ID: ${conversationId}`);
  }

  /**
   * Set the ANI (caller ID) for bot interactions
   * @param ani The ANI value to use for bot interactions
   */
  setAni(ani: string): void {
    this.ani = ani;
    logDebug(`[BotServiceAdapter] Set ANI: ${ani}`);
  }

  /**
   * Check if a bot exists for this session
   */
  async checkIfBotExists(): Promise<boolean> {
    // Only log this message periodically to reduce noise
    if (shouldLogMessage('botServiceCheck', loggingConfig.botServiceCheckInterval)) {
      logDebug(
        `[BotServiceAdapter] Checking if bot exists for conversationId: ${this.conversationId}, ani: ${this.ani}`
      );
    }

    // If conversationId is missing, we can't proceed
    if (!this.conversationId) {
      logWarning('[BotServiceAdapter] Missing conversationId, cannot check if bot exists');
      return false;
    }

    // If ANI is missing, use a default value
    const effectiveAni = this.ani || 'unknown';
    if (!this.ani) {
      logWarning('[BotServiceAdapter] Missing ani, using default value: unknown');
    }

    // Try to get the bot with the available information
    this.selectedBot = await this.botService.getBotIfExists(this.conversationId, effectiveAni);

    if (this.selectedBot) {
      logDebug(`[BotServiceAdapter] Bot found for conversationId: ${this.conversationId}`);
      return true;
    } else {
      logWarning(`[BotServiceAdapter] No bot found for conversationId: ${this.conversationId}`);
      return false;
    }
  }

  /**
   * Synchronous check if a bot exists for this session
   * This is used for operations that can't be async, like DTMF processing
   */
  checkIfBotExistsSync(): boolean {
    return this.selectedBot != null;
  }

  /**
   * Get the initial bot response
   * This method is called when the session is first opened
   *
   * @param metricsContext Optional metrics context for tracking
   * @param abortSignal Optional AbortSignal to cancel the request
   * @deprecated session parameter removed
   * @returns The initial bot response or null if no bot is available
   */
  async getInitialResponse(
    metricsContext: RequestContext,
    abortSignal?: AbortSignal
  ): Promise<BotResponse | null> {
    if (!this.selectedBot) {
      logDebug('[BotServiceAdapter] No bot selected, cannot get initial response');
      return null;
    }

    // Check if the request has been aborted
    if (abortSignal && abortSignal.aborted) {
      logDebug('[BotServiceAdapter] Initial response request aborted before processing');
      return null;
    }

    // For initial greeting, the Session.processBotStart method now handles
    // the direct transition from IDLE to PROCESSING_BOT, so we don't need
    // to transition through PROCESSING_INPUT anymore.

    // Just log the current state for debugging
    logDebug(`[BotServiceAdapter] Current state: ${this.stateManager.getState()}`);

    // No state transitions needed here - they're handled by Session.processBotStart

    // Start the llmProcessing phase if we have a metrics context
    metricsContext.trackPhase('llmProcessing');

    try {
      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logDebug(
          '[BotServiceAdapter] Initial response request aborted before getting bot response'
        );
        return null;
      }

      // Check the current state
      const currentState = this.stateManager.getState();
      logDebug(`[BotServiceAdapter] Current state before getting bot response: ${currentState}`);

      // No state transitions here. The session is responsible for ensuring the correct state before calling this method.

      // Get the initial response from the bot
      logDebug('[BotServiceAdapter] Getting initial response from bot');
      const response = await this.selectedBot.getInitialResponse(metricsContext, abortSignal);

      // Check if the request has been aborted
      if (abortSignal && abortSignal.aborted) {
        logDebug('[BotServiceAdapter] Initial response request aborted after getting bot response');
        return null;
      }

      // Store the AI reply for metrics as soon as the LLM response is available
      if (
        response &&
        response.text &&
        this.session &&
        typeof this.session.getMetricsAdapter === 'function'
      ) {
        const metricsAdapter = this.session.getMetricsAdapter();
        if (metricsAdapter && typeof metricsAdapter.setAiReply === 'function') {
          metricsAdapter.setAiReply(response.text);
        }
      }

      // The bot response should be stored on the session by the caller, not here.

      // No state transitions here. The session is responsible for transitioning to RESPONDING after PROCESSING_BOT.

      return response;
    } finally {
      // Always end the phase, even if an error occurs
      if (metricsContext) {
        metricsContext.endPhase('llmProcessing');
      }
    }
  }

  /**
   * Process user input and get a bot response
   */
  async processUserInput(
    transcript: Transcript,
    metricsContext?: RequestContext,
    abortSignal?: AbortSignal
  ): Promise<{ success: boolean; response?: BotResponse }> {
    // Check if we have a bot and aren't already processing
    if (!this.selectedBot) {
      logDebug(
        `[BotServiceAdapter] No bot selected, cannot process transcript: "${transcript.text}"`
      );
      return { success: false };
    }

    if (this.isProcessingResponse) {
      logDebug(
        `[BotServiceAdapter] Already processing a response, ignoring duplicate request for: "${transcript.text}"`
      );
      return { success: false };
    }

    // Set processing flag and ensure we're in PROCESSING_BOT state
    this.isProcessingResponse = true;

    try {
      if (!this.stateManager.isProcessingBot()) {
        // Use setState with await to ensure proper execution of state actions
        await this.stateManager.setState(SessionState.PROCESSING_BOT, {
          reason: 'Processing bot response',
          transcript: transcript.text,
        });
      }
    } catch (err) {
      // If we get a state transition error, log it but continue
      if (err instanceof Error && err.message.includes('Cannot start new transition')) {
        logWarning(`[BotServiceAdapter] State transition error: ${err.message}`);
        // Continue processing even if we couldn't transition to PROCESSING_BOT
        // The state machine will handle the transition later
      } else {
        // For other errors, rethrow
        throw err;
      }
    }

    try {
      // Get bot response with metrics tracking
      const result = await this.getBotResponseWithMetrics(
        transcript.text,
        metricsContext,
        abortSignal
      );

      if (!result.success || !result.response) {
        logDebug(`[BotServiceAdapter] No result from bot for transcript: "${transcript.text}"`);
        // Transition back to IDLE if we didn't get a response
        // Use setState with await to ensure proper execution of state actions
        await this.stateManager.setState(SessionState.IDLE, {
          reason: 'No bot response received',
        });
        return result;
      }

      // Store the bot response on the session
      if (result.response) {
        logDebug(
          `[BotServiceAdapter] Storing bot response on session (${
            result.response.text?.length || 0
          } chars, ${result.response.audioBytes?.length || 0} bytes audio)`
        );
        this.session.setLatestBotResponse(result.response);
      }

      // Set the AI reply in the metrics context as soon as the LLM response is available (user input turns)
      if (
        result.response &&
        result.response.text &&
        this.session &&
        typeof this.session.getMetricsAdapter === 'function'
      ) {
        const metricsAdapter = this.session.getMetricsAdapter();
        if (metricsAdapter && typeof metricsAdapter.setAiReply === 'function') {
          metricsAdapter.setAiReply(result.response.text);
        }
      }

      // IMPORTANT: We no longer directly transition to RESPONDING state here.
      // Instead, we let the event-driven architecture handle the transition.
      // The BOT_RESPONSE_RECEIVED event will be emitted by HandleBotResponseAction
      // or by the asynchronous bot processing in ProcessUserInputAction.

      return result;
    } finally {
      // Reset processing flag
      this.isProcessingResponse = false;
    }
  }

  /**
   * Get a bot response with metrics tracking
   */
  private async getBotResponseWithMetrics(
    text: string,
    metricsContext?: RequestContext,
    abortSignal?: AbortSignal
  ): Promise<{ success: boolean; response?: BotResponse }> {
    // Ensure we have a bot
    if (!this.selectedBot) {
      logDebug('[BotServiceAdapter] No bot selected, cannot process user input');
      return { success: false };
    }

    // Process the user input with the bot
    // If we don't have a metrics context, create a dummy one
    if (!metricsContext) {
      logError(
        '[BotServiceAdapter] No metrics context provided for bot interaction. Metrics will not be recorded.'
      );
      return { success: false };
    }

    // Process with the provided metrics context
    const result = await this.botService.processUserInput(
      this.selectedBot,
      text,
      metricsContext,
      abortSignal
    );

    return result;
  }

  /**
   * Check if the session should end based on the bot response
   */
  shouldEndSession(response: BotResponse): {
    shouldEnd: boolean;
    reason?: string;
    outputVariables?: JsonStringMap;
  } {
    if (!response.endSession) {
      return { shouldEnd: false };
    }

    return {
      shouldEnd: true,
      reason: response.endSessionReason || 'completed',
      outputVariables: {
        toAgent: response.endSessionReason === 'agent' ? 'true' : 'false',
      },
    };
  }

  /**
   * Cancel the current bot turn (used for barge-in)
   */
  cancelCurrentTurn(): void {
    if (this.selectedBot) {
      this.selectedBot.cancelCurrentTurn();
      logDebug('[BotServiceAdapter] Cancelled current bot turn');
    }
  }

  /**
   * Dispose of bot resources
   */
  async dispose(): Promise<void> {
    if (this.selectedBot) {
      await this.selectedBot.dispose();
      this.selectedBot = null;
    }
  }
}
