/**
 * Shared chat message types for LLM interactions
 */
export interface ChatMessage {
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string | null;
  tool_call_id?: string;
  name?: string;
  tool_calls?: Array<{
    id: string;
    type: 'function';
    function: {
      name: string;
      arguments: string;
    };
  }>;
}

/**
 * Options for LLM provider chat completion requests
 */
export interface LLMProviderOptions {
  /** Controls randomness: 0 = deterministic, 1 = maximum randomness */
  temperature?: number;

  /** Maximum number of tokens to generate */
  maxTokens?: number;

  /** Controls diversity via nucleus sampling */
  topP?: number;

  /** Penalizes repeated tokens */
  frequencyPenalty?: number;

  /** Penalizes repeated topics */
  presencePenalty?: number;

  /** Sequences where the API will stop generating further tokens */
  stop?: string[] | null;

  /** AbortSignal to cancel the request */
  abortSignal?: AbortSignal;
}

/**
 * Interface that all LLM providers must implement
 */
export interface LLMProvider {
  /**
   * Initialize the provider with configuration
   */
  initialize(): Promise<void>;

  /**
   * Get a chat completion from the LLM
   * @param messages The conversation history
   * @param options Options for the completion request
   * @returns The LLM's response text
   */
  getChatCompletion(messages: ChatMessage[], options?: LLMProviderOptions): Promise<string>;

  /**
   * Clean up resources used by the provider
   */
  dispose(): Promise<void>;
}
