# Bot Service Refactoring Plan

## Overview

This document outlines the plan to refactor the Bot Service to support multiple LLM providers, including Azure OpenAI and a corporate-hosted solution. The refactoring will use the Strategy pattern to allow easy switching between different LLM providers.

## Current Architecture

Currently, the Bot Service is tightly coupled with Azure OpenAI:

- `BotService` class manages bot instances
- `BotResource` class handles interactions with Azure OpenAI
- The OpenAI API calls are hardcoded in the `BotResource` class

## Target Architecture

The refactored architecture will:

1. Abstract LLM provider functionality behind interfaces
2. Implement provider-specific classes
3. Use a factory to create the appropriate provider
4. Allow runtime switching between providers via configuration

## Implementation Steps

### [x] 1. Create Folder Structure

---

## Service Call Measurement

All LLM provider calls are measured using the `callWithMeasurement` utility at the top-level service entry points (e.g., in `BotResource` methods). This ensures that every LLM completion is timed and logged as a service timing entry in the metrics context.

- **Provider-level methods** (e.g., direct LLM provider calls) are not measured unless called directly in tests or custom code.
- **Best Practice:** Always use the top-level bot service for LLM completions to ensure metrics are recorded.

---

```
src/services/bot-service/
├── index.ts                  # Main entry point, exports public API
├── bot-service.ts            # BotService class (manages instances)
├── bot-resource.ts           # BotResource class (uses LLM provider)
├── bot-response.ts           # BotResponse class
├── types/
│   ├── index.ts              # Exports all types
│   ├── chat-message.ts       # ChatMessage interface
│   └── llm-provider.ts       # LLM provider interfaces
└── providers/
    ├── index.ts              # Provider factory
    ├── azure-openai.ts       # Azure OpenAI implementation
    └── litellm.ts            # LiteLLM implementation
```

### [x] 2. Define LLM Provider Interface

Create an interface that all LLM providers must implement:

- `initialize()`: Set up the provider with configuration
- `getChatCompletion()`: Get a response from the LLM
- `dispose()`: Clean up resources

### [x] 3. Implement Provider-Specific Classes

- `AzureOpenAIProvider`: Implementation for Azure OpenAI
- `LiteLLMProvider`: Implementation for LiteLLM

### [x] 4. Create Provider Factory

Implement a factory that creates the appropriate provider based on configuration.

### [x] 5. Refactor BotResource

Update `BotResource` to use the LLM provider interface instead of direct API calls.

### [x] 6. Update Configuration

Add environment variables for provider selection and configuration.

### [x] 7. Create Entry Point

Create an `index.ts` file that exports the public API.

### [ ] 8. Test Implementation

Test with both Azure OpenAI and corporate LLM providers.

## Detailed Interface Definitions

### LLM Provider Interface

```typescript
export interface LLMProviderOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[] | null;
}

export interface LLMProvider {
  initialize(): Promise<void>;
  getChatCompletion(messages: ChatMessage[], options?: LLMProviderOptions): Promise<string>;
  dispose(): Promise<void>;
}
```

### Provider Factory

```typescript
export type LLMProviderType = 'azure' | 'litellm';

export class LLMProviderFactory {
  static getProvider(type: LLMProviderType): LLMProvider {
    switch (type) {
      case 'azure':
        return new AzureOpenAIProvider();
      case 'litellm':
        return new LiteLLMProvider();
      default:
        throw new Error(`Unknown LLM provider type: ${type}`);
    }
  }
}
```

## Configuration

Environment variables for provider selection:

```
# LLM Provider Configuration
LLM_PROVIDER_TYPE=azure  # or 'litellm'

# Azure OpenAI Configuration
AZURE_OPENAI_KEY=your-azure-key
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
AZURE_OPENAI_DEPLOYMENT_NAME=your-deployment-name

# LiteLLM Configuration
LITELLM_API_KEY=your-litellm-api-key
LITELLM_ENDPOINT=https://litellm.ai-sandbox.azure.to2cz.cz
LITELLM_MODEL=gpt-4
```

## Benefits

1. **Flexibility**: Easily switch between different LLM providers
2. **Maintainability**: Clean separation of concerns
3. **Extensibility**: Add new providers by implementing the interface
4. **Testability**: Mock the LLM provider for testing
