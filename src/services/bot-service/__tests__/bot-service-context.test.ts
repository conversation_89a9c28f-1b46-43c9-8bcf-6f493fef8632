jest.mock('../../tts-service', () => ({
  getTTSService: jest.fn().mockResolvedValue({}),
}));

import { BotResource } from '../bot-resource';
import { InMemoryDatabaseService } from '../../in-memory-database-service';
import { LLMProviderFactory } from '../providers';

describe('BotResource (tool/customer context integration)', () => {
  it('should include customer/tool context in the LLM system prompt', async () => {
    // Arrange: mock tool result (e.g., customer info)
    const customerInfo = { ani: '12345', name: 'Test User', metadata: { status: 'VIP' } };

    // Mock LLM provider to capture the system prompt
    const mockLLMProvider = {
      initialize: jest.fn(),
      getChatCompletion: jest.fn().mockResolvedValue('Hello, Test User!'),
      dispose: jest.fn(),
    };

    // Mock DB service
    const dbService = {
      getLastSummary: jest.fn().mockResolvedValue(null),
      getConversationHistory: jest.fn().mockResolvedValue([]),
    } as unknown as InMemoryDatabaseService;

    // Mock LLMProviderFactory to return the mockLLMProvider
    jest.spyOn(LLMProviderFactory, 'getProvider').mockReturnValue(mockLLMProvider);

    // Create BotResource using the static factory, passing tool context up front
    const botResource = await BotResource.create('12345', 'conv-1', dbService, customerInfo);
    (botResource as any).llmProvider = mockLLMProvider;

    // Act: get a response from the bot
    const mockRequestContext = { requestId: 'req-1' } as any;
    await botResource.getInitialResponse(mockRequestContext);

    // Assert: system prompt passed to LLM should include customer name/status
    const promptArg = mockLLMProvider.getChatCompletion.mock.calls[0][0][0].content;
    expect(promptArg).toContain('Test User');
    expect(promptArg).toContain('VIP');
  });
});
