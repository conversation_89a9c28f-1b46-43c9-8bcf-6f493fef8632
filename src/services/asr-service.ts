import { ServiceContainer, getSpeechConfig, BaseSpeechService, Transcript } from './speech';

let service: BaseSpeechService | null = null;

export { Transcript }; // Re-export for backward compatibility

/**
 * Get the ASR service instance
 * Creates a new instance if one doesn't exist
 */
export async function getASRService(): Promise<BaseSpeechService> {
  if (!service) {
    try {
      const config = getSpeechConfig();
      const container = ServiceContainer.getInstance();

      // Initialize container if not already initialized or if initialization failed
      try {
        container.getSpeechFactory();
      } catch (error) {
        container.initialize(config);
      }

      service = await container.getSpeechFactory().createASRService();
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error('[ASR] Failed to initialize service:', message);
      throw new Error(`Failed to initialize ASR service: ${message}`);
    }
  }

  if (!service) {
    throw new Error('Failed to create ASR service');
  }

  return service;
}

/**
 * Dispose of the current ASR service instance
 */
export function disposeASRService(): void {
  if (service) {
    service.dispose();
    service = null;
  }
}
