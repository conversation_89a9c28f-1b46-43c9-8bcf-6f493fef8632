import { ServiceContainer, getSpeechConfig, BaseTTSService } from './speech';

let service: BaseTTSService | null = null;

/**
 * Get the TTS service instance
 * Creates a new instance if one doesn't exist
 */
export async function getTTSService(): Promise<BaseTTSService> {
  if (!service) {
    try {
      const config = getSpeechConfig();
      const container = ServiceContainer.getInstance();

      // Initialize container if not already initialized or if initialization failed
      try {
        container.getSpeechFactory();
      } catch (error) {
        container.initialize(config);
      }

      const newService = container.getSpeechFactory().createTTSService();
      service = newService;
      // Initialize the service and wait for completion
      // try {
      //   // Give the service time to initialize
      //   await new Promise<void>((resolve, reject) => {
      //     // Attempt to get audio to trigger initialization
      //     newService
      //       .getAudioBytes("test")
      //       .then(() => {
      //         service = newService;
      //         resolve();
      //       })
      //       .catch(reject);

      //     // Set a timeout in case initialization takes too long
      //     setTimeout(() => {
      //       reject(new Error("TTS service initialization timed out"));
      //     }, 10000); // 10 second timeout
      //   });
      // } catch (error) {
      //   console.error("[TTS] Service initialization failed:", error);
      //   throw error;
      // }
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error('[TTS] Failed to initialize service:', message);
      throw new Error(`Failed to initialize TTS service: ${message}`);
    }
  }

  if (!service) {
    throw new Error('Failed to create TTS service');
  }

  return service;
}

/**
 * Dispose of the current TTS service instance
 */
export function disposeTTSService(): void {
  if (service) {
    service.dispose();
    service = null;
  }
}
