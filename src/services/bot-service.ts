/**
 * This file is deprecated and will be removed in a future release.
 * Please use the implementation in src/services/bot-service/index.ts instead.
 *
 * TODO: Remove this file by Q2 2024 after ensuring all imports use the new location.
 *
 * @deprecated Use the implementation in src/services/bot-service/index.ts instead.
 */

import { BotResource } from './bot-service/bot-resource';
import { BotService as NewBotService } from './bot-service/bot-service';
import { BotResponse } from './bot-service/bot-response';

/**
 * Re-export the BotService class from the new location for backward compatibility.
 * @deprecated Use the implementation in src/services/bot-service/index.ts instead.
 */
export class BotService extends NewBotService {}

/**
 * Re-export the BotResource class from the new location for backward compatibility.
 * @deprecated Use the implementation in src/services/bot-service/index.ts instead.
 */
export { BotResource, BotResponse };
