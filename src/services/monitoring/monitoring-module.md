# Monitoring & Metrics Module

## Overview

This module provides robust, request- and conversation-scoped metrics for the AudioConnector system. It enables detailed tracking of ASR, LLM, and TTS phases, service timings, and system-level metrics, supporting both real-time monitoring and post-hoc analysis.

## Architecture

- **PerformanceLogger**: Singleton for system-level and advanced metrics. Not recommended for direct use in request/session flows.
- **RequestContext**: The primary API for request-scoped metrics. Use this for all per-request phase tracking, timings, and user/AI data.
- **SessionMetricsAdapter**: Bridges the session state machine to the metrics system, ensuring correct phase status and deduplication.
- **SystemMetricsCollector**: Gathers system-level resource and health metrics.

## Implementation Checklist

- [x] Explicit phase management via `trackPhase`/`endPhase` on `RequestContext`
- [x] All code and tests migrated off `PhaseContext`
- [x] `SessionMetricsAdapter` used for all session/user flows
- [x] Documentation and examples updated

## Usage Example

```ts
import { SessionMetricsAdapter } from './session-metrics-adapter';

const adapter = new SessionMetricsAdapter(stateManager);
adapter.setConversationId(conversationId);
const request = await adapter.startRequest(userInput);

adapter.startPhase('speechToText');
// ...ASR...
adapter.endPhase('speechToText');

adapter.startPhase('llmProcessing');
// ...Bot...
adapter.endPhase('llmProcessing');

adapter.startPhase('textToSpeech');
// ...TTS/Playback...
adapter.endPhase('textToSpeech');

await adapter.finalizeRequest();
adapter.finalizeConversation();
```

## Best Practices

- Always use `SessionMetricsAdapter` and `RequestContext` for request/session metrics.
- Use `PerformanceLogger` only for system-level or advanced metrics.
- See [performance-metrics.md](./performance-metrics.md) for full details.

## Testing

- Unit and integration tests are in `__tests__/`.
- All metrics flows are covered, including error and edge cases.

## Integration Points

- Session state machine (`EventDrivenStateManager`)
- Audio, ASR, LLM, and TTS services
- WebSocket and HTTP API endpoints
