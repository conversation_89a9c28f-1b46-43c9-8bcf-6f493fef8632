/**
 * Canonical, async-safe adapter for integrating session/user interaction lifecycles with the metrics subsystem.
 *
 * - Encapsulates all metrics-related logic for session events, bridging the session state machine to PerformanceLogger.
 * - Manages phase status to prevent double phase starts/ends and ensure robust, accurate metrics tracking.
 * - Fully decoupled from state transitions: only observes and records timing/phase data, never drives or triggers state changes.
 * - All session/user interaction metrics should use this adapter, not PerformanceLogger directly.
 * - Recommended integration point for state machines, event-driven flows, and session management.
 */

import { PerformanceLogger, RequestContext } from './performance-logger';
import { EventDrivenStateManager } from '../../session/state-machine/event-driven-state-manager';

export { RequestContext } from './performance-logger';
import { logWarning, logInfo, logMetrics, logError } from '../logging/logger';

export class SessionMetricsAdapter {
  private performanceLogger: PerformanceLogger;
  private currentRequest: RequestContext | null = null;
  private currentUserInput = '';
  private currentAiReply = '';
  private stateManager: EventDrivenStateManager;
  private conversationId?: string;
  private requestsById: Map<string, RequestContext> = new Map();

  // Track phase status for the current request
  private phaseStatus: Record<
    'speechToText' | 'llmProcessing' | 'textToSpeech',
    'not_started' | 'started' | 'ended'
  > = {
    speechToText: 'not_started',
    llmProcessing: 'not_started',
    textToSpeech: 'not_started',
  };

  constructor(stateManager: EventDrivenStateManager) {
    this.performanceLogger = PerformanceLogger.getInstance();
    this.stateManager = stateManager;
  }

  /**
   * Set the conversation ID for metrics tracking
   */
  setConversationId(conversationId: string): void {
    this.conversationId = conversationId;
    this.performanceLogger.setConversationId(conversationId);
  }

  /**
   * Start tracking a new request with the given user input
   */
  async startRequest(userInput: string, isInitialGreeting = false): Promise<RequestContext> {
    // If there is an existing request, finalize it before starting a new one
    if (this.currentRequest) {
      await this.finalizeRequest(this.currentAiReply);
    }

    // Store the user input in the adapter
    this.currentUserInput = userInput;

    // Create a request with the user input and initial greeting flag
    this.currentRequest = this.performanceLogger.createRequest(userInput, isInitialGreeting);
    this.requestsById.set(this.currentRequest.requestId, this.currentRequest);

    // Reset phase status for the new request
    this.phaseStatus = {
      speechToText: 'not_started',
      llmProcessing: 'not_started',
      textToSpeech: 'not_started',
    };

    // No need to log every request start - this happens frequently

    return this.currentRequest;
  }

  /**
   * Get the current request context, or null if none exists
   */
  getCurrentRequest(): RequestContext {
    if (!this.currentRequest) {
      logError('[Metrics]: No request context available');
      throw new Error('No request context available');
    }
    return this.currentRequest;
  }

  /**
   * Start tracking a specific phase of request processing
   * @returns A PhaseContext that will automatically end the phase when end() is called
   */
  async startPhase(phase: 'speechToText' | 'llmProcessing' | 'textToSpeech'): Promise<void> {
    if (!this.currentRequest) {
      // Create a request with the stored user input directly
      this.currentRequest = this.performanceLogger.createRequest(this.currentUserInput);

      // Apply the stored AI reply to the new request context
      // No need to set AI reply on the context; it will be passed to finalize.
    }

    // Guard: prevent double-start
    if (this.phaseStatus[phase] === 'started') {
      logWarning(
        `[Metrics][startPhase] Phase "${phase}" already started for current request, skipping.`
      );
      return;
    }
    if (this.phaseStatus[phase] === 'ended') {
      logWarning(
        `[Metrics][startPhase] Phase "${phase}" already ended for current request, skipping.`
      );
      return;
    }

    this.phaseStatus[phase] = 'started';

    // Debug logging for phase start
    if (this.conversationId && this.currentRequest) {
      logInfo(
        `[Metrics][startPhase] conversationId=${this.conversationId}, requestId=${this.currentRequest.requestId}, phase=${phase}`
      );
    }

    // Metrics code does not drive state transitions. Only observes and records phase data.
    // No context object is returned; phase management is now explicit.
    return;
  }

  /**
   * Get a request context by its requestId, or undefined if not found.
   */
  getRequestById(requestId: string): RequestContext | undefined {
    return this.requestsById.get(requestId);
  }

  /**
   * End tracking a specific phase of request processing
   */
  async endPhase(phase: 'speechToText' | 'llmProcessing' | 'textToSpeech'): Promise<void> {
    if (!this.currentRequest) {
      return;
    }

    // Guard: prevent double-end
    if (this.phaseStatus[phase] === 'ended') {
      logWarning(
        `[Metrics][endPhase] Phase "${phase}" already ended for current request, skipping.`
      );
      return;
    }
    if (this.phaseStatus[phase] === 'not_started') {
      logWarning(
        `[Metrics][endPhase] Phase "${phase}" was never started for current request, skipping.`
      );
      return;
    }

    this.currentRequest.endPhase(phase);

    this.phaseStatus[phase] = 'ended';

    // Debug logging for phase end and duration
    if (this.conversationId && this.currentRequest) {
      // Get the phase duration using the public helper
      const phaseDurations = this.performanceLogger.getPhaseDurations(
        this.conversationId,
        this.currentRequest.requestId
      );
      const duration =
        phaseDurations && phaseDurations[phase] !== null ? phaseDurations[phase] : 'N/A';
      logInfo(
        `[Metrics][endPhase] conversationId=${this.conversationId}, requestId=${this.currentRequest.requestId}, phase=${phase}, duration=${duration}ms`
      );
    }

    // Metrics code does not drive state transitions. Only observes and records phase data.
  }

  /**
   * Set the AI reply for the current request
   */
  setAiReply(reply: string): void {
    this.currentAiReply = reply;
    logInfo(`[Metrics] Setting AI reply for current request: "${reply}"`);
    // No need to set AI reply on the context; it will be passed to finalize.
  }

  /**
   * Get the current AI reply
   */
  getCurrentAiReply(): string {
    return this.currentAiReply;
  }

  /**
   * Finalize the current request and clean up
   */
  async finalizeRequest(aiReply?: string): Promise<void> {
    if (this.currentRequest) {
      // Debug logging for finalized request and all phase durations
      if (this.conversationId) {
        const phaseDurations = this.performanceLogger.getPhaseDurations(
          this.conversationId,
          this.currentRequest.requestId
        );
        if (phaseDurations) {
          logInfo(
            `[Metrics][finalizeRequest] conversationId=${this.conversationId}, requestId=${this.currentRequest.requestId}, phaseDurations={speechToText:${phaseDurations.speechToText}ms, llmProcessing:${phaseDurations.llmProcessing}ms, textToSpeech:${phaseDurations.textToSpeech}ms}`
          );
        } else {
          logInfo(
            `[Metrics][finalizeRequest] conversationId=${this.conversationId}, requestId=${this.currentRequest.requestId}, phaseDurations=N/A`
          );
        }
      }

      // Use the stored AI reply if not provided
      const replyToUse = aiReply !== undefined ? aiReply : this.currentAiReply;

      // Pass the AI reply explicitly
      this.currentRequest.finalize(replyToUse);
      this.currentRequest = null;

      // Reset phase status for the next request
      this.phaseStatus = {
        speechToText: 'not_started',
        llmProcessing: 'not_started',
        textToSpeech: 'not_started',
      };

      // Keep these values for the next request if needed
      // this.currentUserInput = '';
      // this.currentAiReply = '';

      // When a request is finalized, transition back to IDLE if we're in PLAYING state
      // This indicates the completion of the current turn
      // Metrics code no longer drives state transitions.
      // State transitions must be handled by the state machine and its actions.
    }
  }

  /**
   * Finalize the entire conversation
   */
  finalizeConversation(): void {
    // Ensure any pending request is finalized with the current values
    if (this.currentRequest) {
      this.currentRequest.finalize();
      this.currentRequest = null;
    }

    this.performanceLogger.finalizeConversation();

    // Clear stored values after conversation is finalized
    this.currentUserInput = '';
    this.currentAiReply = '';
  }

  /**
   * Log a metrics message
   * This is useful for tracking important events that should appear in metrics logs
   */
  logMetricsMessage(message: string): void {
    logMetrics(`[Session] ${message}`);
  }

  /**
   * (REMOVED) updateStateForPhase: Metrics code no longer drives state transitions.
   * This method has been removed to ensure metrics only observes and records.
   */

  /**
   * (REMOVED) updateStateAfterPhaseCompletion: Metrics code no longer drives state transitions.
   * This method has been removed to ensure metrics only observes and records.
   */
}
