# RequestId Lifecycle & Enforcement in Monitoring

This document describes how the system tracks and enforces the use of a unique `requestId` for every user input event, ensuring robust, end-to-end metrics and traceability.

---

## RequestId Lifecycle

- **Generation:**
  A new `requestId` is generated at the start of every user input event (speech, DTMF, etc.) by the `StartMetricsRequestAction` in the state machine, via `metricsAdapter.startRequest(userInput)`.
- **Propagation:**
  The `requestId` is stored in the session/request context and is required in all downstream event payloads and metrics calls. All actions, events, and metrics for a user input must propagate this `requestId`.
- **Enforcement:**
  The metrics system (`PerformanceLogger.startPhase`) enforces the presence of a valid `requestId` for all metrics phases. If a phase is started without a `requestId`, an error is thrown. If metrics are disabled or the conversation ID is missing, a log message is emitted.

---

## Key Components

- **PerformanceLogger:**
  Central class for managing metrics, request/phase timing, and requestId enforcement.
- **RequestContext:**
  Encapsulates a single user input/request, holding the `requestId` and providing a fluent API for tracking phases and setting user/AI data.
- **PerformanceMetricsStore:**
  Stores all metrics data, keyed by conversation and requestId.

---

## Example: RequestId Flow

```typescript
// At the start of PROCESSING_INPUT state:
await metricsAdapter.startRequest(userInput); // Generates and stores a new requestId

// All subsequent events and metrics for this input:
eventEmitter.emit(SessionEventType.PROCESSING_INPUT_COMPLETED, { ..., requestId });
metricsAdapter.startPhase('speechToText'); // Throws if requestId is missing
```

---

## Runtime Checks

- If `PerformanceLogger.startPhase` is called without a valid `requestId`, an error is thrown.
- If metrics are disabled or the conversation ID is missing, a log message is emitted at INFO or WARN level.

---

## Best Practices

- Always generate the `requestId` at the start of user input processing.
- Propagate the `requestId` in all event payloads and metrics calls.
- Never start a metrics phase without a valid `requestId`.
- Use the provided logging functions (`logInfo`, `logWarning`, etc.) for all monitoring-related logs.

---

## See Also

- [State Machine README](../../session/state-machine/README.md) for details on state transitions and action flow.
- [Root README](../../../README.md) for architectural overview.
