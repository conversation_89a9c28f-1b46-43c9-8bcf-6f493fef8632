import { PerformanceLogger, RequestContext } from '../performance-logger';
import { MetricsPhase } from '../../../types/monitoring';

// Mock the PerformanceMetricsStore
jest.mock('../performance-metrics-store', () => {
  return {
    PerformanceMetricsStore: jest.fn().mockImplementation(() => ({
      initializeConversation: jest.fn(),
      initializeRequest: jest.fn().mockReturnValue('mock-request-id'),
      startPhase: jest.fn(),
      endPhase: jest.fn(),
      finalizeRequest: jest.fn(),
      finalizeConversation: jest.fn(),
      getConversationMetrics: jest.fn().mockReturnValue(null),
      setUserInput: jest.fn(),
      setAiReply: jest.fn(),
    })),
  };
});

describe('Metrics Integration', () => {
  let performanceLogger: PerformanceLogger;

  beforeEach(() => {
    // Reset the singleton instance
    // @ts-ignore - accessing private property for testing
    PerformanceLogger.instance = undefined;

    // Get a fresh instance
    performanceLogger = PerformanceLogger.getInstance();

    // Set up conversation and request
    performanceLogger.setConversationId('test-conv-id');
    performanceLogger.initializeRequest();

    // @ts-ignore - accessing private property for testing
    performanceLogger.currentRequestId = 'test-req-id';

    // Enable metrics for testing
    process.env.ENABLE_PERF_MONITOR = 'true';

    // Spy on methods
    jest.spyOn(performanceLogger, 'startPhase');
    jest.spyOn(performanceLogger, 'endPhase');
    jest.spyOn(performanceLogger, 'setCurrentUserInput');
    jest.spyOn(performanceLogger, 'setCurrentAiReply');
    jest.spyOn(performanceLogger, 'finalizeRequest');
  });

  afterEach(() => {
    // Clean up environment variables
    delete process.env.ENABLE_PERF_MONITOR;

    // Reset all mocks
    jest.clearAllMocks();
  });

  // PhaseContext tests removed: phase management is now explicit and tested via direct calls.

  describe('transitionPhase', () => {
    it('should end one phase and start another', () => {
      // Use transitionPhase to switch from ASR to LLM
      const fromPhase: MetricsPhase = 'speechToText';
      const toPhase: MetricsPhase = 'llmProcessing';

      // Start the initial phase
      performanceLogger.startPhase(fromPhase);

      // Reset the spy counts
      jest.clearAllMocks();

      // Transition to the next phase
      performanceLogger.transitionPhase(fromPhase, toPhase);

      // Verify that endPhase was called for the first phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(fromPhase);

      // Verify that startPhase was called for the second phase
      expect(performanceLogger.startPhase).toHaveBeenCalledWith(toPhase);

      // End the second phase explicitly
      performanceLogger.endPhase(toPhase);

      // Verify that endPhase was called for the second phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith(toPhase);
    });
  });

  describe('RequestContext', () => {
    it('should create a request context with the correct request ID', () => {
      // Mock initializeRequest to return a predictable value
      jest.spyOn(performanceLogger, 'initializeRequest').mockReturnValue('new-req-id');

      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Verify that initializeRequest was called
      expect(performanceLogger.initializeRequest).toHaveBeenCalled();

      // Verify that the request ID is correct
      expect(requestContext.requestId).toBe('new-req-id');
    });

    it('should set user input and AI reply using the explicit API', () => {
      // Create a request context with user input
      const requestContext = performanceLogger.createRequest('test user input');

      // Finalize the request with an AI reply
      requestContext.finalize('test AI reply');

      // Verify that finalizeRequest was called with the correct values
      expect(performanceLogger.finalizeRequest).toHaveBeenCalledWith(
        'test user input',
        'test AI reply'
      );
    });

    it('should track phases', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Track a phase
      const phaseContext = requestContext.trackPhase('speechToText');

      // Verify that startPhase was called
      expect(performanceLogger.startPhase).toHaveBeenCalledWith('speechToText');

      // End the phase
      performanceLogger.endPhase('speechToText');

      // Verify that endPhase was called
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('speechToText');
    });

    it('should transition between phases', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Start the initial phase
      performanceLogger.startPhase('speechToText');

      // Reset the spy counts
      jest.clearAllMocks();

      // Transition to the next phase
      const phaseContext = requestContext.transitionPhase('speechToText', 'llmProcessing');

      // Verify that endPhase was called for the first phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('speechToText');

      // Verify that startPhase was called for the second phase
      expect(performanceLogger.startPhase).toHaveBeenCalledWith('llmProcessing');

      // End the second phase
      performanceLogger.endPhase('llmProcessing');

      // Verify that endPhase was called for the second phase
      expect(performanceLogger.endPhase).toHaveBeenCalledWith('llmProcessing');
    });

    it('should finalize the request', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Finalize the request
      requestContext.finalize();

      // Verify that finalizeRequest was called
      expect(performanceLogger.finalizeRequest).toHaveBeenCalled();
    });

    it('should finalize the request with an AI reply', () => {
      // Create a request context
      const requestContext = performanceLogger.createRequest();

      // Finalize the request with an AI reply
      requestContext.finalize('test AI reply');

      // Verify that setCurrentAiReply and finalizeRequest were called
      expect(performanceLogger.setCurrentAiReply).toHaveBeenCalledWith('test AI reply');
      expect(performanceLogger.finalizeRequest).toHaveBeenCalled();
    });

    it('should support the explicit API for user input and AI reply', () => {
      // Create a request context with user input
      const requestContext = performanceLogger.createRequest('test user input');

      // Finalize the request with an AI reply
      requestContext.finalize('test AI reply');

      // Verify that finalizeRequest was called with the correct values
      expect(performanceLogger.finalizeRequest).toHaveBeenCalledWith(
        'test user input',
        'test AI reply'
      );
    });
  });
});
