import { logMetrics } from '../logging';
import { RequestContext } from './performance-logger';

/**
 * Measures the duration of an async function call and records it in the metrics context.
 * @param metricsContext The metrics context to record timing in.
 * @param label The label for the service timing (e.g., 'LLMServiceCall', 'TTSServiceCall').
 * @param fn The async function to call.
 * @returns The result of the function call.
 */
export async function callWithMeasurement<T>(
  metricsContext: RequestContext,
  label: string,
  fn: () => Promise<T>
): Promise<T> {
  logMetrics(`Trying to measure ${label}`);
  const getNow =
    typeof performance !== 'undefined' && typeof performance.now === 'function'
      ? () => performance.now()
      : () => Date.now();
  const start = getNow();
  try {
    logMetrics(`Returning ${label}`);
    return await fn();
  } finally {
    const end = getNow();
    const duration = end - start;
    metricsContext.addServiceTiming(label, duration);
    logMetrics(`Added ${label}`);
  }
}
