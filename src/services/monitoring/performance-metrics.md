# Monitoring & Performance Metrics

## Data Model
```typescript
export interface PhaseMetrics {
  startTime: Date;
  endTime: Date | null;
  duration: number | null;
}
export interface RequestMetrics {
  requestId: string;
  timestamp: Date;
  userInput: string;
  aiReply: string;
  phases: {
    speechToText: PhaseMetrics;
    llmProcessing: PhaseMetrics;
    textToSpeech: PhaseMetrics;
  };
  totalDuration: number | null;
  /**
   * Service timings for this request.
   * Each entry: { label: string; provider: string; duration: number }
   * Example labels: 'LLMServiceCall', 'TTSServiceCall', 'ASRCallCount', 'ASRCallAvgDuration'
   */
  serviceTimings?: Array<{ label: string; provider: string; duration: number }>;
}
export interface ConversationMetrics {
  conversationId: string;
  ani: string;
  startTime: Date;
  endTime: Date;
  requests: RequestMetrics[];
  phaseMetrics: {
    speechToText: { avgDuration: number; minDuration: number; maxDuration: number; totalDuration: number; count: number; };
    llmProcessing: { avgDuration: number; minDuration: number; maxDuration: number; totalDuration: number; count: number; };
    textToSpeech: { avgDuration: number; minDuration: number; maxDuration: number; totalDuration: number; count: number; };
  };
}
```
---

## Service Call Measurement

All external service calls to LLM, TTS, and ASR providers are measured using the `callWithMeasurement` utility. This ensures that every invocation of a provider's API (e.g., speech-to-text, text-to-speech, LLM completion) is timed and logged as a service timing entry in the metrics context.

- **Where is measurement enforced?**
  - Measurement is enforced at the top-level service entry points:
    - ASR: `EnhancedASRService.processAudio` and `ASRServiceWithPool.processAudio`
    - TTS: All TTS synthesis in the state machine and bot resource actions
    - LLM: All LLM completions in bot resource actions
  - Provider-level methods (e.g., `AzureSpeechService.processAudio`, `GoogleSpeechAdapter.processAudio`) are not measured unless called directly in tests or custom code.

- **How is measurement performed?**
  - The `callWithMeasurement(metricsContext, label, fn)` utility wraps the provider call, records the duration, and logs the timing with a label such as `ASRServiceCall`, `TTSServiceCall`, or `LLMServiceCall`.

- **Best Practice:** Always invoke provider APIs through the top-level service, not directly, to ensure metrics are recorded.

---

## ASR Phase Timing Pitfalls and Debugging Guidance

### Common Problems

Robust tracking of ASR (Automatic Speech Recognition) phase timing is challenging, especially in continuous or streaming recognition modes. The following pitfalls have been observed in production and should be considered when designing or debugging metrics collection:

- **Phase Flooding (Multiple Starts per Utterance):**
  - Multiple `start` events for a single utterance can occur due to overlapping triggers or mismanaged state, leading to inflated or fragmented metrics.
- **Race Conditions (Uncertain Async Boundaries):**
  - Asynchronous event handling (e.g., audio stream events, ASR callbacks) can result in out-of-order or overlapping phase transitions, making it difficult to reliably associate metrics with the correct utterance.
- **Context Mismatches:**
  - Metrics context (e.g., request/utterance ID) may not be reliably associated with the correct utterance, especially if context propagation is not robust across async boundaries.
- **Double-Finalization or Missed Finalization:**
  - Phases may be finalized (ended) more than once, or not at all, due to unclear state transitions or error handling, resulting in inaccurate durations or missing data.
- **Continuous Mode Phase Management:**
  - In continuous ASR mode, managing phase boundaries is especially difficult, as new utterances may begin before the previous one is fully finalized, and silence detection may be unreliable.

### Debugging Tips

- **Log Patterns to Look For:**
  - Multiple `ASR phase start` logs without corresponding `end` for the same utterance/request.
  - `ASR phase end` events with missing or mismatched context (e.g., request ID, utterance ID).
  - Overlapping or out-of-order `start`/`end` events in logs.
  - Unusually short or long phase durations, or durations of zero.
  - Missing metrics for some utterances, or duplicate metrics for the same utterance.

- **Concrete Debugging Steps:**
  - Add unique identifiers (e.g., utterance ID, request ID) to all ASR phase logs.
  - Correlate `start` and `end` events in logs to ensure every start has a matching end.
  - Use assertions or warnings in code to detect double-finalization or missing finalization.
  - Simulate rapid-fire or overlapping utterances in tests to surface race conditions.

### Architectural Guidance

To improve reliability and maintainability of ASR phase metrics:

- **Introduce a Dedicated "LISTENING" State:**
  - After the `IDLE` state, introduce a `LISTENING` state in the session or ASR state machine.
  - This provides a clean asynchronous boundary for starting and ending ASR phases, reducing the risk of race conditions and context mismatches.
  - All phase management logic should be tied to explicit state transitions, not just event callbacks.

Careful state management and explicit boundaries are essential for robust ASR phase timing metrics, especially in complex or high-concurrency environments.

### Service Timings

- `serviceTimings` is an array of objects attached to each request, providing fine-grained timing for external service calls.
- Typical labels:
  - `LLMServiceCall`: Time spent in LLM provider (e.g., OpenAI, litellm)
  - `TTSServiceCall`: Time spent in TTS provider
  - `ASRCallCount`, `ASRCallAvgDuration`: For ASR, number of calls and average duration
- Example:
  ```json
  [
    { "label": "LLMServiceCall", "provider": "litellm", "duration": 1267.0 },
    { "label": "TTSServiceCall", "provider": "elevenlabs", "duration": 6944.0 }
  ]
  ```

### Log Output Format

- **Conversation Summary**:
  `[CONVERSATION SUMMARY] ConvID: <id>, Interactions: <count>`
- **Per-Interaction**:
  `  <n>. User: "<input>", AI: "<reply>", ASR: <ms>, LLM: <ms>, TTS: <ms>, Total: <ms>`
- **Service Calls**:
  `     Service Calls: [LLMServiceCall: <provider> <ms>, ...]`
- **Raw Service Timings** (debug):
  `[DEBUG] Raw serviceTimings for request <reqId>: [...]`
- These logs are output via the logger and are useful for tracing and debugging performance.

### Duplicate Request Handling & AI Reply Association

- Duplicate requests (same user input) within a conversation are detected and skipped to avoid redundant metrics.
- When associating an AI reply, the system first tries to match by request ID, then by user input, then by most recent request with an empty reply.


## Phases
- `speechToText`: ASR
- `llmProcessing`: Bot/LLM
- `textToSpeech`: TTS/playback
- Phases must be started/ended explicitly; no overlaps.

## Key Components
- **SessionMetricsAdapter**: Use for all session/user metrics. Handles async safety, phase deduplication, and aggregation.
- **PerformanceLogger**: For non-session/general metrics only.
- **PerformanceMetricsStore**: Internal backend, not used directly.
- **Logging utilities**: For output only, not metrics state.

## Usage Example
```typescript
const adapter = new SessionMetricsAdapter(stateManager);
adapter.setConversationId(conversationId);
const request = await adapter.startRequest(userInput);
adapter.startPhase('speechToText');
// ...ASR...
adapter.endPhase('speechToText');
adapter.startPhase('llmProcessing');
// ...Bot...
adapter.endPhase('llmProcessing');
adapter.startPhase('textToSpeech');
// ...TTS/Playback...
adapter.endPhase('textToSpeech');
await adapter.finalizeRequest();
adapter.finalizeConversation();
```

## Best Practices
- Always use `SessionMetricsAdapter` and `RequestContext` for session/user/request-scoped metrics.
- Use `PerformanceLogger` only for system-level or advanced metrics needs; treat it as an internal API.
- Never use logging utilities for metrics storage.
- All phase status/deduplication logic is in the adapter.

---

## Important Implementation Notes (2025-06)

### AI Reply Association and Finalization

- The AI reply (`aiReply`) for each turn is set in the session by the state machine, specifically in the `handle-bot-response-action` immediately after the LLM/bot response is received and before transitioning to the RESPONDING state.
- The AI reply is stored in the session (`session.setLatestBotResponse(botResponse)`) and is used for metrics finalization.
- **Finalization of metrics for the last turn** occurs during session cleanup (in the `Session.close()` method), which calls `metricsAdapter.finalizeConversation()` and then, if there is a pending request, `finalizeMetricsRequest()`. The AI reply used is whatever is in `session.latestBotResponse?.text` at that moment.
- If the session is closed (e.g., user hangup, disconnect, or system shutdown) before the last bot response is set, the AI reply for the last turn may be missing in the metrics. This is a race condition between bot response processing and session cleanup.
- To ensure the last AI reply is always present in the metrics, the system must guarantee that the latest bot response is set before session cleanup and metrics finalization are triggered.

### State Machine and Metrics Flow

- The state machine transitions through phases: LISTENING → PROCESSING_INPUT → PROCESSING_BOT → RESPONDING → PLAYING → IDLE.
- The AI reply is set on the transition from PROCESSING_BOT to RESPONDING, after the LLM phase ends and before TTS/playback.
- Metrics phases (`speechToText`, `llmProcessing`, `textToSpeech`) are started and ended explicitly at state boundaries.
- The metrics adapter (`SessionMetricsAdapter`) uses the latest bot response from the session at the time of finalization to record the AI reply for the turn.

### Debugging and Race Conditions

- If the last AI reply is missing in the metrics, check for race conditions between bot response processing and session cleanup.
- Ensure that `session.setLatestBotResponse` is called before any metrics finalization logic runs during session close.
- Review logs for the order of `[HandleBotResponseAction] Set latest bot response on session` and `[Session] Finalizing metrics` to diagnose timing issues.

---
---
