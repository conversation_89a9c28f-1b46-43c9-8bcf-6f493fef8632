/**
 * BargeInManager
 * ---------------
 * Encapsulates detection logic for both audio and DTMF barge-in events.
 * Provides both a callback-based interface and event emission for consumers.
 *
 * This class is focused solely on barge-in detection and notification, not state management.
 * Playback state is now managed by the session state machine.
 *
 * Usage (callback-based):
 *   const manager = new BargeInManager();
 *   manager.onBargeIn((event) => {
 *     // Handle barge-in event (audio or DTMF)
 *   });
 *
 * Usage (event-based):
 *   const eventEmitter = new EventEmitter();
 *   const manager = new BargeInManager({ eventEmitter });
 *   // Events will be emitted to the eventEmitter
 */

import { EventEmitter, EventPriority } from '../../services/events/event-emitter';
import {
  SessionEventType,
  BargeInDetectedEventData,
  PlaybackStartedEventData,
  PlaybackCompletedEventData,
} from '../../services/events/session-events';
import { logError } from '../logging/logger';

export type BargeInType = 'audio' | 'dtmf';

export interface BargeInEvent {
  type: BargeInType;
  timestamp: number;
  text?: string; // Text content for audio barge-in
  // Optionally, add more metadata as needed (e.g., DTMF digit, audio level)
}

type BargeInCallback = (event: BargeInEvent) => void;

// Keep this type for backward compatibility
export type PlaybackState = 'playing' | 'paused' | 'stopped';

/**
 * Options for BargeInManager
 */
export interface BargeInManagerOptions {
  /**
   * Optional event emitter for event-based communication
   */
  eventEmitter?: EventEmitter;
}

export class BargeInManager {
  private callbacks: Set<BargeInCallback>;
  private bargeInEnabled = true;
  private eventEmitter?: EventEmitter;
  private currentPlaybackState: PlaybackState = 'stopped';

  constructor(options?: BargeInManagerOptions) {
    this.callbacks = new Set();
    this.eventEmitter = options?.eventEmitter;
  }

  /**
   * Set the event emitter after construction
   * This allows adding event emission to an existing BargeInManager
   */
  public setEventEmitter(eventEmitter: EventEmitter): void {
    this.eventEmitter = eventEmitter;
    // Import logging dynamically to avoid circular dependencies
    import('../logging/logger').then(({ logInfo }) => {
      logInfo('[BargeInManager] Event emitter set, event emission enabled');
    });
  }

  /**
   * Register a callback to be invoked when a barge-in event is detected.
   * @param callback Function to call with the BargeInEvent.
   * @returns Unsubscribe function to remove the callback.
   */
  public onBargeIn(callback: BargeInCallback): () => void {
    this.callbacks.add(callback);
    return () => {
      this.callbacks.delete(callback);
    };
  }

  /**
   * Internal method to signal a barge-in event to all registered callbacks and emit event.
   * @param event The barge-in event to dispatch.
   */
  protected emitBargeIn(event: BargeInEvent): void {
    // Call registered callbacks (backward compatibility)
    for (const cb of this.callbacks) {
      try {
        cb(event);
      } catch (err) {
        // Import logging dynamically to avoid circular dependencies
        import('../logging/logger').then(({ logError }) => {
          logError(
            `[BargeInManager] Error in barge-in callback: ${
              err instanceof Error ? err.message : String(err)
            }`
          );
        });
      }
    }

    // Emit event if event emitter is available
    if (this.eventEmitter) {
      // Map BargeInType to source for SessionEvents
      const source = event.type === 'audio' ? 'speech' : 'dtmf';

      // Emit barge-in detected event with high priority
      this.eventEmitter.emit(
        SessionEventType.BARGE_IN_DETECTED,
        {
          source,
          text: event.text,
          timestamp: event.timestamp,
        } as BargeInDetectedEventData,
        EventPriority.HIGH
      );

      // Import logging dynamically to avoid circular dependencies
      import('../logging/logger').then(({ logDebug }) => {
        logDebug(`[BargeInManager] Emitted BARGE_IN_DETECTED event (${source})`);
      });
    } else {
      logError(
        `[BargeInManager] Cannot emit BARGE_IN_DECTED event, emitter is ${this.eventEmitter}`
      );
    }
  }

  /**
   * Enable barge-in detection
   */
  public enableBargeIn(): void {
    this.bargeInEnabled = true;
    // Import logging dynamically to avoid circular dependencies
    import('../logging/logger').then(({ logInfo }) => {
      logInfo('[BargeInManager] Barge-in detection enabled');
    });
  }

  /**
   * Disable barge-in detection
   */
  public disableBargeIn(): void {
    this.bargeInEnabled = false;
    // Import logging dynamically to avoid circular dependencies
    import('../logging/logger').then(({ logInfo }) => {
      logInfo('[BargeInManager] Barge-in detection disabled');
    });
  }

  /**
   * Check if barge-in detection is enabled
   */
  public isBargeInEnabled(): boolean {
    return this.bargeInEnabled;
  }

  /**
   * Call this when audio barge-in is detected.
   * @param text The text content that triggered the barge-in
   */
  public detectAudioBargeIn(text?: string): void {
    if (!this.bargeInEnabled) {
      // Import logging dynamically to avoid circular dependencies
      import('../logging/logger').then(({ logDebug }) => {
        logDebug('[BargeInManager] Audio barge-in ignored (detection disabled)');
      });
      return;
    }

    this.emitBargeIn({
      type: 'audio',
      timestamp: Date.now(),
      text: text,
    });
  }

  /**
   * Call this when DTMF barge-in is detected.
   */
  public detectDtmfBargeIn(): void {
    if (!this.bargeInEnabled) {
      // Import logging dynamically to avoid circular dependencies
      import('../logging/logger').then(({ logDebug }) => {
        logDebug('[BargeInManager] DTMF barge-in ignored (detection disabled)');
      });
      return;
    }

    this.emitBargeIn({
      type: 'dtmf',
      timestamp: Date.now(),
    });
  }

  /**
   * Set the current playback state and emit corresponding events.
   * This method is called by the session to update the playback state.
   *
   * @param state The new playback state
   */
  public setPlaybackState(state: PlaybackState): void {
    // Skip if state hasn't changed
    if (this.currentPlaybackState === state) {
      return;
    }

    const oldState = this.currentPlaybackState;
    this.currentPlaybackState = state;

    // Import logging dynamically to avoid circular dependencies
    import('../logging/logger').then(({ logDebug }) => {
      logDebug(`[BargeInManager] Playback state changed: ${oldState} → ${state}`);
    });

    // Emit events if event emitter is available
    if (this.eventEmitter) {
      if (state === 'playing') {
        // Emit playback started event
        this.eventEmitter.emit(
          SessionEventType.PLAYBACK_STARTED,
          {
            timestamp: Date.now(),
          } as PlaybackStartedEventData,
          EventPriority.MEDIUM
        );
      } else if (state === 'stopped' && oldState === 'playing') {
        // Emit playback completed event
        this.eventEmitter.emit(
          SessionEventType.PLAYBACK_COMPLETED,
          {
            timestamp: Date.now(),
          } as PlaybackCompletedEventData,
          EventPriority.MEDIUM
        );
      }
    }
  }

  /**
   * Get the current playback state
   */
  public getPlaybackState(): PlaybackState {
    return this.currentPlaybackState;
  }
}
