# Barge-In Implementation: Technical Details

## Overview

This document provides a detailed explanation of the barge-in implementation in the application, focusing on the state transitions, component interactions, and flow of events when a barge-in occurs.

## Barge-In Types

The system supports two types of barge-in:

1. **Audio Barge-In**: Detected when speech is recognized during audio playback
2. **DTMF Barge-In**: Detected when DTMF digits are pressed during audio playback

## Component Architecture

The barge-in functionality is implemented through several cooperating components:

### 1. BargeInManager (`src/services/barge-in/barge-in-manager.ts`)

- Central manager for barge-in detection and playback state
- Provides methods to detect audio and DTMF barge-in
- Manages playback state (`playing`, `paused`, `stopped`)
- Emits barge-in events to registered callbacks
- Provides a clean, callback-based API

### 2. Session (`src/session/session.ts`)

- Registers callbacks with BargeInManager
- Handles barge-in events by transitioning to appropriate states
- Coordinates state transitions with the state machine

### 3. HandleBargeInAction (`src/session/state-machine/actions/audio/handle-barge-in-action.ts`)

- State machine action that handles barge-in events
- Executes when exiting RESPONDING or PLAYING states
- Ensures consistent handling of barge-in events

### 4. AudioManager and DTMFManager

- Detect speech and DTMF input during playback
- Notify BargeInManager when barge-in is detected

## Barge-In Flow

```mermaid
sequenceDiagram
    participant User
    participant AudioManager
    participant BargeInManager
    participant Session
    participant StateManager
    participant HandleBargeInAction

    Note over Session: In PLAYING or RESPONDING state
    User->>AudioManager: Speaks during playback
    AudioManager->>BargeInManager: detectAudioBargeIn(text)
    BargeInManager->>Session: onBargeIn callback(event)
    Session->>StateManager: setState(IDLE or PROCESSING_INPUT)
    StateManager->>HandleBargeInAction: execute(context)
    HandleBargeInAction->>Session: cancelCurrentTurn()
    HandleBargeInAction->>StateManager: setState(next state)
```

## State Transitions for Barge-In

The state transitions for barge-in events depend on the current state:

1. **During PLAYING State**:

   - Barge-in detected → Transition to IDLE state
   - HandleBargeInAction executes as an exit action
   - Playback is stopped
   - System is ready for new user input

2. **During RESPONDING State**:
   - Barge-in detected → Transition to PROCESSING_INPUT state
   - HandleBargeInAction executes as an exit action
   - Current bot turn is canceled
   - User input is processed immediately

```mermaid
flowchart TD
    PLAY[PLAYING]
    RESP[RESPONDING]
    IDLE[IDLE]
    PROC_IN[PROCESSING_INPUT]

    PLAY -->|"Audio/DTMF barge-in"| IDLE
    RESP -->|"Audio/DTMF barge-in"| PROC_IN

    IDLE -->|"User input"| PROC_IN
```

## Implementation Details

### 1. Registering Callbacks

The Session registers callbacks with the BargeInManager during initialization and properly handles async state transitions:

```typescript
// In Session constructor
this.bargeInManager = new BargeInManager();

// Register barge-in callback for audio and DTMF events
this.bargeInManager.onBargeIn((event: BargeInEvent) => {
  // Use void to handle the Promise returned by handleBargeIn
  // This ensures we don't block the callback but still execute the async function
  if (event.type === 'audio') {
    void this.handleBargeIn('speech', event.text);
  } else if (event.type === 'dtmf') {
    void this.handleBargeIn('dtmf');
  }
});

// Register playback state change listener
this.bargeInManager.onPlaybackStateChange(state => {
  // Update session state based on playback state
  // Use void to handle the Promise returned by setState
  if (state === 'playing' && !this.stateManager.isPlaying()) {
    void this.stateManager.setState(SessionState.PLAYING, {
      reason: 'Audio playback started',
    });
  } else if (state === 'stopped' && this.stateManager.isPlaying()) {
    void this.stateManager.setState(SessionState.IDLE, {
      reason: 'Audio playback stopped',
    });
  }
});
```

### 2. Handling Barge-In Events

The Session's handleBargeIn method determines the appropriate state transition and properly awaits the state changes:

```typescript
private async handleBargeIn(source: 'speech' | 'dtmf', text?: string): Promise<void> {
  if (this.stateManager.isDisconnectingOrClosed()) {
    return;
  }

  // Cancel all in-progress actions for the current turn (ASR/bot/tts/playback)
  this.cancelCurrentTurn();

  const currentState = this.stateManager.getState();
  logInfo(`[Session] Handling barge-in from ${source} in state ${currentState}`);

  // The state transition will trigger the HandleBargeInAction
  // which will handle the barge-in based on the current state
  if (currentState === SessionState.PLAYING) {
    // Use await to ensure exit actions are executed
    await this.stateManager.setState(SessionState.IDLE, {
      reason: `Barge-in from ${source}`,
      bargeInSource: source,
      bargeInText: text,
    });
  } else if (currentState === SessionState.RESPONDING) {
    // Use await to ensure exit actions are executed
    await this.stateManager.setState(SessionState.PROCESSING_INPUT, {
      reason: `Barge-in from ${source} during response preparation`,
      bargeInSource: source,
      bargeInText: text,
      isBargeIn: true,
    });
  } else {
    logWarning(`[Session] Barge-in received in unexpected state: ${currentState}`);
  }

  logInfo(`[Session] Finished handling barge-in from ${source}`);
}
```

### 3. State Machine Actions

The HandleBargeInAction is registered as an exit action for both RESPONDING and PLAYING states:

```typescript
// In register-actions.ts
stateManager.registerStateActions(SessionState.RESPONDING, {
  onEnter: [new LogStateTransitionAction(), new StartPlaybackAction()],
  onExit: [new HandleBargeInAction()],
});

stateManager.registerStateActions(SessionState.PLAYING, {
  onEnter: [new LogStateTransitionAction(), new SetPlaybackStateAction('playing')],
  onExit: [new HandleBargeInAction(), new TransitionToIdleOnPlaybackStoppedAction()],
});
```

## Error Handling and Validation

The BargeInManager includes robust error handling and validation:

1. **Callback Error Handling**: Errors in callbacks are caught and logged with detailed error messages

   ```typescript
   protected emitBargeIn(event: BargeInEvent): void {
     for (const cb of this.callbacks) {
       try {
         cb(event);
       } catch (err) {
         // Import logging dynamically to avoid circular dependencies
         import('../monitoring/logging').then(({ logError }) => {
           logError(`[BargeInManager] Error in barge-in callback: ${err instanceof Error ? err.message : String(err)}`);
         });
       }
     }
   }
   ```

2. **Playback State Validation**: Only valid playback states are accepted

   ```typescript
   public setPlaybackState(state: PlaybackState): void {
     // Validate the state
     const validStates: PlaybackState[] = ['playing', 'paused', 'stopped'];
     if (!validStates.includes(state)) {
       // Import logging dynamically to avoid circular dependencies
       import('../monitoring/logging').then(({ logError }) => {
         logError(`[BargeInManager] Invalid playback state: ${state}`);
       });
       return;
     }

     // Rest of implementation...
   }
   ```

3. **State Transition Validation**: The state machine validates all transitions and ensures proper execution of actions

## Configuration

Barge-in functionality can be configured through environment variables:

- `ENABLE_BARGE_IN`: Enable/disable barge-in functionality (true/false)
- `BARGE_IN_CONFIDENCE_THRESHOLD`: Confidence threshold for speech barge-in
- `BARGE_IN_DEBUG`: Enable additional debug logging for barge-in events

## Async State Transitions

The barge-in implementation relies on proper handling of asynchronous state transitions to ensure that all actions are executed correctly. This is especially important for barge-in events, which can occur at any time and need to be handled immediately.

### Importance of Awaiting State Transitions

When handling barge-in events, it's crucial to await the state transitions to ensure that all exit actions are executed:

```typescript
// In handleBargeIn method
if (currentState === SessionState.PLAYING) {
  // Use await to ensure exit actions are executed
  await this.stateManager.setState(SessionState.IDLE, {
    reason: `Barge-in from ${source}`,
    bargeInSource: source,
    bargeInText: text,
  });
}
```

Failing to await state transitions can lead to:

1. The HandleBargeInAction not being fully executed
2. The bot turn not being properly canceled
3. Race conditions when multiple barge-in events occur in quick succession
4. Unpredictable behavior when actions depend on the completion of previous actions

### Handling Async Callbacks

For callbacks that need to trigger state transitions, use the `void` operator to handle the Promise without blocking the callback:

```typescript
// Register barge-in callback
this.bargeInManager.onBargeIn((event: BargeInEvent) => {
  // Use void to handle the Promise without blocking
  void this.handleBargeIn('speech', event.text);
});
```

This ensures that the callback returns immediately while still executing the async function.

## Best Practices

1. **Use the BargeInManager**: Always use the BargeInManager for barge-in detection and playback state management
2. **Respect the State Machine**: Let the state machine handle state transitions
3. **Use State Actions**: Encapsulate barge-in handling logic in state actions
4. **Consistent Error Handling**: Always catch and log errors in callbacks
5. **Validate States**: Validate playback states and state transitions
6. **Await State Transitions**: Always await state transitions to ensure proper execution of actions
7. **Use void for Callbacks**: Use the void operator for async callbacks to handle Promises without blocking
