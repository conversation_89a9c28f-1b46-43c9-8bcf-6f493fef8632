/**
 * Tests for the BargeInManager with event emission
 */

import { BargeInManager } from './barge-in-manager';
import { EventEmitter } from '../events/event-emitter';
import { SessionEventType } from '../events/session-events';

describe('BargeInManager with event emission', () => {
  let eventEmitter: EventEmitter;
  let bargeInManager: BargeInManager;

  beforeEach(() => {
    // Create a new event emitter for each test
    eventEmitter = new EventEmitter();

    // Create a new barge-in manager with the event emitter
    bargeInManager = new BargeInManager({ eventEmitter });
  });

  test('should emit BARGE_IN_DETECTED event when audio barge-in is detected', done => {
    // Subscribe to the BARGE_IN_DETECTED event
    eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, data => {
      // Verify the event data
      expect(data.source).toBe('speech');
      expect(data.text).toBe('Hello');
      expect(data.timestamp).toBeDefined();
      done();
    });

    // Trigger audio barge-in
    bargeInManager.detectAudioBargeIn('Hello');
  });

  test('should emit BARGE_IN_DETECTED event when DTMF barge-in is detected', done => {
    // Subscribe to the BARGE_IN_DETECTED event
    eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, data => {
      // Verify the event data
      expect(data.source).toBe('dtmf');
      expect(data.timestamp).toBeDefined();
      done();
    });

    // Trigger DTMF barge-in
    bargeInManager.detectDtmfBargeIn();
  });

  test('should emit PLAYBACK_STARTED event when playback state changes to playing', done => {
    // Subscribe to the PLAYBACK_STARTED event
    eventEmitter.on(SessionEventType.PLAYBACK_STARTED, data => {
      // Verify the event data
      expect(data.timestamp).toBeDefined();
      done();
    });

    // Set playback state to playing
    bargeInManager.setPlaybackState('playing');
  });

  test('should emit PLAYBACK_COMPLETED event when playback state changes from playing to stopped', done => {
    // Set initial state to playing
    bargeInManager.setPlaybackState('playing');

    // Subscribe to the PLAYBACK_COMPLETED event
    eventEmitter.on(SessionEventType.PLAYBACK_COMPLETED, data => {
      // Verify the event data
      expect(data.timestamp).toBeDefined();
      done();
    });

    // Set playback state to stopped
    bargeInManager.setPlaybackState('stopped');
  });

  test('should not emit events when barge-in is disabled', () => {
    // Create a mock event handler
    const mockEventHandler = jest.fn();

    // Subscribe to the BARGE_IN_DETECTED event
    eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, mockEventHandler);

    // Disable barge-in
    bargeInManager.disableBargeIn();

    // Trigger audio barge-in
    bargeInManager.detectAudioBargeIn('Hello');

    // Verify that the event handler was not called
    expect(mockEventHandler).not.toHaveBeenCalled();
  });

  test('should support adding event emitter after construction', done => {
    // Create a barge-in manager without event emitter
    const manager = new BargeInManager();

    // Create a new event emitter
    const emitter = new EventEmitter();

    // Subscribe to the BARGE_IN_DETECTED event
    emitter.on(SessionEventType.BARGE_IN_DETECTED, data => {
      // Verify the event data
      expect(data.source).toBe('speech');
      expect(data.text).toBe('Hello');
      expect(data.timestamp).toBeDefined();
      done();
    });

    // Set the event emitter
    manager.setEventEmitter(emitter);

    // Trigger audio barge-in
    manager.detectAudioBargeIn('Hello');
  });

  test('should support both callback and event-based approaches', () => {
    // Create a mock callback
    const mockCallback = jest.fn();

    // Create a mock event handler
    const mockEventHandler = jest.fn();

    // Register the callback
    bargeInManager.onBargeIn(mockCallback);

    // Subscribe to the BARGE_IN_DETECTED event
    eventEmitter.on(SessionEventType.BARGE_IN_DETECTED, mockEventHandler);

    // Trigger audio barge-in
    bargeInManager.detectAudioBargeIn('Hello');

    // Verify that both the callback and event handler were called
    expect(mockCallback).toHaveBeenCalled();
    expect(mockEventHandler).toHaveBeenCalled();
  });
});
