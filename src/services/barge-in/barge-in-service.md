# Barge-In Service: Technical Overview

## Purpose

The Barge-In Service provides centralized management of barge-in detection and playback state for the application. It detects when users interrupt (barge-in) during audio playback, either through speech or DTMF input, and provides a clean callback-based API for consumers to react to these events.

---

## Key Responsibilities

- Detect barge-in events from both speech and DTMF input
- Manage playback state (playing, paused, stopped)
- Notify listeners of barge-in events and playback state changes
- Provide a clean, callback-based API for consumers
- Centralize barge-in logic to avoid duplication

---

## Core Components and Their Roles

### BargeInManager (`src/services/barge-in/barge-in-manager.ts`)

- Central manager for barge-in detection and playback state
- Provides methods to detect audio and DTMF barge-in
- Manages playback state and notifies listeners of changes
- Emits barge-in events to registered callbacks

---

## Barge-In Types

The service supports two types of barge-in:

1. **Audio Barge-In**: Detected when speech is recognized during audio playback

   - Triggered by the AudioManager when speech is detected during playback
   - Includes the transcript text that triggered the barge-in

2. **DTMF Barge-In**: Detected when DTMF digits are pressed during audio playback
   - Triggered by the DTMFManager when digits are received during playback
   - Includes the DTMF digit that triggered the barge-in

---

## Playback States

The BargeInManager tracks three playback states:

1. **Playing**: Audio is currently being played to the user
2. **Paused**: Audio playback is temporarily paused
3. **Stopped**: No audio is being played

These states are used to determine when barge-in detection should be active and to coordinate playback across the application.

---

## Component Interactions

1. **Initialization**: BargeInManager is created during session initialization
2. **Callback Registration**: Consumers register callbacks for barge-in events and playback state changes
3. **Playback State Management**: Session updates playback state through the manager
4. **Barge-In Detection**: AudioManager and DTMFManager detect barge-in and notify the manager
5. **Event Emission**: Manager emits barge-in events to registered callbacks
6. **Playback Control**: Session stops playback in response to barge-in events

---

## Configuration Options

The Barge-In Service can be configured through environment variables:

- `ENABLE_BARGE_IN`: Enable barge-in functionality (true/false)
- `BARGE_IN_CONFIDENCE_THRESHOLD`: Confidence threshold for speech barge-in
- `BARGE_IN_DEBUG`: Enable additional debug logging for barge-in events

---

## Design Principles

- **Centralized Logic**: Single source of truth for barge-in and playback state
- **Callback-Based API**: Clean, event-driven interface for consumers
- **Separation of Concerns**: Clear separation between detection and handling
- **Self-Contained**: Minimal dependencies on other modules
- **Extensible**: Easy to add new barge-in types or detection methods

---

## File Structure

- `src/services/barge-in/`
  - `barge-in-manager.ts` – Core implementation of the BargeInManager
  - `index.ts` – Exports the BargeInManager and related types

---

## Usage Example

```typescript
// Create BargeInManager
const bargeInManager = new BargeInManager();

// Register callback for barge-in events
bargeInManager.onBargeIn(event => {
  console.log(`Barge-in detected from ${event.type} at ${event.timestamp}`);
  if (event.type === 'audio' && event.text) {
    console.log(`Barge-in text: ${event.text}`);
  }

  // Stop playback in response to barge-in
  stopPlayback();
});

// Register callback for playback state changes
bargeInManager.onPlaybackStateChange(state => {
  console.log(`Playback state changed to: ${state}`);
});

// Update playback state
bargeInManager.setPlaybackState('playing');

// Detect audio barge-in
bargeInManager.detectAudioBargeIn('Hello there');

// Detect DTMF barge-in
bargeInManager.detectDtmfBargeIn();
```

---

## Integration with Other Services

The Barge-In Service integrates with several other services:

- **AudioManager**: Detects speech during playback and triggers audio barge-in
- **DTMFManager**: Detects DTMF input during playback and triggers DTMF barge-in
- **Session**: Registers callbacks and responds to barge-in events
- **AudioServiceAdapter**: Updates playback state during audio operations
- **State Machine**: Handles state transitions through dedicated actions

---

## State Machine Integration

The Barge-In Service is now tightly integrated with the state machine through the `HandleBargeInAction`:

1. **State Actions**: The `HandleBargeInAction` is registered as an exit action for both RESPONDING and PLAYING states
2. **Consistent Handling**: Ensures consistent handling of barge-in events regardless of source
3. **State-Specific Transitions**:
   - During PLAYING state: Transitions to IDLE state
   - During RESPONDING state: Transitions to PROCESSING_INPUT state

### State Transition Diagram

```mermaid
flowchart TD
    PLAY[PLAYING]
    RESP[RESPONDING]
    IDLE[IDLE]
    PROC_IN[PROCESSING_INPUT]

    PLAY -->|"Audio/DTMF barge-in"| IDLE
    RESP -->|"Audio/DTMF barge-in"| PROC_IN

    IDLE -->|"User input"| PROC_IN
```

### Error Handling

The BargeInManager now includes improved error handling:

1. **Callback Error Logging**: Errors in callbacks are caught and logged
2. **Playback State Validation**: Only valid playback states are accepted
3. **State Transition Validation**: The state machine validates all transitions

For more detailed information about the barge-in implementation, see [barge-in-implementation.md](./barge-in-implementation.md).
