/**
 * Session Events: Definitions for all events used in the session lifecycle.
 *
 * This file defines the event types and event data interfaces for the event-driven architecture.
 *
 * @module services/events/session-events
 */

import { SessionState } from '../../session/session-state-manager';
import { BotResponse } from '../../services/bot-service/bot-response';
import { Transcript } from '../../services/speech/base/types';

/**
 * Session event types
 */
export enum SessionEventType {
  // Session lifecycle events
  SESSION_INITIALIZING = 'session:initializing',
  SESSION_INITIALIZED = 'session:initialized',
  SESSION_CLOSE_REQUESTED = 'session:close_requested',
  SESSION_CLOSED = 'session:closed',

  // State machine events
  STATE_CHANGED = 'state:changed',

  // User input events
  USER_INPUT_RECEIVED = 'user:input_received',
  USER_INPUT_PROCESSED = 'user:input_processed',
  PROCESSING_INPUT_COMPLETED = 'processing:input_completed',

  // Bot events
  INITIAL_GREETING_REQUESTED = 'bot:initial_greeting_requested',
  BOT_RESPONSE_RECEIVED = 'bot:response_received',
  BOT_PROCESSING_COMPLETED = 'bot:processing_completed',
  BOT_PROCESSING_REQUESTED = 'bot:processing_requested',

  // Audio events
  AUDIO_DATA = 'audio:data',
  PLAYBACK_STARTED = 'audio:playback_started',
  PLAYBACK_COMPLETED = 'audio:playback_completed',

  // ASR events
  TRANSCRIPT_RECEIVED = 'asr:transcript_received',
  PAUSE_DETECTED = 'asr:pause_detected',

  // DTMF events
  DTMF_INPUT = 'dtmf:input',
  DTMF_SEQUENCE_COMPLETED = 'dtmf:sequence_completed',

  // Barge-in events
  BARGE_IN_DETECTED = 'barge_in:detected',

  // Interruption events
  INTERRUPTION_REQUESTED = 'interruption:requested',

  // Error events
  ERROR = 'error',
}

/**
 * Base interface for all event data
 */
export interface EventData {
  timestamp?: number;
}

/**
 * Session initialization event data
 */
export interface SessionInitializingEventData extends EventData {
  sessionId: string;
  clientId?: string;
}

/**
 * Session initialized event data
 */
export interface SessionInitializedEventData extends EventData {
  sessionId: string;
  clientId?: string;
}

/**
 * Session close requested event data
 */
export interface SessionCloseRequestedEventData extends EventData {
  reason: string;
  code?: number;
}

/**
 * Session closed event data
 */
export interface SessionClosedEventData extends EventData {
  sessionId: string;
  reason: string;
  code?: number;
}

/**
 * State changed event data
 */
export interface StateChangedEventData extends EventData {
  oldState: SessionState;
  newState: SessionState;
  reason?: string;
  metadata?: Record<string, any>;
}

/**
 * User input received event data
 */
export interface UserInputReceivedEventData extends EventData {
  input: string | Transcript;
  inputType: 'speech' | 'dtmf';
  isFinal?: boolean;
}

/**
 * User input processed event data
 */
export interface UserInputProcessedEventData extends EventData {
  input: string | Transcript;
  inputType: 'speech' | 'dtmf';
  requestId: string;
}

/**
 * Processing input completed event data
 * This event is emitted when input processing is complete and ready for bot processing.
 */
export interface ProcessingInputCompletedEventData extends EventData {
  /**
   * The input data (transcript for speech, sequence for DTMF)
   */
  input: string | Transcript;

  /**
   * The type of input that was processed
   */
  inputType: 'speech' | 'dtmf';

  /**
   * Optional request ID for tracking
   */
  requestId: string;
}

/**
 * Initial greeting requested event data
 */
export interface InitialGreetingRequestedEventData extends EventData {
  requestId: string;
}

/**
 * Bot response received event data
 */
export interface BotResponseReceivedEventData extends EventData {
  response: BotResponse;
  requestId: string;
  /**
   * Flag indicating if this response came from asynchronous processing
   */
  fromAsyncProcessing?: boolean;
}

/**
 * Bot processing requested event data
 */
export interface BotProcessingRequestedEventData extends EventData {
  input?: string | Transcript;
  inputType?: 'speech' | 'dtmf';
  isInitialGreeting?: boolean;
  requestId: string;
}

/**
 * Bot processing completed event data
 */
export interface BotProcessingCompletedEventData extends EventData {
  response: BotResponse;
  requestId: string;
}

/**
 * Audio data event data
 */
export interface AudioDataEventData extends EventData {
  audioData: Uint8Array;
  sampleRate?: number;
  channels?: number;
}

/**
 * Playback started event data
 */
export interface PlaybackStartedEventData extends EventData {
  audioBytes?: Uint8Array;
  text?: string;
}

/**
 * Playback completed event data
 */
export interface PlaybackCompletedEventData extends EventData {
  duration?: number;
}

/**
 * Transcript received event data
 */
export interface TranscriptReceivedEventData extends EventData {
  transcript: Transcript;
  isFinal: boolean;
}

/**
 * Pause detected event data
 */
export interface PauseDetectedEventData extends EventData {
  transcript: Transcript;
  pauseDuration: number;
  requestId: string;
}

/**
 * DTMF input event data
 */
export interface DtmfInputEventData extends EventData {
  digit: string;
  duration?: number;
}

/**
 * DTMF sequence completed event data
 */
export interface DtmfSequenceCompletedEventData extends EventData {
  sequence: string;
  requestId: string;
}

/**
 * Barge-in detected event data
 */
export interface BargeInDetectedEventData extends EventData {
  source: 'speech' | 'dtmf';
  text?: string;
}

/**
 * Error event data
 */
export interface ErrorEventData extends EventData {
  error: Error | string;
  source?: string;
  context?: Record<string, any>;
}

/**
 * Interruption requested event data
 */
export interface InterruptionRequestedEventData extends EventData {
  metadata: Record<string, unknown>;
}
