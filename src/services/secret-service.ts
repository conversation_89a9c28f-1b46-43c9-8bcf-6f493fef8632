/*
 * This class provides the authentication process the secret for a given key.
 */
export class SecretService {
  /*
   * For this implementation, we are just using a static map that holds the key/values.
   * In reality, you will want to store these somewhere else, like S3, or some other
   * secrets manager.
   */
  static secrets = new Map();

  static {
    //b64 superMegaTajne*Heslo542555 => c3VwZXJNZWdhVGFqbmUqSGVzbG81NDI1NTU=
    SecretService.secrets.set('myApiKey', 'superMegaTajne*Heslo542555');
    SecretService.secrets.set('MyApiKey', 'superMegaTajne*Heslo542555');
  }

  getSecretForKey(key: string): Uint8Array {
    const secretString = SecretService.secrets.get(key) || '';
    console.log(`Secret for key ${key} is ${secretString}`);
    return Buffer.from(secretString);
  }
}
