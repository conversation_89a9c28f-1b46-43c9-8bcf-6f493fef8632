mds# AudioManager Module

## Overview

The `AudioManager` class is responsible for managing audio input processing and ASR service integration for the AudioConnector Server. Barge-in detection and playback state are now managed centrally by the `BargeInManager` class, which AudioManager consults for all playback state and barge-in event handling. AudioManager acts as an abstraction layer between the session and the underlying ASR service, providing a clean interface for handling audio events.

## Current Architecture

- **AudioManager**: Handles ASR service initialization and event wiring. Consults BargeInManager for playback state and barge-in event routing.
- **BargeInManager**: Central authority for playback state and barge-in event routing.
- **ASR Service**: Provides speech-to-text capabilities and emits transcript events.

## Implementation Progress Checklist

- [x] Create initial `AudioManager` class with ASR integration and event wiring
- [x] Add pause detection for stable stream processing
- [x] Add barge-in detection logic (speech during playback)
- [x] Integrate with session for barge-in event handling
- [x] Add streaming-only mode without ASR finalization
- [x] Add environment variable configuration
- [x] Centralize playback state and barge-in event routing in BargeInManager
- [x] Remove legacy playback state and barge-in logic from AudioManager

## ASR Streaming Modes

The `AudioManager` supports three different modes for handling speech recognition:

### 1. Standard Mode (`ASR_STREAMING_MODE=standard`)

- Uses the ASR service's default configuration
- Processes utterances when the ASR service determines they are complete
- Best for scenarios where you want to rely on the ASR's built-in logic

### 2. Hybrid Mode (`ASR_STREAMING_MODE=hybrid`)

- Configures ASR with relaxed endpointing
- Uses both ASR final transcripts and pause detection
- Processes utterances either when the ASR finalizes them or when a pause is detected
- Good balance between ASR intelligence and application control

### 3. Continuous Mode (`ASR_STREAMING_MODE=continuous`)

- Configures ASR with very relaxed endpointing to minimize final results
- Ignores ASR final transcripts completely
- Relies entirely on pause detection to determine when utterances are complete
- Provides more consistent behavior and control over utterance completion
- Best for scenarios where you need precise control over utterance completion

### Pause Detection

- Tracks the timestamp of the last detected speech in interim transcripts
- If a pause longer than the configured threshold is detected, emits a final transcript
- The pause threshold can be configured using the `PAUSE_THRESHOLD_MS` environment variable (default: 1500ms)
- Enable by setting `ENABLE_STABLE_STREAM=true`
- Required for both Hybrid and Continuous modes

These features enable the system to process user utterances as soon as a natural pause is detected, rather than waiting for the ASR's built-in silence detection.

## Usage Example

```typescript
const audioManager = new AudioManager(
  (transcript, isFinal) => {
    /* handle transcript */
  },
  (source, text) => {
    /* handle barge-in */
  },
  error => {
    /* handle error */
  }
);

await audioManager.initialize();
audioManager.setAudioPlayingState(true);
await audioManager.processAudio(audioData);
```

## Barge-in Detection

The `AudioManager` implements barge-in detection by monitoring ASR transcript events during audio playback. If speech with sufficient confidence is detected while audio is playing, the `onBargeIn` callback is triggered (only once per playback event). This allows the system to interrupt playback and respond to user input immediately.

- Barge-in can be enabled/disabled using the `ENABLE_BARGE_IN` environment variable.
- The confidence threshold for barge-in can be configured using the `BARGE_IN_CONFIDENCE_THRESHOLD` environment variable (default: 0.6).
- The barge-in trigger is reset each time playback stops.
- This logic ensures that only the first detected speech during playback triggers a barge-in event.

## Testing Strategy

- Unit tests for event wiring and state management
- Integration tests for barge-in and pause detection (to be added)

## Dependencies and Integration Points

- Depends on ASR service implementations (e.g., Azure, Google)
- Integrated with `Session` for playback state, transcript, and barge-in event handling
- Used in conjunction with `DTMFManager` for unified input and barge-in architecture
- Uses environment variables for configuration:
  - `ENABLE_BARGE_IN`: Set to 'true' to enable barge-in functionality
  - `BARGE_IN_CONFIDENCE_THRESHOLD`: Confidence threshold for speech barge-in (0.0-1.0)
  - `PAUSE_THRESHOLD_MS`: Pause threshold in milliseconds for stable stream detection
  - `ENABLE_STABLE_STREAM`: Set to 'true' to enable stable stream processing
  - `ASR_STREAMING_MODE`: Set to 'standard', 'hybrid', or 'continuous' to configure ASR streaming behavior
