/**
 * AudioServiceAdapter
 *
 * Adapts the Session class to the AudioManager and related audio services.
 * This adapter encapsulates all audio-related functionality that was previously
 * embedded in the Session class, providing a clean interface for audio handling.
 *
 * Now supports event emission for event-driven architecture.
 */

// AudioManager import removed as it's not used
import { BargeInManager } from '../barge-in/barge-in-manager';
import { EnhancedASRService } from '../asr-service/index';
import { SessionState } from '../../session/session-state-manager';
import { EventDrivenStateManager } from '../../session/state-machine/event-driven-state-manager';
import { IWebSocketController } from '../../session/websocket-controller';
import { Transcript } from '../speech';
import { DTMFManager } from '../dtmf/dtmf-manager';
import { EventEmitter, EventPriority } from '../events/event-emitter';
import {
  SessionEventType,
  AudioDataEventData,
  PlaybackStartedEventData,
  PlaybackCompletedEventData,
  TranscriptReceivedEventData,
} from '../events/session-events';
import { logInfo, logDebug, logError, logWarning, logMetrics } from '../logging/logger';
import { RequestContext } from '../monitoring/performance-logger';
import { ASRServiceNotReadyError } from './errors';

export type TranscriptHandler = (transcript: Transcript, isFinal: boolean) => void;
export type ErrorHandler = (error: Error | string) => void;

/**
 * Options for AudioServiceAdapter
 */
export interface AudioServiceAdapterOptions {
  /**
   * Optional event emitter for event-based communication
   */
  eventEmitter?: EventEmitter;
}

export class AudioServiceAdapter {
  // AudioManager removed: all audio/ASR/barge-in logic is now handled by adapters and managers.
  private bargeInManager: BargeInManager;
  private enhancedASRService: EnhancedASRService | null = null;
  private stateManager: EventDrivenStateManager;
  private wsController: IWebSocketController;
  private dtmfManager: DTMFManager | null = null;
  private eventEmitter?: EventEmitter;
  private isEventEmissionEnabled = true;

  // Track the current TTS metrics context for playback
  private currentTtsMetricsContext?: RequestContext;
  // Track the current ASR metrics context for phase tracking
  private currentAsrMetricsContext?: RequestContext;

  /**
   * Set the current TTS metrics context for phase tracking.
   */
  public setCurrentTtsMetricsContext(context?: RequestContext) {
    this.currentTtsMetricsContext = context;
  }

  // Track the timestamp when user speech ends (for ASR latency measurement)
  private asrUserEndTime: number | null = null;

  // Track all ASR call durations for the current utterance
  private asrCallDurations: number[] = [];

  // (Removed: currentTtsPhaseContext, use explicit phase management)

  constructor(
    bargeInManager: BargeInManager,
    wsController: IWebSocketController,
    stateManager: EventDrivenStateManager,
    dtmfManager?: DTMFManager,
    options?: AudioServiceAdapterOptions
  ) {
    this.bargeInManager = bargeInManager;
    this.wsController = wsController;
    this.stateManager = stateManager;
    this.dtmfManager = dtmfManager || null;
    this.eventEmitter = options?.eventEmitter;

    // If we have an event emitter, log that event emission is enabled
    if (this.eventEmitter) {
      logDebug('[AudioServiceAdapter] Event emission enabled');
    }
  }

  /**
   * Set the event emitter after construction
   * This allows adding event emission to an existing AudioServiceAdapter
   */
  public setEventEmitter(eventEmitter: EventEmitter): void {
    this.eventEmitter = eventEmitter;
    logDebug('[AudioServiceAdapter] Event emitter set, event emission enabled');
  }

  /**
   * Enable or disable event emission
   */
  public setEventEmissionEnabled(enabled: boolean): void {
    this.isEventEmissionEnabled = enabled;
    logDebug(`[AudioServiceAdapter] Event emission ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Emit an event if event emission is enabled
   */
  private emit<T>(
    eventType: SessionEventType,
    data: T,
    priority: EventPriority = EventPriority.MEDIUM
  ): void {
    if (this.eventEmitter && this.isEventEmissionEnabled) {
      this.eventEmitter.emit(eventType, data, priority);
    }
  }

  /**
   * Set the ASR service and connect it to the AudioManager
   */
  setASRService(asrService: EnhancedASRService): void {
    this.enhancedASRService = asrService;

    // Connect the ASR service to the AudioManager for pause detection
    if (asrService) {
      // Get the underlying BaseSpeechService from the EnhancedASRService
      const baseSpeechService = asrService.getASRService();

      if (baseSpeechService) {
        // Set the ASR service for pause detection if needed (AudioManager removed)
        // TODO: Wire baseSpeechService to pause detection logic or adapter as needed.
        logDebug('[AudioServiceAdapter] Connected ASR service for pause detection');

        // Always subscribe to transcript events, regardless of eventEmitter
        // The EnhancedASRService doesn't expose a direct onTranscript method,
        // but we can use the constructor callbacks that are already set up
        // We'll hook into the ASR service events directly
        baseSpeechService
          .on('transcript', (transcript: Transcript) => {
            // Always emit transcript received event
            const isFinal =
              (transcript as any).isFinal === true || (transcript as any).final === true;
            this.emit(
              SessionEventType.TRANSCRIPT_RECEIVED,
              {
                transcript,
                isFinal,
                timestamp: Date.now(),
              } as TranscriptReceivedEventData,
              EventPriority.HIGH // Changed to HIGH priority for faster barge-in detection
            );
            // If this is a final transcript, do NOT finalize here.
            // ASR phase finalization is now handled exclusively by the state machine.
            // This avoids race conditions and ensures robust, single-path finalization.
          })
          .on('final-transcript', (transcript: Transcript) => {
            // Emit transcript received event for final transcripts
            this.emit(
              SessionEventType.TRANSCRIPT_RECEIVED,
              {
                transcript,
                isFinal: true,
                timestamp: Date.now(),
                inputType: 'speech',
              } as TranscriptReceivedEventData,
              EventPriority.HIGH
            );
          });

        logDebug('[AudioServiceAdapter] Subscribed to ASR transcript events');
      } else {
        logError('[AudioServiceAdapter] Failed to get BaseSpeechService from EnhancedASRService');
      }
    }
  }

  /**
   * Process audio data through the ASR service
   */
  async processAudio(data: Uint8Array, metricsContext: RequestContext): Promise<void> {
    // Robust ASR phase tracking: assign metrics context for this user input turn
    this.currentAsrMetricsContext = metricsContext;

    // Check if we're in a valid state to process audio
    if (this.stateManager.isDisconnectingOrClosed()) {
      logInfo('[AudioServiceAdapter] Session is closing, cannot process audio');
      return;
    }

    // If we don't have an ASR service, we can't process audio
    if (!this.enhancedASRService) {
      logWarning('[AudioServiceAdapter] No ASR service available, cannot process audio');
      return;
    }

    // Emit audio data event
    this.emit(
      SessionEventType.AUDIO_DATA,
      {
        audioData: data,
        timestamp: Date.now(),
      } as AudioDataEventData,
      EventPriority.LOW // Lower priority since this is a high-frequency event
    );

    // Always initialize asrCallDurations if not present
    if (!this.asrCallDurations) {
      logDebug('[ASR METRICS] No durations found, setting new ones');
      this.asrCallDurations = [];
    }

    const currentState = this.stateManager.getState();

    // Handle state transitions based on current state
    if (currentState === SessionState.IDLE || currentState === SessionState.PLAYING) {
      // These states can directly transition to PROCESSING_INPUT
      // State transition to PROCESSING_INPUT is now handled only after a transcript is available.
      // The Session class (handleTranscript) will perform the transition with the required metadata.
    } else if (
      currentState === SessionState.PROCESSING_BOT ||
      currentState === SessionState.RESPONDING
    ) {
      // For these states, we still process audio but don't change state
      // This allows barge-in detection to work while respecting the state machine
      // The barge-in system will handle the state transition when appropriate
      if (this.bargeInManager.isBargeInEnabled() && this.stateManager.isPlaying()) {
        // If barge-in is enabled and we're in PLAYING state, barge-in can occur
        logDebug('[AudioServiceAdapter] Processing audio for potential barge-in detection');
      } else {
        // Otherwise, we're just collecting audio but not changing state yet
        // logDebug('[AudioServiceAdapter] Collecting audio while in ' + currentState + ' state');
      }
    }

    // Always process the audio data regardless of state
    // This ensures barge-in detection works and audio isn't lost

    // --- ASR service call timing (high-resolution) ---
    const asrStart =
      typeof performance !== 'undefined' && performance.now ? performance.now() : Date.now();
    await this.enhancedASRService.processAudio(data, metricsContext);
    const asrEnd =
      typeof performance !== 'undefined' && performance.now ? performance.now() : Date.now();
    const asrDuration = asrEnd - asrStart;

    // Instead of adding each call, accumulate durations for summary at utterance end
    this.asrCallDurations.push(asrDuration);
    // TEMP DEBUG: Log each ASR call duration as it's recorded
    logDebug(`[ASR METRICS] Added ASR service call`);
  }
  /**
   * Finalize the ASR phase and clear metrics context.
   */
  // (finalizeAsrPhase removed; ASR phase finalization is now handled by state machine)

  /**
   * Send audio data to the client
   */
  async sendAudio(bytes: Uint8Array, metricsContext?: RequestContext): Promise<void> {
    // Check if session is closed
    if (this.stateManager.isDisconnectingOrClosed()) {
      logWarning(
        '[AudioServiceAdapter] Attempted to send audio after session was closed or disconnecting.'
      );
      return;
    }

    // Only transition to PLAYING state if we're not already in RESPONDING state
    // If we're in RESPONDING, the StartPlaybackAction will handle the transition
    const currentState = this.stateManager.getState();
    if (currentState !== SessionState.RESPONDING) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logDebug(
            `[AudioServiceAdapter] Not transitioning to PLAYING: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
        } else {
          logDebug(
            '[AudioServiceAdapter] Not transitioning to PLAYING: a transition is in progress'
          );
        }
      } else {
        // No transition in progress, so we can transition to PLAYING
        logDebug('[AudioServiceAdapter] Transitioning to PLAYING state from ' + currentState);
        await this.stateManager.setState(SessionState.PLAYING, {
          reason: 'Sending audio to client',
          audioSize: bytes.length,
        });
      }
    } else {
      logDebug(
        '[AudioServiceAdapter] Already in RESPONDING state, StartPlaybackAction will handle transition to PLAYING'
      );
    }

    // Enable barge-in detection in BargeInManager
    this.bargeInManager.enableBargeIn();

    try {
      logDebug(`[Audio] Sending ${bytes.length} bytes of TTS audio data`);

      // Emit playback started event
      this.emit(
        SessionEventType.PLAYBACK_STARTED,
        {
          audioBytes: bytes,
          timestamp: Date.now(),
        } as PlaybackStartedEventData,
        EventPriority.MEDIUM
      );

      // Use the WebSocketController to send audio (handles chunking internally)
      await this.wsController.sendAudio(bytes);

      // End the TTS phase timing for the current request - the audio was sent
      // and it's length isn't consequential for measuring the system
      // responsitivity

      if (this.currentTtsMetricsContext) {
        this.currentTtsMetricsContext.endPhase('textToSpeech');
        this.currentTtsMetricsContext = undefined;
      }

      logDebug(
        `[Audio] TTS audio sent (${bytes.length} bytes) - waiting for PlaybackStarted event`
      );
    } catch (error) {
      // If an error occurs, disable barge-in detection
      this.bargeInManager.disableBargeIn();

      // Transition back to IDLE state
      await this.stateManager.setState(SessionState.IDLE, {
        reason: 'Error sending audio',
      });

      // Re-throw the error to be handled by the caller
      throw error;
    } finally {
      // Do not end the TTS phase here; it will be ended on playback completed event
    }
  }

  /**
   * Set the playback state
   */
  async setPlaybackState(state: 'playing' | 'paused' | 'stopped'): Promise<void> {
    // Enable or disable barge-in based on playback state
    if (state === 'playing') {
      this.bargeInManager.enableBargeIn();

      // Emit playback started event
      this.emit(
        SessionEventType.PLAYBACK_STARTED,
        {
          timestamp: Date.now(),
        } as PlaybackStartedEventData,
        EventPriority.MEDIUM
      );
    } else if (state === 'stopped') {
      this.bargeInManager.disableBargeIn();

      // Emit playback completed event
      this.emit(
        SessionEventType.PLAYBACK_COMPLETED,
        {
          timestamp: Date.now(),
        } as PlaybackCompletedEventData,
        EventPriority.MEDIUM
      );
    }

    // Update session state based on playback state
    if (state === 'playing' && !this.stateManager.isPlaying()) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logDebug(
            `[AudioServiceAdapter] Not transitioning to PLAYING: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
          return;
        }
      }

      await this.stateManager.setState(SessionState.PLAYING, {
        reason: 'Audio playback started',
        playbackStatus: state,
      });
    } else if (state === 'stopped' && this.stateManager.isPlaying()) {
      // Check if a transition is already in progress
      if (
        typeof this.stateManager.isStateTransitionInProgress === 'function' &&
        this.stateManager.isStateTransitionInProgress()
      ) {
        // Get information about the current transition
        const currentTransition =
          typeof this.stateManager.getCurrentTransitionInfo === 'function'
            ? this.stateManager.getCurrentTransitionInfo()
            : null;

        if (currentTransition) {
          logDebug(
            `[AudioServiceAdapter] Not transitioning to IDLE: another transition is already in progress (${currentTransition.from} → ${currentTransition.to})`
          );
          return;
        }
      }

      await this.stateManager.setState(SessionState.IDLE, {
        reason: 'Audio playback stopped',
        playbackStatus: state,
      });
    }
  }

  /**
   * Get the current playback state based on session state
   */
  getPlaybackState(): 'playing' | 'paused' | 'stopped' {
    // Determine playback state based on session state
    if (this.stateManager.isPlaying()) {
      return 'playing';
    } else {
      return 'stopped';
    }
  }

  /**
   * Process DTMF input
   * @param digit The DTMF digit to process
   */
  processDTMF(digit: string): void {
    // Check if we have a DTMF manager
    if (this.dtmfManager) {
      // Process DTMF input through the DTMFManager
      this.dtmfManager.processDigit(digit);
      logDebug(`[AudioServiceAdapter] Processed DTMF digit: ${digit}`);
    } else {
      logWarning('[AudioServiceAdapter] No DTMFManager available, cannot process DTMF');
    }
  }

  /**
   * Set the DTMF manager
   * @param dtmfManager The DTMF manager to use
   */
  setDTMFManager(dtmfManager: DTMFManager): void {
    this.dtmfManager = dtmfManager;
  }

  /**
   * Dispose of audio resources
   */
  async dispose(): Promise<void> {
    // Dispose of ASR service
    if (this.enhancedASRService) {
      try {
        await this.enhancedASRService.dispose();
      } catch (error) {
        logError(
          `[AudioServiceAdapter] Error disposing ASR service: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    }

    // AudioManager removed: no disposal needed.
    // (No AudioManager disposal needed.)
  }
  /**
   * Call this when user speech ends (pause detected or VAD end).
   * Records the timestamp for ASR latency measurement.
   */
  public onUserSpeechEnd(): void {
    this.asrUserEndTime = Date.now();
  }
  /**
   * Arm the ASR/audio pipeline to be ready for new user input.
   * This should be called when entering the IDLE state.
   */
  public async armForInput(): Promise<void> {
    // Always (re)start pause detection timer when preparing for user input
    // AudioManager removed: pause detection should be handled by ASR service or another component if needed.
    const sessionState = this.stateManager.getState?.() || 'unknown';
    const asrType = this.enhancedASRService?.constructor?.name || typeof this.enhancedASRService;
    logInfo(
      `[AudioServiceAdapter] armForInput: Called in session state: ${sessionState}, ASR type: ${asrType}`
    );

    if (this.stateManager.isDisconnectingOrClosed()) {
      logDebug(
        '[AudioServiceAdapter] Not arming ASR for input: session is disconnecting or closed'
      );
      return;
    }
    if (!this.enhancedASRService) {
      logError(
        '[AudioServiceAdapter] FATAL: Cannot arm ASR for input: enhancedASRService is not set. This violates the session initialization invariant: ASR service must be initialized and set before arming for input. Halting further state transitions and arming attempts.'
      );
      throw new ASRServiceNotReadyError(
        'FATAL: Cannot arm ASR for input: enhancedASRService is not set. This violates the session initialization invariant.'
      );
    }
    try {
      await this.enhancedASRService.armForInput();
      logDebug('[AudioServiceAdapter] ASR/audio pipeline armed for new user input');
      // Post-arm checks and logs
      const asrType = this.enhancedASRService?.constructor?.name || typeof this.enhancedASRService;
    } catch (error) {
      logError(
        `[AudioServiceAdapter] Error arming ASR for input: ${
          error instanceof Error ? error.stack || error.message : String(error)
        }`
      );
    }
    logDebug('[AudioServiceAdapter] armForInput: Completed');
  }
}
