# DTMF Service: Technical Overview

## Purpose

The DTMF (Dual-Tone Multi-Frequency) Service handles the processing of DTMF digit input from clients, manages digit sequences, and provides barge-in detection for DTMF input during audio playback. It abstracts the underlying DTMF processing and provides a clean interface for session integration.

---

## Key Responsibilities

- Process DTMF digit input from clients
- Accumulate digits into complete sequences
- Detect DTMF-based barge-in during audio playback
- Emit events for sequence completion
- Provide a clean interface for session integration

---

## Core Components and Their Roles

### DTMFManager (`src/services/dtmf/dtmf-manager.ts`)

- High-level manager for DTMF processing
- Handles digit input and sequence completion
- Detects DTMF barge-in during audio playback
- Provides callbacks for sequence completion and barge-in
- Integrates with BargeInManager for playback state

### DTMFService (`src/services/dtmf-service.ts`)

- Low-level service for DTMF digit accumulation
- Collects digits until a terminating character (#) is received
- Emits events for completed sequences
- Manages internal state (None, Processing, Complete)

---

## DTMF Sequence Processing

The DTMF Service processes digit sequences as follows:

1. Digits are accumulated until a terminating character (#) is received
2. When the terminating character is received, the sequence is considered complete
3. The complete sequence is emitted as an event
4. A new sequence can then be started

This allows for flexible input of variable-length digit sequences.

---

## Barge-In Detection

The DTMFManager integrates with the BargeInManager to detect DTMF barge-in:

- When a DTMF digit is received during audio playback, it's considered a barge-in
- The barge-in event is emitted to registered callbacks
- The BargeInManager is notified of the DTMF barge-in
- The Session can then stop playback and process the barge-in

---

## Component Interactions

1. **Initialization**: DTMFManager is created during session initialization
2. **Digit Processing**: Client sends DTMF digits which are processed by the manager
3. **Barge-In Detection**: Manager detects barge-in if digits are received during playback
4. **Sequence Completion**: When a sequence is complete, callbacks are notified
5. **State Management**: Manager tracks capturing state and service lifecycle

---

## Configuration Options

The DTMF Service can be configured through environment variables:

- `ENABLE_BARGE_IN`: Enable barge-in functionality (true/false)

---

## Design Principles

- **Separation of Concerns**: Clear separation between digit processing and sequence management
- **Event-Driven Architecture**: Asynchronous event emission for sequence completion
- **Clean Interface**: Simple API for session integration
- **State Management**: Explicit state tracking for DTMF processing
- **Integration**: Seamless integration with BargeInManager for playback state

---

## File Structure

- `src/services/dtmf/`
  - `dtmf-manager.ts` – High-level manager for DTMF processing
  - `__tests__/` – Unit tests for DTMF functionality
- `src/services/dtmf-service.ts` – Low-level service for DTMF digit accumulation

---

## Usage Example

```typescript
// Create DTMFManager with callbacks
const dtmfManager = new DTMFManager(
  digits => {
    // Handle complete DTMF sequence
    console.log(`DTMF sequence complete: ${digits}`);
    // Process the sequence (e.g., menu navigation)
    processDigitSequence(digits);
  },
  (source, digit) => {
    // Handle DTMF barge-in
    console.log(`DTMF barge-in detected with digit: ${digit}`);
    // Stop playback and process the barge-in
    stopPlayback();
  },
  bargeInManager
);

// Process a DTMF digit
dtmfManager.processDigit('1');
dtmfManager.processDigit('2');
dtmfManager.processDigit('3');
dtmfManager.processDigit('#'); // Completes the sequence

// Check if DTMF capture is active
const isCapturing = dtmfManager.isCapturing();
```

---

## Integration with Other Services

The DTMF Service integrates with several other services:

- **Session**: Processes DTMF input and handles sequence completion
- **BargeInManager**: Provides playback state for barge-in detection
- **WebSocket Controller**: Receives DTMF messages from clients
- **AudioServiceAdapter**: Processes DTMF input through the manager
