import { DTMFManager } from '../dtmf-manager';
import { DTMFService } from '../dtmf-service';
import * as envVars from '../../../common/environment-variables';

// Import the real BargeInManager to extend it
import { BargeInManager } from '../../barge-in/barge-in-manager';

// Create a mock BargeInManager
const mockGetPlaybackState = jest.fn().mockReturnValue('stopped');
const mockSetPlaybackState = jest.fn();
const mockOnBargeIn = jest.fn().mockReturnValue(() => {});
const mockOnPlaybackStateChange = jest.fn().mockReturnValue(() => {});
const mockDetectAudioBargeIn = jest.fn();
const mockDetectDtmfBargeIn = jest.fn();
const mockEmitBargeIn = jest.fn();
const mockIsBargeInEnabled = jest.fn().mockReturnValue(true);

// Mock the BargeInManager class
jest.mock('../../barge-in/barge-in-manager', () => {
  return {
    BargeInManager: jest.fn().mockImplementation(() => {
      return {
        callbacks: new Set(),
        playbackState: 'stopped',
        playbackStateCallbacks: new Set(),
        getPlaybackState: mockGetPlaybackState,
        setPlaybackState: mockSetPlaybackState,
        onBargeIn: mockOnBargeIn,
        onPlaybackStateChange: mockOnPlaybackStateChange,
        detectAudioBargeIn: mockDetectAudioBargeIn,
        detectDtmfBargeIn: mockDetectDtmfBargeIn,
        emitBargeIn: mockEmitBargeIn,
        isBargeInEnabled: mockIsBargeInEnabled,
      };
    }),
  };
});

const mockBargeInManager = new BargeInManager();

// Mock the environment variables
jest.mock('../../../common/environment-variables', () => ({
  isBargeInEnabled: jest.fn().mockReturnValue(true),
  getBargeInConfidenceThreshold: jest.fn().mockReturnValue(0.6),
  getPauseThresholdMs: jest.fn().mockReturnValue(1500),
  isStableStreamEnabled: jest.fn().mockReturnValue(true),
}));

// Mock the DTMFService
jest.mock('../dtmf-service', () => {
  return {
    DTMFService: jest.fn().mockImplementation(() => {
      const listeners: Record<string, Array<(digits: string) => void>> = {};
      return {
        on: jest.fn((event: string, listener: (digits: string) => void) => {
          if (!listeners[event]) {
            listeners[event] = [];
          }
          listeners[event].push(listener);
          return this;
        }),
        getState: jest.fn().mockReturnValue('None'),
        processDigit: jest.fn((digit: string) => {
          if (digit === '#') {
            listeners['final-digits']?.forEach(listener => listener('123'));
          }
          return this;
        }),
        // Helper to trigger events in tests
        _emit: (event: string, data: string) => {
          listeners[event]?.forEach(listener => listener(data));
        },
      };
    }),
  };
});

describe('DTMFManager', () => {
  let dtmfManager: DTMFManager;
  let onDigitsCompleteMock: jest.Mock;
  let onBargeInMock: jest.Mock;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();

    // Create mock callbacks
    onDigitsCompleteMock = jest.fn();
    onBargeInMock = jest.fn();

    // Create DTMFManager instance
    dtmfManager = new DTMFManager(onDigitsCompleteMock, onBargeInMock, mockBargeInManager);
  });

  describe('initialization', () => {
    it('should initialize with correct default values', () => {
      expect(dtmfManager.isCapturing()).toBe(false);
    });
  });

  describe('processDigit', () => {
    it('should start capturing DTMF when first digit is processed', () => {
      expect(dtmfManager.isCapturing()).toBe(false);
      dtmfManager.processDigit('1');
      expect(dtmfManager.isCapturing()).toBe(true);
    });

    it('should create a new DTMFService instance when processing a digit', () => {
      dtmfManager.processDigit('1');
      expect(DTMFService).toHaveBeenCalledTimes(1);
    });

    it('should pass the digit to the DTMFService', () => {
      dtmfManager.processDigit('1');
      const mockDTMFServiceInstance = (DTMFService as jest.Mock).mock.results[0].value;
      expect(mockDTMFServiceInstance.processDigit).toHaveBeenCalledWith('1');
    });

    it('should not create a new DTMFService instance if one already exists and is not complete', () => {
      dtmfManager.processDigit('1');
      expect(DTMFService).toHaveBeenCalledTimes(1);

      // Get the mock instance after it's been created
      const mockDTMFServiceInstance = (DTMFService as jest.Mock).mock.results[0].value;

      // Mock that the service is still processing
      mockDTMFServiceInstance.getState.mockReturnValue('Processing');

      dtmfManager.processDigit('2');
      expect(DTMFService).toHaveBeenCalledTimes(1); // Still just one instance
    });

    it('should create a new DTMFService instance if the previous one is complete', () => {
      dtmfManager.processDigit('1');
      expect(DTMFService).toHaveBeenCalledTimes(1);

      // Get the mock instance after it's been created
      const mockDTMFServiceInstance = (DTMFService as jest.Mock).mock.results[0].value;

      // Mock that the service is complete
      mockDTMFServiceInstance.getState.mockReturnValue('Complete');

      dtmfManager.processDigit('2');
      expect(DTMFService).toHaveBeenCalledTimes(2); // A new instance should be created
    });
  });

  describe('barge-in detection', () => {
    it('should trigger barge-in when barge-in is enabled', () => {
      // Mock barge-in as enabled
      mockIsBargeInEnabled.mockReturnValue(true);

      dtmfManager.processDigit('1');
      expect(onBargeInMock).toHaveBeenCalledWith('dtmf', '1');
    });

    it('should not trigger barge-in when barge-in is disabled', () => {
      // Mock barge-in as disabled
      (envVars.isBargeInEnabled as jest.Mock).mockReturnValueOnce(false);

      // Create a new DTMFManager instance with barge-in disabled
      const dtmfManagerDisabled = new DTMFManager(
        onDigitsCompleteMock,
        onBargeInMock,
        mockBargeInManager
      );

      // Set audio playing and process a digit
      mockGetPlaybackState.mockReturnValue('playing');
      dtmfManagerDisabled.processDigit('1');

      // Barge-in should not be triggered
      expect(onBargeInMock).not.toHaveBeenCalled();
    });

    it('should log barge-in detection', () => {
      // Mock the logging function
      const logInfoMock = jest.spyOn(require('../../logging/logger'), 'logInfo');

      // Ensure barge-in is enabled
      mockIsBargeInEnabled.mockReturnValue(true);

      dtmfManager.processDigit('1');

      // Check that the barge-in was logged
      expect(logInfoMock).toHaveBeenCalledWith(expect.stringContaining('[DTMF] Barge-in detected'));

      // Restore the mock
      logInfoMock.mockRestore();
    });
  });

  describe('DTMF sequence completion', () => {
    it('should call onDigitsComplete callback when sequence is complete', () => {
      dtmfManager.processDigit('#'); // This will trigger the 'final-digits' event in our mock
      expect(onDigitsCompleteMock).toHaveBeenCalledWith('123');
    });

    it('should reset capturing state when sequence is complete', () => {
      dtmfManager.processDigit('1');
      expect(dtmfManager.isCapturing()).toBe(true);

      // Simulate the 'final-digits' event
      const mockDTMFServiceInstance = (DTMFService as jest.Mock).mock.results[0].value;
      mockDTMFServiceInstance._emit('final-digits', '123');

      expect(onDigitsCompleteMock).toHaveBeenCalledWith('123');
      expect(dtmfManager.isCapturing()).toBe(false);
    });
  });

  describe('barge-in manager integration', () => {
    it('should check barge-in status from the barge-in manager', () => {
      // Mock barge-in as enabled
      mockIsBargeInEnabled.mockReturnValue(true);
      dtmfManager.processDigit('1');
      expect(onBargeInMock).toHaveBeenCalled();
      expect(mockIsBargeInEnabled).toHaveBeenCalled();

      // Clear mocks
      onBargeInMock.mockClear();
      mockIsBargeInEnabled.mockClear();

      // Mock barge-in as disabled
      mockIsBargeInEnabled.mockReturnValue(false);
      dtmfManager.processDigit('2');
      expect(onBargeInMock).not.toHaveBeenCalled();
      expect(mockIsBargeInEnabled).toHaveBeenCalled();
    });
  });
});
