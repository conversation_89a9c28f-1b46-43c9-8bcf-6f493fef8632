import { DTMFService } from '../dtmf-service';

describe('DTMFService', () => {
  let dtmfService: DTMFService;

  beforeEach(() => {
    dtmfService = new DTMFService();
  });

  describe('initialization', () => {
    it('should initialize with "None" state', () => {
      expect(dtmfService.getState()).toBe('None');
    });
  });

  describe('event handling', () => {
    it('should register event listeners correctly', () => {
      const mockListener = jest.fn();
      const result = dtmfService.on('final-digits', mockListener);

      // Should return itself for chaining
      expect(result).toBe(dtmfService);

      // Process a terminating digit to trigger the event
      dtmfService.processDigit('#');

      // Verify the listener was called
      expect(mockListener).toHaveBeenCalledWith('');
    });
  });

  describe('processDigit', () => {
    it('should change state to "Processing" when processing a digit', () => {
      dtmfService.processDigit('1');
      expect(dtmfService.getState()).toBe('Processing');
    });

    it('should accumulate digits correctly', () => {
      const finalDigitsMock = jest.fn();
      dtmfService.on('final-digits', finalDigitsMock);

      dtmfService.processDigit('1');
      dtmfService.processDigit('2');
      dtmfService.processDigit('3');
      dtmfService.processDigit('#');

      expect(finalDigitsMock).toHaveBeenCalledWith('123');
    });

    it('should change state to "Complete" when processing a terminating digit', () => {
      dtmfService.processDigit('#');
      expect(dtmfService.getState()).toBe('Complete');
    });

    it('should emit "final-digits" event when processing a terminating digit', () => {
      const finalDigitsMock = jest.fn();
      dtmfService.on('final-digits', finalDigitsMock);

      dtmfService.processDigit('1');
      dtmfService.processDigit('2');
      dtmfService.processDigit('#');

      expect(finalDigitsMock).toHaveBeenCalledWith('12');
    });

    it('should return itself for chaining', () => {
      const result = dtmfService.processDigit('1');
      expect(result).toBe(dtmfService);
    });

    it('should emit an error when trying to process digits after completion', () => {
      const errorMock = jest.fn();
      dtmfService.on('error', errorMock);

      // Complete the service
      dtmfService.processDigit('#');

      // Try to process another digit
      dtmfService.processDigit('1');

      expect(errorMock).toHaveBeenCalledWith('DTMF digits already received.');
    });

    it('should clear digits after completion', () => {
      const finalDigitsMock = jest.fn();
      dtmfService.on('final-digits', finalDigitsMock);

      dtmfService.processDigit('1');
      dtmfService.processDigit('2');
      dtmfService.processDigit('#');

      expect(finalDigitsMock).toHaveBeenCalledWith('12');

      // Create a new service to verify digits are cleared
      const newService = new DTMFService();
      const newFinalDigitsMock = jest.fn();
      newService.on('final-digits', newFinalDigitsMock);

      newService.processDigit('#');

      // Should emit with empty string, not '12'
      expect(newFinalDigitsMock).toHaveBeenCalledWith('');
    });
  });
});
