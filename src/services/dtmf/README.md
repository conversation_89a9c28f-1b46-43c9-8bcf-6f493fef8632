# DTMFManager Module

## Overview

The `DTMFManager` class is responsible for handling DTMF digit input, sequence completion, and DTMF-based barge-in detection for the AudioConnector Server. It abstracts the underlying `DTMFService` and provides a clean interface for session integration.

## Current Architecture

- **DTMFManager**: Handles DTMF digit processing, sequence completion, and barge-in detection.
- **DTMFService**: Emits events for DTMF digit collection and completion.

## Implementation Progress Checklist

- [x] Implement `DTMFManager` class with event wiring and state management
- [x] Integrate with `Session` for DTMF input and barge-in handling
- [x] Remove legacy DTMFService logic from session
- [x] Add unit tests

## Usage Example

```typescript
const dtmfManager = new DTMFManager(
  digits => {
    /* handle DTMF sequence complete */
  },
  (source, digit) => {
    /* handle DTMF barge-in */
  }
);

dtmfManager.setAudioPlayingState(true);
dtmfManager.processDigit('1');
```

## Testing Strategy

- Unit tests for DTMF sequence handling and barge-in detection:
  - `dtmf-manager.test.ts`: Tests for DTMFManager functionality including digit processing, barge-in detection, and sequence completion
  - `dtmf-service.test.ts`: Tests for DTMFService functionality including digit accumulation, termination handling, and event emission

## Dependencies and Integration Points

- Depends on `DTMFService` for low-level DTMF event handling
- Integrated with `Session` for playback state and event handling
- Uses environment variables for configuration:
  - `ENABLE_BARGE_IN`: Set to 'true' to enable barge-in functionality
  - `BARGE_IN_CONFIDENCE_THRESHOLD`: Confidence threshold for speech barge-in (0.0-1.0)
  - `PAUSE_THRESHOLD_MS`: Pause threshold in milliseconds for stable stream detection
  - `ENABLE_STABLE_STREAM`: Set to 'true' to enable stable stream processing
