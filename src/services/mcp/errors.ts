/**
 * Custom error types for MCP client failure modes.
 */

export class MCPNetworkError extends <PERSON>rror {
  constructor(message: string, public cause?: any) {
    super(message);
    this.name = 'MCPNetworkError';
  }
}

export class MCPTimeoutError extends Error {
  constructor(message: string, public cause?: any) {
    super(message);
    this.name = 'MCPTimeoutError';
  }
}

export class MCPHTTPError extends <PERSON>rror {
  constructor(
    message: string,
    public status: number,
    public errorText?: string,
    public cause?: any
  ) {
    super(message);
    this.name = 'MCPHTTPError';
  }
}

export class MCPJSONRPCError extends Error {
  constructor(message: string, public code?: number, public data?: any, public cause?: any) {
    super(message);
    this.name = 'MCPJSONRPCError';
  }
}

export class MCPMalformedResponseError extends Error {
  constructor(message: string, public cause?: any) {
    super(message);
    this.name = 'MCPMalformedResponseError';
  }
}
