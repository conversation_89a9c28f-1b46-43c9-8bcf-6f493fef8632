/**
 * MCP (Model Context Protocol) Types
 * 
 * Types for integrating with the MCP server for customer identification
 */

export interface MCPRequest {
  jsonrpc: '2.0';
  id: string | number;
  method: string;
  params?: any;
}

export interface MCPResponse {
  jsonrpc: '2.0';
  id: string | number;
  result?: any;
  error?: MCPError;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

export interface MCPTool {
  name: string;
  description: string;
  inputSchema: {
    type: string;
    properties: Record<string, any>;
    required?: string[];
  };
}

export interface MCPToolsListResponse {
  tools: MCPTool[];
}

export interface MCPToolCallRequest {
  name: string;
  arguments: Record<string, any>;
}

export interface MCPToolCallResponse {
  content: Array<{
    type: string;
    text?: string;
    data?: any;
  }>;
  isError?: boolean;
}

/**
 * Customer information retrieved from MCP server
 */
export interface CustomerInfo {
  ani: string;
  customerId?: string;
  customerName?: string;
  customerType?: string;
  accountStatus?: string;
  preferredLanguage?: string;
  lastContactDate?: string;
  notes?: string;
  metadata?: Record<string, any>;
}

/**
 * MCP service configuration
 */
export interface MCPServiceConfig {
  baseUrl: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Customer lookup result
 */
export interface CustomerLookupResult {
  success: boolean;
  customer?: CustomerInfo;
  error?: string;
  source: 'mcp' | 'cache' | 'fallback';
}
