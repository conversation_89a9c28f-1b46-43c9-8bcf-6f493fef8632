// MCP TypeScript interfaces for MCP integration

export interface MCPRequest {
  tool: string;
  params: Record<string, unknown>;
}

export interface MCPResponse {
  result: Record<string, unknown>;
}

export interface MCPError {
  code: string;
  message: string;
  details?: unknown;
}

export interface CustomerInfo {
  ani: string;
  name: string;
  metadata: Record<string, unknown>;
}

/**
 * Metadata for a tool provided by the MCP server.
 */
export interface MCPToolMetadata {
  name: string;
  arguments: Record<
    string,
    {
      type: string;
      required?: boolean;
      description?: string;
    }
  >;
  description?: string;
}

/**
 * Tool result type for MCP integration.
 * Extend this union/type map as more tools are added.
 */
export type ToolResult = { time: string } | null; // For getDateTime
// TODO: When more tools are added, use a discriminated union or type map for tool results.
