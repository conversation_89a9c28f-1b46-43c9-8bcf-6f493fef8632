/**
 * MCP (Model Context Protocol) Types
 *
 * Types for integrating with the MCP server for customer identification
 * Updated to use official SDK types where possible
 */

// Re-export official SDK types for convenience
export type {
  <PERSON><PERSON>,
  CallToolResult,
  ListToolsResult,
  JSONRPCRequest,
  JSONRPCResponse,
  JSONRPCError,
  JSONRPCMessage,
} from '@modelcontextprotocol/sdk/types.js';

export type { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';

// Legacy types for backward compatibility (deprecated - use SDK types instead)
/** @deprecated Use JSONRPCRequest from SDK instead */
export interface MCPRequest {
  tool: string;
  params: Record<string, unknown>;
}

/** @deprecated Use JSONRPCResponse from SDK instead */
export interface MCPResponse {
  result: Record<string, unknown>;
}

/** @deprecated Use JSONRPCError from SDK instead */
export interface MCPError {
  code: string;
  message: string;
  details?: unknown;
}

/**
 * Customer information retrieved from MCP server
 */
export interface CustomerInfo {
  ani: string;
  customerId?: string;
  customerName?: string;
  customerType?: string;
  accountStatus?: string;
  preferredLanguage?: string;
  lastContactDate?: string;
  notes?: string;
  metadata?: Record<string, unknown>;
}

/**
 * Customer lookup result
 */
export interface CustomerLookupResult {
  success: boolean;
  customer?: CustomerInfo;
  error?: string;
  source: 'mcp' | 'cache' | 'fallback';
}

/**
 * MCP service configuration
 */
export interface MCPServiceConfig {
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * Metadata for a tool provided by the MCP server.
 */
export interface MCPToolMetadata {
  name: string;
  arguments: Record<
    string,
    {
      type: string;
      required?: boolean;
      description?: string;
    }
  >;
  description?: string;
}

/**
 * Tool result type for MCP integration.
 * Extend this union/type map as more tools are added.
 */
export type ToolResult = { time: string } | null; // For getDateTime
// TODO: When more tools are added, use a discriminated union or type map for tool results.
