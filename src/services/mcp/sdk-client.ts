/**
 * Phase 1 Step 1.2: SDK Client Wrapper
 *
 * Wrapper around the official MCP SDK client with proper initialization
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import type { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import type { Tool, CallToolResult, ListToolsResult } from '@modelcontextprotocol/sdk/types.js';
import { logDebug, logError, logInfo } from '../logging/logger';

export interface SDKClientConfig {
  baseUrl?: string;
  timeout?: number;
  retryAttempts?: number;
  retryDelay?: number;
}

/**
 * SDK Client Wrapper for MCP
 *
 * Wraps the official MCP SDK client with our application-specific configuration
 */
export class SDKClient {
  private client: Client;
  private transport: Transport | null = null;
  private config: Required<SDKClientConfig>;
  private connected = false;

  constructor(config?: SDKClientConfig) {
    this.config = {
      baseUrl: config?.baseUrl || process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274',
      timeout: config?.timeout || parseInt(process.env.MCP_TIMEOUT || '5000'),
      retryAttempts: config?.retryAttempts || parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'),
      retryDelay: config?.retryDelay || parseInt(process.env.MCP_RETRY_DELAY || '1000'),
    };

    this.client = new Client(
      {
        name: 'genesys-voice-connector',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    logDebug(`[MCP SDK] Client created with config: ${JSON.stringify(this.config)}`);
  }

  /**
   * Connect to the MCP server
   */
  async connect(): Promise<void> {
    if (this.connected) {
      logDebug('[MCP SDK] Already connected');
      return;
    }

    try {
      logInfo(`[MCP SDK] Connecting to MCP server at ${this.config.baseUrl}`);

      this.transport = new StreamableHTTPClientTransport(new URL(this.config.baseUrl));

      await this.client.connect(this.transport);
      this.connected = true;

      logInfo('[MCP SDK] Successfully connected to MCP server');
    } catch (error) {
      logError(
        `[MCP SDK] Failed to connect to MCP server: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      await this.client.close();
      this.connected = false;
      this.transport = null;
      logInfo('[MCP SDK] Disconnected from MCP server');
    } catch (error) {
      logError(
        `[MCP SDK] Error during disconnect: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Check if connected to MCP server
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * List available tools from the MCP server
   */
  async listTools(): Promise<Tool[]> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server. Call connect() first.');
    }

    try {
      logDebug('[MCP SDK] Listing available tools');
      const result: ListToolsResult = await this.client.listTools();
      logDebug(`[MCP SDK] Found ${result.tools.length} tools`);
      return result.tools;
    } catch (error) {
      logError(
        `[MCP SDK] Failed to list tools: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Call a specific tool on the MCP server
   */
  async callTool(name: string, arguments_: Record<string, any> = {}): Promise<CallToolResult> {
    if (!this.connected) {
      throw new Error('Not connected to MCP server. Call connect() first.');
    }

    try {
      logDebug(`[MCP SDK] Calling tool: ${name} with arguments: ${JSON.stringify(arguments_)}`);
      const result = await this.client.callTool({
        name,
        arguments: arguments_,
      });
      logDebug(`[MCP SDK] Tool call successful: ${name}`);
      return result as CallToolResult;
    } catch (error) {
      logError(
        `[MCP SDK] Tool call failed: ${name} - ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Get the underlying SDK client (for advanced usage)
   */
  getClient(): Client {
    return this.client;
  }
}
