/**
 * MCP Service Module
 * 
 * Provides customer identification and data retrieval using the MCP server
 */

export { MCPService } from './mcp-service';
export { MCPClient } from './mcp-client';
export * from './types';

// Create a singleton instance for the application
let mcpServiceInstance: MCPService | null = null;

/**
 * Get the singleton MCP service instance
 */
export function getMCPService(): MCPService {
  if (!mcpServiceInstance) {
    mcpServiceInstance = new MCPService();
  }
  return mcpServiceInstance;
}

/**
 * Initialize the MCP service
 */
export async function initializeMCPService(): Promise<void> {
  const service = getMCPService();
  await service.initialize();
}

/**
 * Check if MCP service is available
 */
export function isMCPServiceAvailable(): boolean {
  return mcpServiceInstance?.isAvailable() ?? false;
}
