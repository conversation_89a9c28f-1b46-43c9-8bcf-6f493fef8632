# MCP Service Documentation

## Overview

The MCP (Model Context Protocol) Service provides customer identification and data retrieval capabilities by integrating with an external MCP server. This service is used to look up customer information based on ANI (Automatic Number Identification) during voice conversations.

## Architecture

The MCP service follows the established service patterns in the application:

- **MCPService**: Main service class that provides customer lookup functionality
- **MCPClient**: HTTP client that implements the MCP protocol for communication
- **Types**: TypeScript interfaces for type safety and documentation

## Configuration

The service is configured through environment variables:

```bash
# MCP Server Configuration
MCP_SERVER_URL=http://127.0.0.1:6274  # Local development
# MCP_SERVER_URL=https://aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2/mcp  # Production

# Optional Configuration
MCP_TIMEOUT=5000           # Request timeout in milliseconds
MCP_RETRY_ATTEMPTS=3       # Number of retry attempts
MCP_RETRY_DELAY=1000       # Delay between retries in milliseconds
```

## Usage

### Basic Usage

```typescript
import { getMCPService } from '../services/mcp';

// Initialize the service (usually done at application startup)
await initializeMCPService();

// Look up customer by ANI
const result = await getMCPService().lookupCustomer('1234567890');

if (result.success && result.customer) {
  console.log('Customer found:', result.customer.customerName);
} else {
  console.log('Customer lookup failed:', result.error);
}
```

### Integration with Session

The MCP service is integrated into the session lifecycle:

1. **Session Initialization**: Customer lookup is performed when ANI is available
2. **Bot Context**: Customer information is provided to the LLM for personalized responses
3. **Caching**: Customer data is cached to avoid repeated lookups

## Customer Information

The service retrieves and structures customer information:

```typescript
interface CustomerInfo {
  ani: string;                    // Phone number (required)
  customerId?: string;           // Unique customer identifier
  customerName?: string;         // Customer's name
  customerType?: string;         // Customer classification
  accountStatus?: string;        // Account status (active, suspended, etc.)
  preferredLanguage?: string;    // Customer's preferred language
  lastContactDate?: string;      // Last contact timestamp
  notes?: string;               // Additional notes
  metadata?: Record<string, any>; // Additional custom data
}
```

## Error Handling

The service implements robust error handling:

- **Connection Failures**: Gracefully handles MCP server unavailability
- **Tool Discovery**: Automatically finds appropriate customer lookup tools
- **Fallback Behavior**: Continues operation even when MCP is unavailable
- **Caching**: Reduces load and improves reliability through intelligent caching

## MCP Protocol Implementation

The service implements the MCP protocol specification:

1. **Tool Discovery**: Lists available tools from the MCP server
2. **Tool Execution**: Calls specific tools with proper parameters
3. **Response Parsing**: Handles various response formats from MCP tools
4. **Error Handling**: Properly handles MCP protocol errors

## Integration Points

### Session Integration

The MCP service integrates with the session at these points:

- **OpenMessageHandler**: Customer lookup triggered when ANI is available
- **State Machine**: Customer identification as part of session initialization
- **Bot Service**: Customer context provided to LLM for personalized responses

### State Machine Integration

Customer lookup is integrated into the state machine flow:

```
INITIALIZING → [Customer Lookup] → IDLE
```

If customer lookup fails, the session continues without customer context.

## Testing

### Unit Tests

Test the MCP service components:

```bash
npm test src/services/mcp/__tests__/
```

### Integration Tests

Test with actual MCP server:

```bash
# Start MCP server locally
cd ~/Dev/aidcc-ccczautomationmcpserver
npm start

# Run integration tests
npm test -- --grep "MCP Integration"
```

### Manual Testing

Use the MCP Inspector to verify server functionality:
- Open http://127.0.0.1:6274/resources in browser
- Test tool discovery and execution
- Verify customer lookup tools are available

## Monitoring and Logging

The service integrates with the application's logging system:

- **Debug Logs**: Tool discovery, cache hits, request details
- **Info Logs**: Successful customer lookups, service initialization
- **Error Logs**: Connection failures, tool execution errors
- **Metrics**: Customer lookup success rates, response times

## Performance Considerations

- **Caching**: Customer data is cached by ANI to reduce server load
- **Timeouts**: Configurable timeouts prevent hanging requests
- **Retries**: Automatic retry logic for transient failures
- **Connection Pooling**: HTTP client reuses connections efficiently

## Security

- **No Sensitive Data Logging**: Customer information is not logged in detail
- **Secure Transport**: HTTPS support for production environments
- **Error Sanitization**: Error messages don't expose internal details

## Future Enhancements

Potential improvements for the MCP service:

1. **Advanced Caching**: TTL-based cache expiration
2. **Load Balancing**: Support for multiple MCP server instances
3. **Metrics Dashboard**: Real-time monitoring of customer lookup performance
4. **A/B Testing**: Support for different customer lookup strategies
