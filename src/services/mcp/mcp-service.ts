import { MCPClient } from './mcp-client';
import { logDebug, logError, logInfo, logWarn } from '../logging/logger';
import {
  CustomerInfo,
  CustomerLookupResult,
  MCPServiceConfig,
  MCPTool,
  MCPToolCallRequest,
} from './types';

/**
 * MCP Service for Customer Identification
 * 
 * Provides customer identification and data retrieval using the MCP server
 */
export class MCPService {
  private client: MCPClient;
  private config: MCPServiceConfig;
  private availableTools: MCPTool[] = [];
  private isInitialized: boolean = false;
  private customerCache: Map<string, CustomerInfo> = new Map();

  constructor(config?: Partial<MCPServiceConfig>) {
    this.config = {
      baseUrl: process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274',
      timeout: parseInt(process.env.MCP_TIMEOUT || '5000'),
      retryAttempts: parseInt(process.env.MCP_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.MCP_RETRY_DELAY || '1000'),
      ...config,
    };

    this.client = new MCPClient(this.config);
  }

  /**
   * Initialize the MCP service
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logInfo('[MCP] Initializing MCP service...');
      
      // Test connection
      const isConnected = await this.client.testConnection();
      if (!isConnected) {
        throw new Error('Failed to connect to MCP server');
      }

      // Get server info
      const serverInfo = await this.client.getServerInfo();
      if (serverInfo) {
        logDebug('[MCP] Server info:', serverInfo);
      }

      // List available tools
      this.availableTools = await this.client.listTools();
      logInfo(`[MCP] Found ${this.availableTools.length} available tools`);
      
      this.availableTools.forEach(tool => {
        logDebug(`[MCP] Available tool: ${tool.name} - ${tool.description}`);
      });

      this.isInitialized = true;
      logInfo('[MCP] Service initialized successfully');
    } catch (error) {
      logError('[MCP] Failed to initialize service', error);
      throw error;
    }
  }

  /**
   * Check if the service is available
   */
  isAvailable(): boolean {
    return this.isInitialized;
  }

  /**
   * Get available tools
   */
  getAvailableTools(): MCPTool[] {
    return [...this.availableTools];
  }

  /**
   * Look up customer information by ANI (phone number)
   */
  async lookupCustomer(ani: string): Promise<CustomerLookupResult> {
    if (!ani) {
      return {
        success: false,
        error: 'ANI is required for customer lookup',
        source: 'fallback',
      };
    }

    // Check cache first
    const cachedCustomer = this.customerCache.get(ani);
    if (cachedCustomer) {
      logDebug(`[MCP] Customer found in cache for ANI: ${ani}`);
      return {
        success: true,
        customer: cachedCustomer,
        source: 'cache',
      };
    }

    if (!this.isInitialized) {
      logWarn('[MCP] Service not initialized, attempting to initialize...');
      try {
        await this.initialize();
      } catch (error) {
        return {
          success: false,
          error: 'MCP service unavailable',
          source: 'fallback',
        };
      }
    }

    try {
      // Find customer lookup tool
      const customerTool = this.findCustomerLookupTool();
      if (!customerTool) {
        logWarn('[MCP] No customer lookup tool available');
        return {
          success: false,
          error: 'Customer lookup tool not available',
          source: 'fallback',
        };
      }

      // Call the customer lookup tool
      const toolCall: MCPToolCallRequest = {
        name: customerTool.name,
        arguments: { ani },
      };

      logDebug(`[MCP] Looking up customer for ANI: ${ani}`);
      const result = await this.client.callTool(toolCall);

      if (result.isError) {
        throw new Error('Tool call returned error');
      }

      // Parse customer information from tool response
      const customer = this.parseCustomerInfo(result, ani);
      
      // Cache the result
      this.customerCache.set(ani, customer);
      
      logInfo(`[MCP] Customer lookup successful for ANI: ${ani}`);
      return {
        success: true,
        customer,
        source: 'mcp',
      };
    } catch (error) {
      logError(`[MCP] Customer lookup failed for ANI: ${ani}`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        source: 'fallback',
      };
    }
  }

  /**
   * Find the appropriate customer lookup tool
   */
  private findCustomerLookupTool(): MCPTool | null {
    // Look for common customer lookup tool names
    const toolNames = ['lookupCustomer', 'getCustomer', 'findCustomer', 'customerLookup'];
    
    for (const toolName of toolNames) {
      const tool = this.availableTools.find(t => 
        t.name.toLowerCase().includes(toolName.toLowerCase())
      );
      if (tool) {
        return tool;
      }
    }

    // If no specific tool found, return the first tool that might be related
    return this.availableTools.find(t => 
      t.description.toLowerCase().includes('customer') ||
      t.description.toLowerCase().includes('lookup') ||
      t.description.toLowerCase().includes('ani')
    ) || null;
  }

  /**
   * Parse customer information from MCP tool response
   */
  private parseCustomerInfo(result: any, ani: string): CustomerInfo {
    const customer: CustomerInfo = { ani };

    try {
      // Handle different response formats
      if (result.content && Array.isArray(result.content)) {
        for (const content of result.content) {
          if (content.type === 'text' && content.text) {
            // Try to parse JSON from text content
            try {
              const parsed = JSON.parse(content.text);
              Object.assign(customer, parsed);
            } catch {
              // If not JSON, treat as customer name or notes
              if (!customer.customerName) {
                customer.customerName = content.text;
              }
            }
          } else if (content.data) {
            // Direct data object
            Object.assign(customer, content.data);
          }
        }
      } else if (typeof result === 'object') {
        // Direct object response
        Object.assign(customer, result);
      }
    } catch (error) {
      logWarn('[MCP] Failed to parse customer info, using minimal data', error);
    }

    return customer;
  }

  /**
   * Clear customer cache
   */
  clearCache(): void {
    this.customerCache.clear();
    logDebug('[MCP] Customer cache cleared');
  }

  /**
   * Get cached customer count
   */
  getCacheSize(): number {
    return this.customerCache.size;
  }

  /**
   * Dispose of the service
   */
  async dispose(): Promise<void> {
    this.clearCache();
    this.isInitialized = false;
    logInfo('[MCP] Service disposed');
  }
}
