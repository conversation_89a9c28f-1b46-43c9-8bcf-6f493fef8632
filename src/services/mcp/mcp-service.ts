// MCPService: Foundation for MCP service logic

import { json } from 'stream/consumers';
import { logError } from '../logging';
import { MCPClient } from './mcp-client';
import { ToolResult } from './types';

export class MCPService {
  public client: MCPClient;

  constructor(client: MC<PERSON>lient) {
    this.client = client;
  }

  /**
   * Initialize the MCP service (stub for now).
   */
  async initialize(): Promise<void> {
    // Initialization logic (if any) goes here
    return;
  }

  /**
   * Check if the MCP service is available (stub for now).
   */
  async isAvailable(): Promise<boolean> {
    try {
      return await this.client.testConnection();
    } catch (err: any) {
      logError(`[MCPService] MCP availability check failed: ${err?.message || err}`);
      return false;
    }
  }

  /**
   * Safely get the current date/time using the MCP getDateTime tool.
   * Returns null if the MCP server is unavailable or an error occurs.
   */
  async safeGetDateTime(): Promise<{ time: string } | null> {
    try {
      return await this.client.callTool('getDateTime', { format: 'ISO' });
    } catch (err) {
      logError(
        `[MCPService] Error in safeGetDateTime: ${
          err && typeof err === 'object' && 'message' in err
            ? (err as Error).message
            : JSON.stringify(err)
        }`
      );
      return null;
    }
  }
  /**
   * Invoke any MCP tool by name and arguments, with type-safe result.
   * Returns the tool result or null if an error occurs.
   */
  async invokeTool<T = ToolResult>(tool: string, args: Record<string, unknown>): Promise<T | null> {
    try {
      return await this.client.callTool(tool, args);
    } catch (err) {
      logError(
        `[MCPService] Error while trying to call tool "${tool}": ${
          err && typeof err === 'object' && 'message' in err
            ? (err as Error).message
            : JSON.stringify(err)
        }`
      );
      return null;
    }
  }

  /**
   * Graceful disposal of MCPService resources (stub for now).
   * Extend as needed for real resource cleanup.
   */
  async dispose(): Promise<void> {
    // If MCPClient or other resources need cleanup, do it here.
    // For now, nothing to dispose.
  }
}

/**
 * Factory to create MCPService from environment variables.
 * Reads MCP_SERVER_URL (preferred) and MCP_TIMEOUT (optional, ms) from process.env.
 * Provides sensible defaults for local and production environments.
 * Supports optional MCP_RETRY_COUNT for future retry logic.
 * Throws if no URL is found.
 */
export function createMCPServiceFromEnv(): MCPService {
  // Prefer MCP_SERVER_URL, fallback to MCP_URL for backward compatibility
  const url = process.env.MCP_SERVER_URL;

  if (!url) {
    throw new Error('MCP_SERVER_URL environment variable is required for MCPService');
  }

  const timeout = process.env.MCP_TIMEOUT ? Number(process.env.MCP_TIMEOUT) : undefined;
  // Optional: support for retry count (not yet implemented in client)
  const retryCount = process.env.MCP_RETRY_COUNT ? Number(process.env.MCP_RETRY_COUNT) : undefined;

  // MCPClientConfig can be extended to support retryCount in the future
  const client = new MCPClient({ url, timeout });
  // Optionally attach retryCount to client if/when supported

  return new MCPService(client);
}
