/**
 * MCP Client - Official SDK Implementation
 *
 * This is now a wrapper around the official MCP SDK for proper protocol compliance
 * Maintains the same interface as the original implementation for backward compatibility
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import type { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import type { Tool, CallToolResult, ListToolsResult } from '@modelcontextprotocol/sdk/types.js';
import { logDebug, logError, logInfo } from '../logging/logger';
import type { MCPToolMetadata } from './types';

/**
 * Configuration for the MCP client (backward compatibility)
 */
export interface MCPClientConfig {
  url: string;
  timeout?: number;
}

/**
 * MCP client for communicating with the MCP server using official SDK
 */
export class MCPClient {
  private client: Client;
  private transport: Transport | null = null;
  public config: MCPClientConfig;
  private connected = false;

  constructor(config: MCPClientConfig) {
    this.config = config;

    this.client = new Client(
      {
        name: 'genesys-voice-connector',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    logDebug(`[MCP SDK] Client created with config: ${JSON.stringify(this.config)}`);
  }

  /**
   * Connect to the MCP server using official SDK
   */
  private async ensureConnected(): Promise<void> {
    if (this.connected) {
      return;
    }

    try {
      logInfo(`[MCP SDK] Connecting to MCP server at ${this.config.url}`);

      this.transport = new StreamableHTTPClientTransport(new URL(this.config.url));
      await this.client.connect(this.transport);
      this.connected = true;

      logInfo('[MCP SDK] Successfully connected to MCP server');
    } catch (error) {
      logError(
        `[MCP SDK] Failed to connect to MCP server: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Test connection to the MCP server using SDK
   */
  async testConnection(): Promise<boolean> {
    logInfo('[MCPClient] Testing connection to MCP server...');
    try {
      await this.listTools();
      logInfo('[MCPClient] MCP server connection test succeeded.');
      return true;
    } catch (err: any) {
      logError(`[MCPClient] MCP server connection test failed: ${err?.message || err}`);
      return false;
    }
  }

  /**
   * List available tools from the MCP server using SDK
   */
  async listTools(): Promise<MCPToolMetadata[]> {
    await this.ensureConnected();

    try {
      logDebug('[MCP SDK] Listing available tools');
      const result: ListToolsResult = await this.client.listTools();
      logDebug(`[MCP SDK] Found ${result.tools.length} tools`);

      // Convert SDK Tool[] to MCPToolMetadata[] for backward compatibility
      const tools: MCPToolMetadata[] = result.tools.map(tool => ({
        name: tool.name,
        description: tool.description || '',
        arguments: this.convertInputSchemaToArguments(tool.inputSchema),
      }));

      return tools;
    } catch (error) {
      logError(
        `[MCP SDK] Failed to list tools: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Convert SDK inputSchema to MCPToolMetadata arguments format
   */
  private convertInputSchemaToArguments(
    inputSchema: any
  ): Record<string, { type: string; required?: boolean; description?: string }> {
    const args: Record<string, { type: string; required?: boolean; description?: string }> = {};

    if (inputSchema?.properties) {
      for (const [key, value] of Object.entries(inputSchema.properties)) {
        const prop = value as any;
        args[key] = {
          type: prop.type || 'string',
          required: inputSchema.required?.includes(key) || false,
          description: prop.description,
        };
      }
    }

    return args;
  }

  /**
   * Call a tool on the MCP server.
   */
  async callTool(tool: string, args: Record<string, unknown>): Promise<any> {
    const requestId = Date.now();
    const start = Date.now();
    logInfo(`[MCPClient] callTool request: ${JSON.stringify({ tool, args, requestId })}`);
    try {
      const response = await this._post({
        jsonrpc: '2.0',
        method: 'callTool',
        params: { tool, arguments: args },
        id: requestId,
      });
      const duration = Date.now() - start;
      if (!response.result || typeof response.result !== 'object') {
        logError(
          `[MCPClient] callTool invalid response: ${JSON.stringify({ tool, requestId, response })}`
        );
        throw new MCPMalformedResponseError('Invalid MCP response');
      }
      logInfo(`[MCPClient] callTool success: ${JSON.stringify({ tool, requestId, duration })}`);
      return response.result;
    } catch (err: any) {
      const duration = Date.now() - start;
      logError(
        `[MCPClient] callTool failed: ${JSON.stringify({
          tool,
          requestId,
          duration,
          error: err?.message || err,
        })}`
      );
      throw err;
    }
  }

  /**
   * Internal method to send a JSON-RPC request.
   * This is stubbed for testing and should be implemented with real HTTP logic.
   */
  async _post(payload: Record<string, unknown>): Promise<any> {
    const url = this.config.url;
    const timeout = this.config.timeout ?? 2000;
    const maxRetries = 1; // One retry for transient errors
    let attempt = 0;
    let lastError: any = null;

    // For observability
    const start = Date.now();
    const requestId =
      payload && typeof payload === 'object' && 'id' in payload ? payload['id'] : Date.now();

    while (attempt <= maxRetries) {
      attempt++;
      const controller = new AbortController();
      const timer = setTimeout(() => controller.abort(), timeout);

      try {
        logInfo(
          `[MCPClient] _post request: ${JSON.stringify({ url, payload, requestId, attempt })}`
        );
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
        });
        clearTimeout(timer);

        const duration = Date.now() - start;
        logDebug(
          `[MCPClient] _post response received: ${JSON.stringify({
            status: response.status,
            duration,
            requestId,
          })}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          logError(
            `[MCPClient] HTTP error from MCP server: ${JSON.stringify({
              status: response.status,
              errorText,
              requestId,
            })}`
          );
          throw new MCPHTTPError(`HTTP ${response.status}`, response.status, errorText);
        }

        let json: any;
        try {
          json = await response.json();
        } catch (e) {
          logError(
            `[MCPClient] Malformed JSON response from MCP server: ${JSON.stringify({ requestId })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON response from MCP server', e);
        }

        // JSON-RPC 2.0: must have id, and either result or error
        if (typeof json !== 'object' || !('id' in json)) {
          logError(
            `[MCPClient] Malformed JSON-RPC response: missing id: ${JSON.stringify({
              json,
              requestId,
            })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON-RPC response: missing id');
        }
        if (json.id !== requestId) {
          logError(
            `[MCPClient] Mismatched JSON-RPC id in response: ${JSON.stringify({
              expected: requestId,
              actual: json.id,
            })}`
          );
          throw new MCPMalformedResponseError('Mismatched JSON-RPC id in response');
        }
        if ('error' in json && json.error) {
          logError(
            `[MCPClient] JSON-RPC error from MCP server: ${JSON.stringify({
              error: json.error,
              requestId,
            })}`
          );
          throw new MCPJSONRPCError(
            json.error.message || 'JSON-RPC error',
            json.error.code,
            json.error.data
          );
        }
        if (!('result' in json)) {
          logError(
            `[MCPClient] Malformed JSON-RPC response: missing result: ${JSON.stringify({
              json,
              requestId,
            })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON-RPC response: missing result');
        }

        logInfo(`[MCPClient] _post success: ${JSON.stringify({ duration, requestId })}`);
        return json;
      } catch (err: any) {
        clearTimeout(timer);
        lastError = err;
        // Retry only on transient network errors or timeouts
        if (
          attempt <= maxRetries &&
          (err.name === 'AbortError' ||
            err.message?.includes('network') ||
            err.message?.includes('timeout'))
        ) {
          logError(
            `[MCPClient] Transient error, retrying _post: ${JSON.stringify({
              attempt,
              error: err.message,
              requestId,
            })}`
          );
          continue;
        }
        logError(`[MCPClient] _post failed: ${JSON.stringify({ error: err.message, requestId })}`);
        // If it's a known error, rethrow as is; otherwise, wrap as network error
        if (
          err instanceof MCPHTTPError ||
          err instanceof MCPTimeoutError ||
          err instanceof MCPNetworkError ||
          err instanceof MCPJSONRPCError ||
          err instanceof MCPMalformedResponseError
        ) {
          throw err;
        }
        throw new MCPNetworkError('Unknown network error', err);
      }
    }
    throw lastError || new MCPNetworkError('Unknown MCPClient _post error');
  }
}
