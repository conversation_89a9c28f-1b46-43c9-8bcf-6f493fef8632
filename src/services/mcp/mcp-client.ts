// MCPClient: Foundation for MCP protocol client
import type { MCPToolMetadata } from './types';
import { logInfo, logDebug, logError } from '../logging/logger';
import {
  MCPNetworkError,
  MCPTimeoutError,
  MCPHTTPError,
  MCPJSONRPCError,
  MCPMalformedResponseError,
} from './errors';

export interface MCPClientConfig {
  url: string;
  timeout?: number;
}

export class MCPClient {
  public config: MCPClientConfig;

  constructor(config: MCPClientConfig) {
    this.config = config;
  }

  /**
   * Test connection to the MCP server.
   * For now, this is a stub that always returns true.
   * In production, this should perform an actual HTTP request.
   */
  async testConnection(): Promise<boolean> {
    logInfo('[MCPClient] Testing connection to MCP server...');
    try {
      await this.listTools();
      logInfo('[MCPClient] MCP server connection test succeeded.');
      return true;
    } catch (err: any) {
      logError(`[MCPClient] MCP server connection test failed: ${err?.message || err}`);
      return false;
    }
  }

  /**
   * List available tools from the MCP server.
   */
  async listTools(): Promise<MCPToolMetadata[]> {
    const requestId = Date.now();
    const start = Date.now();
    logInfo(`[MCPClient] listTools request: ${JSON.stringify({ requestId })}`);
    try {
      const response = await this._post({
        jsonrpc: '2.0',
        method: 'listTools',
        params: {},
        id: requestId,
      });
      const duration = Date.now() - start;
      if (!response.result || !Array.isArray(response.result)) {
        logError(
          `[MCPClient] listTools invalid response: ${JSON.stringify({ requestId, response })}`
        );
        throw new MCPMalformedResponseError('Invalid MCP response');
      }
      logInfo(
        `[MCPClient] listTools success: ${JSON.stringify({
          requestId,
          duration,
          toolCount: response.result.length,
        })}`
      );
      // Optionally: validate/transform response.result to MCPToolMetadata[]
      return response.result as MCPToolMetadata[];
    } catch (err: any) {
      const duration = Date.now() - start;
      logError(
        `[MCPClient] listTools failed: ${JSON.stringify({
          requestId,
          duration,
          error: err?.message || err,
        })}`
      );
      throw err;
    }
  }

  /**
   * Call a tool on the MCP server.
   */
  async callTool(tool: string, args: Record<string, unknown>): Promise<any> {
    const requestId = Date.now();
    const start = Date.now();
    logInfo(`[MCPClient] callTool request: ${JSON.stringify({ tool, args, requestId })}`);
    try {
      const response = await this._post({
        jsonrpc: '2.0',
        method: 'callTool',
        params: { tool, arguments: args },
        id: requestId,
      });
      const duration = Date.now() - start;
      if (!response.result || typeof response.result !== 'object') {
        logError(
          `[MCPClient] callTool invalid response: ${JSON.stringify({ tool, requestId, response })}`
        );
        throw new MCPMalformedResponseError('Invalid MCP response');
      }
      logInfo(`[MCPClient] callTool success: ${JSON.stringify({ tool, requestId, duration })}`);
      return response.result;
    } catch (err: any) {
      const duration = Date.now() - start;
      logError(
        `[MCPClient] callTool failed: ${JSON.stringify({
          tool,
          requestId,
          duration,
          error: err?.message || err,
        })}`
      );
      throw err;
    }
  }

  /**
   * Internal method to send a JSON-RPC request.
   * This is stubbed for testing and should be implemented with real HTTP logic.
   */
  async _post(payload: Record<string, unknown>): Promise<any> {
    const url = this.config.url;
    const timeout = this.config.timeout ?? 2000;
    const maxRetries = 1; // One retry for transient errors
    let attempt = 0;
    let lastError: any = null;

    // For observability
    const start = Date.now();
    const requestId =
      payload && typeof payload === 'object' && 'id' in payload ? payload['id'] : Date.now();

    while (attempt <= maxRetries) {
      attempt++;
      const controller = new AbortController();
      const timer = setTimeout(() => controller.abort(), timeout);

      try {
        logInfo(
          `[MCPClient] _post request: ${JSON.stringify({ url, payload, requestId, attempt })}`
        );
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
          body: JSON.stringify(payload),
          signal: controller.signal,
        });
        clearTimeout(timer);

        const duration = Date.now() - start;
        logDebug(
          `[MCPClient] _post response received: ${JSON.stringify({
            status: response.status,
            duration,
            requestId,
          })}`
        );

        if (!response.ok) {
          const errorText = await response.text();
          logError(
            `[MCPClient] HTTP error from MCP server: ${JSON.stringify({
              status: response.status,
              errorText,
              requestId,
            })}`
          );
          throw new MCPHTTPError(`HTTP ${response.status}`, response.status, errorText);
        }

        let json: any;
        try {
          json = await response.json();
        } catch (e) {
          logError(
            `[MCPClient] Malformed JSON response from MCP server: ${JSON.stringify({ requestId })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON response from MCP server', e);
        }

        // JSON-RPC 2.0: must have id, and either result or error
        if (typeof json !== 'object' || !('id' in json)) {
          logError(
            `[MCPClient] Malformed JSON-RPC response: missing id: ${JSON.stringify({
              json,
              requestId,
            })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON-RPC response: missing id');
        }
        if (json.id !== requestId) {
          logError(
            `[MCPClient] Mismatched JSON-RPC id in response: ${JSON.stringify({
              expected: requestId,
              actual: json.id,
            })}`
          );
          throw new MCPMalformedResponseError('Mismatched JSON-RPC id in response');
        }
        if ('error' in json && json.error) {
          logError(
            `[MCPClient] JSON-RPC error from MCP server: ${JSON.stringify({
              error: json.error,
              requestId,
            })}`
          );
          throw new MCPJSONRPCError(
            json.error.message || 'JSON-RPC error',
            json.error.code,
            json.error.data
          );
        }
        if (!('result' in json)) {
          logError(
            `[MCPClient] Malformed JSON-RPC response: missing result: ${JSON.stringify({
              json,
              requestId,
            })}`
          );
          throw new MCPMalformedResponseError('Malformed JSON-RPC response: missing result');
        }

        logInfo(`[MCPClient] _post success: ${JSON.stringify({ duration, requestId })}`);
        return json;
      } catch (err: any) {
        clearTimeout(timer);
        lastError = err;
        // Retry only on transient network errors or timeouts
        if (
          attempt <= maxRetries &&
          (err.name === 'AbortError' ||
            err.message?.includes('network') ||
            err.message?.includes('timeout'))
        ) {
          logError(
            `[MCPClient] Transient error, retrying _post: ${JSON.stringify({
              attempt,
              error: err.message,
              requestId,
            })}`
          );
          continue;
        }
        logError(`[MCPClient] _post failed: ${JSON.stringify({ error: err.message, requestId })}`);
        // If it's a known error, rethrow as is; otherwise, wrap as network error
        if (
          err instanceof MCPHTTPError ||
          err instanceof MCPTimeoutError ||
          err instanceof MCPNetworkError ||
          err instanceof MCPJSONRPCError ||
          err instanceof MCPMalformedResponseError
        ) {
          throw err;
        }
        throw new MCPNetworkError('Unknown network error', err);
      }
    }
    throw lastError || new MCPNetworkError('Unknown MCPClient _post error');
  }
}
