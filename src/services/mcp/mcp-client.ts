import { createExternalHttpClient } from '../../utils/http-client';
import { logDebug, logError, logInfo } from '../logging/logger';
import {
  MCPRequest,
  MCPResponse,
  MCPTool,
  MCPToolsListResponse,
  MCPToolCallRequest,
  MCPToolCallResponse,
  MCPServiceConfig,
} from './types';

/**
 * MCP (Model Context Protocol) HTTP Client
 * 
 * Handles communication with the MCP server following the MCP protocol specification
 */
export class MCPClient {
  private config: MCPServiceConfig;
  private requestId: number = 1;

  constructor(config: MCPServiceConfig) {
    this.config = config;
  }

  /**
   * Generate a unique request ID for MCP requests
   */
  private getNextRequestId(): number {
    return this.requestId++;
  }

  /**
   * Make a generic MCP request
   */
  private async makeRequest<T = any>(method: string, params?: any): Promise<T> {
    const client = createExternalHttpClient(this.config.baseUrl);
    
    const request: MCPRequest = {
      jsonrpc: '2.0',
      id: this.getNextRequestId(),
      method,
      params,
    };

    logDebug(`[MCP] Making request: ${method}`, { params });

    try {
      const response = await client.post('/mcp', request, {
        timeout: this.config.timeout || 5000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const mcpResponse: MCPResponse = response.data;

      if (mcpResponse.error) {
        throw new Error(`MCP Error: ${mcpResponse.error.message} (Code: ${mcpResponse.error.code})`);
      }

      logDebug(`[MCP] Request successful: ${method}`);
      return mcpResponse.result;
    } catch (error) {
      logError(`[MCP] Request failed: ${method}`, error);
      throw error;
    }
  }

  /**
   * List available tools from the MCP server
   */
  async listTools(): Promise<MCPTool[]> {
    try {
      const result: MCPToolsListResponse = await this.makeRequest('tools/list');
      return result.tools || [];
    } catch (error) {
      logError('[MCP] Failed to list tools', error);
      throw error;
    }
  }

  /**
   * Call a specific tool on the MCP server
   */
  async callTool(toolCall: MCPToolCallRequest): Promise<MCPToolCallResponse> {
    try {
      const result: MCPToolCallResponse = await this.makeRequest('tools/call', toolCall);
      return result;
    } catch (error) {
      logError(`[MCP] Failed to call tool: ${toolCall.name}`, error);
      throw error;
    }
  }

  /**
   * Test connection to MCP server
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.listTools();
      logInfo('[MCP] Connection test successful');
      return true;
    } catch (error) {
      logError('[MCP] Connection test failed', error);
      return false;
    }
  }

  /**
   * Get server information (if supported)
   */
  async getServerInfo(): Promise<any> {
    try {
      return await this.makeRequest('initialize', {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
        },
        clientInfo: {
          name: 'genesys-voice-connector',
          version: '1.0.0',
        },
      });
    } catch (error) {
      logDebug('[MCP] Server info not available', error);
      return null;
    }
  }
}
