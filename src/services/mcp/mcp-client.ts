/**
 * MCP Client - Official SDK Implementation
 *
 * This is now a wrapper around the official MCP SDK for proper protocol compliance
 * Maintains the same interface as the original implementation for backward compatibility
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StreamableHTTPClientTransport } from '@modelcontextprotocol/sdk/client/streamableHttp.js';
import type { Transport } from '@modelcontextprotocol/sdk/shared/transport.js';
import type { Tool, CallToolResult, ListToolsResult } from '@modelcontextprotocol/sdk/types.js';
import { logDebug, logError, logInfo } from '../logging/logger';
import type { MCPToolMetadata } from './types';

/**
 * Configuration for the MCP client (backward compatibility)
 */
export interface MCPClientConfig {
  url: string;
  timeout?: number;
}

/**
 * MCP client for communicating with the MCP server using official SDK
 */
export class MCPClient {
  private client: Client;
  private transport: Transport | null = null;
  public config: MCPClientConfig;
  private connected = false;

  constructor(config: MCPClientConfig) {
    this.config = config;

    this.client = new Client(
      {
        name: 'genesys-voice-connector',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    logDebug(`[MCP SDK] Client created with config: ${JSON.stringify(this.config)}`);
  }

  /**
   * Connect to the MCP server using official SDK
   */
  private async ensureConnected(): Promise<void> {
    if (this.connected) {
      return;
    }

    try {
      logInfo(`[MCP SDK] Connecting to MCP server at ${this.config.url}`);

      this.transport = new StreamableHTTPClientTransport(new URL(this.config.url));
      await this.client.connect(this.transport);
      this.connected = true;

      logInfo('[MCP SDK] Successfully connected to MCP server');
    } catch (error) {
      logError(
        `[MCP SDK] Failed to connect to MCP server: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Test connection to the MCP server using SDK
   */
  async testConnection(): Promise<boolean> {
    logInfo('[MCPClient] Testing connection to MCP server...');
    try {
      await this.listTools();
      logInfo('[MCPClient] MCP server connection test succeeded.');
      return true;
    } catch (err: any) {
      logError(`[MCPClient] MCP server connection test failed: ${err?.message || err}`);
      return false;
    }
  }

  /**
   * List available tools from the MCP server using SDK
   */
  async listTools(): Promise<MCPToolMetadata[]> {
    await this.ensureConnected();

    try {
      logDebug('[MCP SDK] Listing available tools');
      const result: ListToolsResult = await this.client.listTools();
      logDebug(`[MCP SDK] Found ${result.tools.length} tools`);

      // Convert SDK Tool[] to MCPToolMetadata[] for backward compatibility
      const tools: MCPToolMetadata[] = result.tools.map(tool => ({
        name: tool.name,
        description: tool.description || '',
        arguments: this.convertInputSchemaToArguments(tool.inputSchema),
      }));

      return tools;
    } catch (error) {
      logError(
        `[MCP SDK] Failed to list tools: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Convert SDK inputSchema to MCPToolMetadata arguments format
   */
  private convertInputSchemaToArguments(
    inputSchema: any
  ): Record<string, { type: string; required?: boolean; description?: string }> {
    const args: Record<string, { type: string; required?: boolean; description?: string }> = {};

    if (inputSchema?.properties) {
      for (const [key, value] of Object.entries(inputSchema.properties)) {
        const prop = value as any;
        args[key] = {
          type: prop.type || 'string',
          required: inputSchema.required?.includes(key) || false,
          description: prop.description,
        };
      }
    }

    return args;
  }

  /**
   * Call a tool on the MCP server using SDK
   */
  async callTool(tool: string, args: Record<string, unknown>): Promise<any> {
    await this.ensureConnected();

    try {
      logDebug(`[MCP SDK] Calling tool: ${tool} with arguments: ${JSON.stringify(args)}`);

      // Debug: Log the exact request being sent
      const request = {
        name: tool,
        arguments: args,
      };
      logDebug(`[MCP SDK] Request object: ${JSON.stringify(request, null, 2)}`);

      const result = await this.client.callTool(request);
      logDebug(`[MCP SDK] Tool call successful: ${tool}`);
      logDebug(`[MCP SDK] Raw result: ${JSON.stringify(result, null, 2)}`);

      // For backward compatibility, extract the result from the SDK response
      const callResult = result as CallToolResult;
      if (
        callResult.content &&
        Array.isArray(callResult.content) &&
        callResult.content.length > 0
      ) {
        const firstContent = callResult.content[0];
        if (firstContent.type === 'text' && 'text' in firstContent && firstContent.text) {
          try {
            // Try to parse as JSON first
            return JSON.parse(firstContent.text);
          } catch {
            // If not JSON, return the text directly
            return { result: firstContent.text };
          }
        }
        // Return the content directly if not text
        return firstContent;
      }

      // Return the full result if no content
      return callResult;
    } catch (error) {
      logError(
        `[MCP SDK] Tool call failed: ${tool} - ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (!this.connected) {
      return;
    }

    try {
      await this.client.close();
      this.connected = false;
      this.transport = null;
      logInfo('[MCP SDK] Disconnected from MCP server');
    } catch (error) {
      logError(
        `[MCP SDK] Error during disconnect: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
      throw error;
    }
  }

  /**
   * Check if connected to MCP server
   */
  isConnected(): boolean {
    return this.connected;
  }

  /**
   * Get the underlying SDK client (for advanced usage)
   */
  getClient(): Client {
    return this.client;
  }
}
