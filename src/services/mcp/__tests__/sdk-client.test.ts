/**
 * Phase 1 Step 1.2: TDD Test for SDK Client Wrapper
 * 
 * This test verifies that the SDK client wrapper can be created and configured
 */

import { SDKClient } from '../sdk-client';

describe('MCP SDK Client Wrapper', () => {
  describe('instantiation', () => {
    it('should create SDK client with configuration', () => {
      // This test will fail initially until we create the SDKClient class
      const config = {
        baseUrl: 'http://127.0.0.1:6274',
        timeout: 5000,
      };

      expect(() => {
        const client = new SDKClient(config);
        expect(client).toBeDefined();
        expect(client).toBeInstanceOf(SDKClient);
      }).not.toThrow();
    });

    it('should use default configuration when none provided', () => {
      expect(() => {
        const client = new SDKClient();
        expect(client).toBeDefined();
      }).not.toThrow();
    });
  });

  describe('connection', () => {
    let client: SDKClient;

    beforeEach(() => {
      client = new SDKClient({
        baseUrl: 'http://127.0.0.1:6274',
        timeout: 5000,
      });
    });

    it('should have connect method', () => {
      expect(client.connect).toBeDefined();
      expect(typeof client.connect).toBe('function');
    });

    it('should have disconnect method', () => {
      expect(client.disconnect).toBeDefined();
      expect(typeof client.disconnect).toBe('function');
    });

    it('should have isConnected method', () => {
      expect(client.isConnected).toBeDefined();
      expect(typeof client.isConnected).toBe('function');
    });
  });

  describe('tool operations', () => {
    let client: SDKClient;

    beforeEach(() => {
      client = new SDKClient({
        baseUrl: 'http://127.0.0.1:6274',
        timeout: 5000,
      });
    });

    it('should have listTools method', () => {
      expect(client.listTools).toBeDefined();
      expect(typeof client.listTools).toBe('function');
    });

    it('should have callTool method', () => {
      expect(client.callTool).toBeDefined();
      expect(typeof client.callTool).toBe('function');
    });
  });
});
