/**
 * Phase 1 Step 1.3: TDD Test for Updated Types with SDK
 *
 * This test verifies that the types are compatible with the official SDK
 */

import type { Tool, CallToolResult, ListToolsResult } from '@modelcontextprotocol/sdk/types.js';
import { CustomerInfo, CustomerLookupResult } from '../types';

describe('MCP Types SDK Compatibility', () => {
  describe('SDK types import', () => {
    it('should import Tool type from SDK', () => {
      // This test will fail initially until we update types.ts
      const mockTool: Tool = {
        name: 'test-tool',
        description: 'A test tool',
        inputSchema: {
          type: 'object',
          properties: {
            input: { type: 'string' }
          }
        }
      };

      expect(mockTool.name).toBe('test-tool');
      expect(mockTool.description).toBe('A test tool');
      expect(mockTool.inputSchema.type).toBe('object');
    });

    it('should import CallToolResult type from SDK', () => {
      const mockResult: CallToolResult = {
        content: [
          {
            type: 'text',
            text: 'Test result'
          }
        ]
      };

      expect(mockResult.content).toHaveLength(1);
      expect(mockResult.content[0].type).toBe('text');
    });

    it('should import ListToolsResult type from SDK', () => {
      const mockListResult: ListToolsResult = {
        tools: [
          {
            name: 'test-tool',
            description: 'A test tool',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          }
        ]
      };

      expect(mockListResult.tools).toHaveLength(1);
      expect(mockListResult.tools[0].name).toBe('test-tool');
    });
  });

  describe('existing custom types', () => {
    it('should preserve CustomerInfo interface', () => {
      const customerInfo: CustomerInfo = {
        ani: '**********',
        customerId: 'cust-123',
        customerName: 'John Doe',
        customerType: 'premium',
        accountStatus: 'active'
      };

      expect(customerInfo.ani).toBe('**********');
      expect(customerInfo.customerId).toBe('cust-123');
      expect(customerInfo.customerName).toBe('John Doe');
    });

    it('should preserve CustomerLookupResult interface', () => {
      const lookupResult: CustomerLookupResult = {
        success: true,
        customer: {
          ani: '**********',
          customerName: 'John Doe'
        },
        source: 'mcp'
      };

      expect(lookupResult.success).toBe(true);
      expect(lookupResult.customer?.ani).toBe('**********');
      expect(lookupResult.source).toBe('mcp');
    });
  });

  describe('type compatibility', () => {
    it('should allow SDK types to be used with custom interfaces', () => {
      // Test that SDK types work with our custom result handling
      const sdkResult: CallToolResult = {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              ani: '**********',
              customerName: 'John Doe'
            })
          }
        ]
      };

      const textContent = sdkResult.content[0];
      const customerData = textContent.type === 'text' && textContent.text
        ? JSON.parse(textContent.text)
        : {};

      const customerLookupResult: CustomerLookupResult = {
        success: true,
        customer: customerData,
        source: 'mcp'
      };

      expect(customerLookupResult.success).toBe(true);
      expect(customerLookupResult.customer?.ani).toBe('**********');
    });
  });
});
