/**
 * Updated tests for SDK-based MCPClient
 */

import { MCPClient } from '../mcp-client';

// Mock the SDK for testing
jest.mock('@modelcontextprotocol/sdk/client/index.js', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    listTools: jest.fn().mockResolvedValue({
      tools: [
        {
          name: 'getDateTime',
          description: 'Get current date and time',
          inputSchema: {
            type: 'object',
            properties: {
              format: { type: 'string' },
            },
          },
        },
      ],
    }),
    callTool: jest.fn().mockResolvedValue({
      content: [
        {
          type: 'text',
          text: JSON.stringify({ time: '2025-06-04T09:00:00Z' }),
        },
      ],
    }),
  })),
}));

jest.mock('@modelcontextprotocol/sdk/client/streamableHttp.js', () => ({
  StreamableHTTPClientTransport: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    send: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('MCPClient (SDK-based)', () => {
  it('should instantiate with configuration', () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    expect(client).toBeInstanceOf(MCPClient);
    expect(client.config).toEqual(config);
  });

  it('should have a connection test method that returns a boolean', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    const result = await client.testConnection();
    expect(typeof result).toBe('boolean');
  });

  it('should list available tools from the MCP server using SDK', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    const tools = await client.listTools();
    expect(Array.isArray(tools)).toBe(true);
    expect(tools[0]).toHaveProperty('name', 'getDateTime');
    expect(tools[0]).toHaveProperty('arguments');
    expect(tools[0]).toHaveProperty('description');
  });

  it('should handle connection failure gracefully in listTools', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    // Mock the SDK client to throw an error
    const mockClient = (client as any).client;
    mockClient.listTools.mockRejectedValueOnce(new Error('Network error'));

    await expect(client.listTools()).rejects.toThrow('Network error');
  });

  it('should execute getDateTime tool and parse the response', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    const result = await client.callTool('getDateTime', { format: 'ISO' });
    expect(result).toHaveProperty('time', '2025-06-04T09:00:00Z');
  });

  it('should handle tool execution errors', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    // Mock the SDK client to throw an error
    const mockClient = (client as any).client;
    mockClient.callTool.mockRejectedValueOnce(new Error('Timeout'));

    await expect(client.callTool('getDateTime', { format: 'ISO' })).rejects.toThrow('Timeout');
  });

  it('should support connection management methods', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    // Test connection state
    expect(client.isConnected()).toBe(false);

    // Test disconnect (should not throw when not connected)
    await expect(client.disconnect()).resolves.not.toThrow();

    // Test getting underlying client
    expect(client.getClient()).toBeDefined();
  });

  it('should convert SDK tool format to MCPToolMetadata format', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);

    const tools = await client.listTools();
    expect(tools[0]).toHaveProperty('name', 'getDateTime');
    expect(tools[0]).toHaveProperty('description', 'Get current date and time');
    expect(tools[0]).toHaveProperty('arguments');
    expect(tools[0].arguments).toHaveProperty('format');
    expect(tools[0].arguments.format).toHaveProperty('type', 'string');
  });
});
