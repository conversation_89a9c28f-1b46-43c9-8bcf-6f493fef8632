import { MC<PERSON>lient } from '../mcp-client';

describe('MCPClient', () => {
  it('should instantiate with configuration', () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    expect(client).toBeInstanceOf(MCPClient);
    expect(client.config).toEqual(config);
  });

  it('should have a connection test method that returns a boolean', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    const result = await client.testConnection();
    expect(typeof result).toBe('boolean');
  });

  it('should list available tools from the MCP server', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    client._post = async () => ({
      result: [{ name: 'getDateTime', arguments: { format: 'ISO' } }],
    });
    const tools = await client.listTools();
    expect(Array.isArray(tools)).toBe(true);
    expect(tools[0]).toHaveProperty('name', 'getDateTime');
    expect(tools[0]).toHaveProperty('arguments');
  });

  it('should handle connection failure gracefully in listTools', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    client._post = async () => {
      throw new Error('Network error');
    };
    await expect(client.listTools()).rejects.toThrow('Network error');
  });

  it('should execute getDateTime tool and parse the response', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    client._post = async (payload: any) => {
      expect(payload.method).toBe('callTool');
      expect(payload.params.tool).toBe('getDateTime');
      expect(payload.params.arguments).toEqual({ format: 'ISO' });
      return { result: { time: '2025-06-04T09:00:00Z' } };
    };
    const result = await client.callTool('getDateTime', { format: 'ISO' });
    expect(result).toHaveProperty('time', '2025-06-04T09:00:00Z');
  });

  describe('_post (transport layer)', () => {
    const config = { url: 'http://localhost:9999', timeout: 100 };

    it('should throw on network error', async () => {
      const client = new MCPClient(config);
      // Simulate fetch/network error
      client['_post'] = async () => {
        throw new Error('Simulated network error');
      };
      await expect(client._post({})).rejects.toThrow('Simulated network error');
    });

    it('should throw on HTTP error (non-2xx)', async () => {
      const client = new MCPClient(config);
      // Simulate HTTP error by throwing with status
      client['_post'] = async () => {
        throw Object.assign(new Error('HTTP 500'), { status: 500 });
      };
      await expect(client._post({})).rejects.toThrow('HTTP 500');
    });

    it('should throw on JSON-RPC error response', async () => {
      const client = new MCPClient(config);
      // Simulate JSON-RPC error object
      client['_post'] = async () => ({
        error: { code: -32601, message: 'Method not found' },
        id: 1,
      });
      const response = await client._post({});
      expect(response.error).toBeDefined();
      expect(response.error.message).toBe('Method not found');
    });

    it('should throw on malformed response', async () => {
      const client = new MCPClient(config);
      // Simulate malformed response (missing result/error)
      client['_post'] = async () => ({ foo: 'bar' });
      const response = await client._post({});
      expect(response.result).toBeUndefined();
      expect(response.error).toBeUndefined();
    });

    it('should timeout if request takes too long', async () => {
      const client = new MCPClient({ ...config, timeout: 10 });
      // Simulate long-running request
      client['_post'] = async () =>
        new Promise(resolve => setTimeout(() => resolve({ result: 'ok' }), 100));
      // This test is illustrative; real timeout logic should be in the implementation
      // Here, we just check that the promise does not resolve in time
      const promise = client._post({});
      await expect(
        Promise.race([
          promise,
          new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 20)),
        ])
      ).rejects.toThrow('Timeout');
    });

    it('should retry on transient network error', async () => {
      const client = new MCPClient(config);
      let callCount = 0;
      client['_post'] = async () => {
        callCount++;
        if (callCount < 2) {
          throw new Error('Transient network error');
        }
        return { result: 'ok' };
      };
      // Simulate retry logic in the real implementation
      let result;
      try {
        result = await client._post({});
      } catch (e) {
        // Retry once
        result = await client._post({});
      }
      expect(result.result).toBe('ok');
      expect(callCount).toBe(2);
    });
  });

  it('should handle tool execution errors', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    client._post = async () => {
      throw new Error('Timeout');
    };
    await expect(client.callTool('getDateTime', { format: 'ISO' })).rejects.toThrow('Timeout');
  });

  it('should handle invalid tool response', async () => {
    const config = { url: 'http://localhost:6274', timeout: 1000 };
    const client = new MCPClient(config);
    client._post = async () => ({});
    await expect(client.callTool('getDateTime', { format: 'ISO' })).rejects.toThrow(
      'Invalid MCP response'
    );
  });
});
