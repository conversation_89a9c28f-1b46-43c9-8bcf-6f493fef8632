/**
 * Type-level test for MCP types.
 * Jest requires at least one test block, so we wrap type assertions in a dummy test.
 */

import type {
  CustomerInfo,
  CustomerLookupResult,
  Tool,
  CallToolResult,
  ListToolsResult,
} from '../types';

test('MCP types are defined and importable (type-level check)', () => {
  // Example type-level tests (should not error when interfaces are correct)
  const info: CustomerInfo = {
    ani: '12345',
    customerName: 'John Doe',
    customerId: 'cust-123',
    metadata: {},
  };
  const lookupResult: CustomerLookupResult = {
    success: true,
    customer: info,
    source: 'mcp',
  };

  // SDK types
  const tool: Tool = {
    name: 'test-tool',
    description: 'A test tool',
    inputSchema: {
      type: 'object',
      properties: {
        input: { type: 'string' },
      },
    },
  };

  const callResult: CallToolResult = {
    content: [
      {
        type: 'text',
        text: 'Test result',
      },
    ],
  };

  const listResult: ListToolsResult = {
    tools: [tool],
  };

  // Type assertions (should not error when interfaces are correct)
  function assertType<T>(value: T) {
    return value;
  }

  assertType<CustomerInfo>(info);
  assertType<CustomerLookupResult>(lookupResult);
  assertType<Tool>(tool);
  assertType<CallToolResult>(callResult);
  assertType<ListToolsResult>(listResult);

  // No runtime assertions needed; this test is for type checking only.
  expect(true).toBe(true);
});
