/**
 * Type-level test for MCP types.
 * Jest requires at least one test block, so we wrap type assertions in a dummy test.
 */

import type { MCPRequest, MCPResponse, MCPError, CustomerInfo } from '../types';

test('MCP types are defined and importable (type-level check)', () => {
  // Example type-level tests (should not error when interfaces are correct)
  const req: MCPRequest = { tool: 'lookupCustomer', params: { ani: '12345' } };
  const res: MCPResponse = { result: { customerId: 'abc' } };
  const err: MCPError = { code: 'ERR', message: 'error' };
  const info: CustomerInfo = { ani: '12345', name: '<PERSON>', metadata: {} };
  // Type assertions (should not error when interfaces are correct)
  function assertType<T>(value: T) {
    return value;
  }
  assertType<MCPRequest>(req);
  assertType<MCPResponse>(res);
  assertType<MCPError>(err);
  assertType<CustomerInfo>(info);
  // No runtime assertions needed; this test is for type checking only.
  expect(true).toBe(true);
});
