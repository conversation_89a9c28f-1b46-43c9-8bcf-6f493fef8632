import { MCPClient } from '../mcp-client';
import { createMCPServiceFromEnv, MCPService } from '../mcp-service';

describe('MCPService', () => {
  it('should instantiate with an MCPClient', () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    const service = new MCPService(client);
    expect(service).toBeInstanceOf(MCPService);
    expect(service.client).toBe(client);
  });

  it('should initialize and check availability', async () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    const service = new MCPService(client);
    await service.initialize();
    const available = await service.isAvailable();
    expect(typeof available).toBe('boolean');
  });

  it('should handle MCP server unavailability gracefully', async () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    // Simulate MCP server down: callTool throws
    client.callTool = async () => {
      throw new Error('MCP unavailable');
    };
    const service = new MCPService(client);

    // For now, let's say the service should return null or a fallback object, not throw
    const result = await service.safeGetDateTime();
    expect(result).toBeNull(); // or expect fallback value
  });

  it('should handle errors in invokeTool gracefully', async () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    client.callTool = async () => {
      throw new Error('Simulated MCP error');
    };
    const service = new MCPService(client);
    const result = await service.invokeTool('getDateTime', { format: 'ISO' });
    expect(result).toBeNull();
  });

  it('should return false from isAvailable if MCP is down', async () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    client.testConnection = async () => {
      throw new Error('Simulated MCP down');
    };
    const service = new MCPService(client);
    const available = await service.isAvailable();
    expect(available).toBe(false);
  });

  it('should handle custom MCP error types gracefully', async () => {
    const client = new MCPClient({ url: 'http://localhost:6274', timeout: 1000 });
    // Simulate a custom error (e.g., MCPNetworkError)
    class MCPNetworkError extends Error {}
    client.callTool = async () => {
      throw new MCPNetworkError('Simulated network error');
    };
    const service = new MCPService(client);
    const result = await service.invokeTool('getDateTime', { format: 'ISO' });
    expect(result).toBeNull();
  });
});

describe('createMCPServiceFromEnv', () => {
  const OLD_ENV = process.env;

  beforeEach(() => {
    process.env = { ...OLD_ENV };
  });
  it('creates MCPService with MCP_SERVER_URL and timeout from env', () => {
    process.env.MCP_SERVER_URL = 'http://env-mcp:9000';
    process.env.MCP_TIMEOUT = '2500';
    const service = createMCPServiceFromEnv();
    expect(service).toBeInstanceOf(MCPService);
    expect(service.client.config.url).toBe('http://env-mcp:9000');
    expect(service.client.config.timeout).toBe(2500);
  });

  it('creates MCPService with only MCP_SERVER_URL from env', () => {
    process.env.MCP_SERVER_URL = 'http://env-mcp:9001';
    delete process.env.MCP_TIMEOUT;
    const service = createMCPServiceFromEnv();
    expect(service.client.config.url).toBe('http://env-mcp:9001');
    expect(service.client.config.timeout).toBeUndefined();
  });

  it('throws if MCP_SERVER_URL is missing', () => {
    delete process.env.MCP_SERVER_URL;
    expect(() => createMCPServiceFromEnv()).toThrow(/MCP_SERVER_URL/);
  });

  it('calls dispose() without error (lifecycle test)', async () => {
    process.env.MCP_SERVER_URL = 'http://env-mcp:9002';
    const service = createMCPServiceFromEnv();
    await expect(service.dispose()).resolves.toBeUndefined();
  });

  afterEach(() => {
    process.env = OLD_ENV;
  });
});
