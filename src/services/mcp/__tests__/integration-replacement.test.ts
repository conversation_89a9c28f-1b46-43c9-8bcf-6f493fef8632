/**
 * Phase 4 Step 4.1: TDD Test for Implementation Replacement
 * 
 * This test verifies that the SDK implementation can be a drop-in replacement
 */

import { MCPClientSDK } from '../mcp-client-sdk';
import type { Tool, CallToolResult } from '@modelcontextprotocol/sdk/types.js';

// Mock the SDK for testing
jest.mock('@modelcontextprotocol/sdk/client/index.js', () => ({
  Client: jest.fn().mockImplementation(() => ({
    connect: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    listTools: jest.fn().mockResolvedValue({
      tools: [
        {
          name: 'lookupCustomer',
          description: 'Look up customer by ANI',
          inputSchema: {
            type: 'object',
            properties: {
              ani: { type: 'string' }
            },
            required: ['ani']
          }
        }
      ]
    }),
    callTool: jest.fn().mockResolvedValue({
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            ani: '**********',
            customerName: '<PERSON>',
            customerId: 'cust-123',
            accountStatus: 'active'
          })
        }
      ]
    })
  }))
}));

jest.mock('@modelcontextprotocol/sdk/client/streamableHttp.js', () => ({
  StreamableHTTPClientTransport: jest.fn().mockImplementation(() => ({
    start: jest.fn().mockResolvedValue(undefined),
    close: jest.fn().mockResolvedValue(undefined),
    send: jest.fn().mockResolvedValue(undefined)
  }))
}));

describe('MCP SDK Integration Replacement', () => {
  describe('drop-in replacement compatibility', () => {
    let client: MCPClientSDK;

    beforeEach(async () => {
      client = new MCPClientSDK({
        baseUrl: 'http://127.0.0.1:6274',
        timeout: 5000,
      });
      await client.connect();
    });

    afterEach(async () => {
      await client.disconnect();
    });

    it('should work as drop-in replacement for customer lookup', async () => {
      // Test the customer lookup workflow that the voice connector uses
      const tools: Tool[] = await client.listTools();
      
      // Find customer lookup tool
      const customerTool = tools.find(tool => 
        tool.name.toLowerCase().includes('customer') ||
        tool.name.toLowerCase().includes('lookup')
      );
      
      expect(customerTool).toBeDefined();
      expect(customerTool?.name).toBe('lookupCustomer');
      
      // Call the customer lookup tool
      const result: CallToolResult = await client.callTool('lookupCustomer', {
        ani: '**********'
      });
      
      expect(result.content).toHaveLength(1);
      expect(result.content[0].type).toBe('text');
      
      // Parse customer data
      const textContent = result.content[0];
      if (textContent.type === 'text' && textContent.text) {
        const customerData = JSON.parse(textContent.text);
        expect(customerData.ani).toBe('**********');
        expect(customerData.customerName).toBe('John Doe');
        expect(customerData.customerId).toBe('cust-123');
        expect(customerData.accountStatus).toBe('active');
      }
    });

    it('should maintain error handling patterns', async () => {
      // Mock tool call failure
      const mockClient = (client as any).client;
      mockClient.callTool.mockRejectedValueOnce(new Error('Customer not found'));

      await expect(client.callTool('lookupCustomer', { ani: 'invalid' }))
        .rejects.toThrow('Customer not found');
    });

    it('should maintain connection state management', async () => {
      expect(client.isConnected()).toBe(true);
      
      await client.disconnect();
      expect(client.isConnected()).toBe(false);
      
      // Should throw when not connected
      await expect(client.listTools()).rejects.toThrow('Not connected to MCP server');
    });

    it('should support test connection method', async () => {
      const isConnected = await client.testConnection();
      expect(isConnected).toBe(true);
    });
  });

  describe('configuration compatibility', () => {
    it('should accept same configuration as original client', () => {
      const config = {
        baseUrl: 'http://127.0.0.1:6274',
        timeout: 10000,
        retryAttempts: 5,
        retryDelay: 2000,
      };

      expect(() => {
        const client = new MCPClientSDK(config);
        expect(client).toBeDefined();
      }).not.toThrow();
    });

    it('should use environment variables for defaults', () => {
      // Test with no config provided
      const client = new MCPClientSDK();
      expect(client).toBeDefined();
      
      // Should use default URL from environment or fallback
      expect(client.isConnected()).toBe(false);
    });
  });

  describe('interface compatibility', () => {
    it('should expose all required methods', () => {
      const client = new MCPClientSDK();
      
      // Check all methods that existing code expects
      expect(typeof client.connect).toBe('function');
      expect(typeof client.disconnect).toBe('function');
      expect(typeof client.isConnected).toBe('function');
      expect(typeof client.listTools).toBe('function');
      expect(typeof client.callTool).toBe('function');
      expect(typeof client.testConnection).toBe('function');
      expect(typeof client.getClient).toBe('function');
    });

    it('should return correct types', async () => {
      const client = new MCPClientSDK();
      await client.connect();
      
      // Test return types
      const tools = await client.listTools();
      expect(Array.isArray(tools)).toBe(true);
      
      const result = await client.callTool('lookupCustomer', { ani: '123' });
      expect(result).toHaveProperty('content');
      expect(Array.isArray(result.content)).toBe(true);
      
      await client.disconnect();
    });
  });
});
