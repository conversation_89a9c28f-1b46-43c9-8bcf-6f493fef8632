# MCP Service - Official SDK Implementation

## Overview

The MCP (Model Context Protocol) service provides customer identification capabilities using the official MCP SDK. This implementation has been migrated from a custom HTTP client to use the official MCP SDK for proper protocol compliance and better reliability.

## Architecture

### Current Implementation (SDK-Based)

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Voice App     │───▶│   MCP Service    │───▶│   MCP Server    │
│                 │    │                  │    │                 │
│ - Session Mgmt  │    │ - SDK Client     │    │ - Customer DB   │
│ - Call Handling │    │ - Tool Calling   │    │ - Tool Registry │
│ - ANI Lookup    │    │ - Error Handling │    │ - Business Logic│
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### Key Components

1. **MCPClient** (`mcp-client.ts`) - Main client using official SDK
2. **MCPService** (`mcp-service.ts`) - High-level service wrapper
3. **Types** (`types.ts`) - TypeScript interfaces with SDK compatibility
4. **Error Handling** (`errors.ts`) - Custom error types

## Implementation Progress

### ✅ Phase 1: SDK Integration Foundation
- [x] 1.1 Verify SDK Import Compatibility
- [x] 1.2 Create SDK Client Wrapper
- [x] 1.3 Update Type Definitions for SDK

### ✅ Phase 2: Migrate MCPClient to Official SDK
- [x] 2.1 Implement SDK-Based Client
- [x] 2.2 Maintain Interface Compatibility
- [x] 2.3 Add Connection Management

### ✅ Phase 3: Update MCPService Integration
- [x] 3.1 Update Service to Use SDK Client
- [x] 3.2 Preserve Error Handling Patterns
- [x] 3.3 Maintain Logging and Metrics

### ✅ Phase 4: Integration Testing & Validation
- [x] 4.1 Replace Implementation Files
- [x] 4.2 Run Comprehensive Tests
- [x] 4.3 Validate Backward Compatibility

## Usage Examples

### Basic Client Usage

```typescript
import { MCPClient } from './mcp-client';

const client = new MCPClient({
  url: 'http://127.0.0.1:6274',
  timeout: 5000
});

// List available tools
const tools = await client.listTools();

// Call a tool
const result = await client.callTool('lookupCustomer', {
  ani: '**********'
});
```

### Service-Level Usage

```typescript
import { MCPService } from './mcp-service';

const mcpService = new MCPService({
  baseUrl: 'http://127.0.0.1:6274',
  timeout: 5000
});

// Lookup customer by ANI
const customer = await mcpService.lookupCustomer('**********');
```

## Configuration

### Environment Variables

```bash
# MCP Server Configuration
MCP_SERVER_URL=http://127.0.0.1:6274
MCP_TIMEOUT=5000
MCP_RETRY_ATTEMPTS=3
MCP_RETRY_DELAY=1000

# Production Configuration
MCP_SERVER_URL=https://aidcc-ccczautomationmcpserver-dev.aks-bf.network.cz.o2/mcp
```

### Configuration Options

```typescript
interface MCPClientConfig {
  url: string;           // MCP server URL
  timeout?: number;      // Request timeout (default: 5000ms)
}

interface MCPServiceConfig {
  baseUrl?: string;      // MCP server URL
  timeout?: number;      // Request timeout
  retryAttempts?: number; // Retry attempts (default: 3)
  retryDelay?: number;   // Retry delay (default: 1000ms)
}
```

## Testing

### Running Tests

```bash
# Run all MCP tests (unit tests with mocks)
npm run test:mcp

# Run specific test suites
npm test src/services/mcp/__tests__/mcp-client.test.ts
npm test src/services/mcp/__tests__/mcp-service.test.ts
npm test src/services/mcp/__tests__/integration-replacement.test.ts

# Run local integration test with real MCP server
npm run test:mcp-local
```

### Test Coverage

- **SDK Import Tests** - Verify SDK compatibility and imports
- **Client Tests** - Test SDK-based client functionality with mocks
- **Service Tests** - Test high-level service operations
- **Integration Tests** - Test drop-in replacement compatibility
- **Type Tests** - Verify TypeScript type compatibility

### Local Integration Testing

For testing with a real MCP server (not part of CI pipeline):

#### Prerequisites
1. **Start MCP Server**:
   ```bash
   cd ~/Dev/aidcc-ccczautomationmcpserver
   npm start
   ```
2. **Verify Server**: Check `http://127.0.0.1:6274` is accessible
3. **Check Tools**: Use MCP Inspector to verify customer lookup tools are available

#### Run Local Tests
```bash
# TypeScript version (recommended)
npm run test:mcp-local

# Or run directly with tsx
npx tsx scripts/test-mcp-integration.ts
```

#### What Local Tests Cover
- **Real MCP Server Connection** - Tests actual SDK connection to running server
- **Tool Discovery** - Lists and verifies available tools
- **Customer Lookup** - Tests customer identification with various ANIs
- **Error Handling** - Tests connection failures and invalid inputs
- **Service Integration** - Tests high-level service wrapper
- **SDK Features** - Tests connection management and SDK-specific functionality

#### Sample Output
```
🧪 MCP Integration Local Testing (SDK-Based)
Testing MCP server at: http://127.0.0.1:6274

=== Testing MCP Client (Low-Level SDK) ===
ℹ️  Testing MCP server connection...
✅ MCP server connection successful
ℹ️  Discovering available tools...
✅ Found 3 tools:
  - lookupCustomer: Look up customer information by ANI
  - getDateTime: Get current date and time
  - getWeather: Get weather information

=== Testing MCP Service (High-Level) ===
ℹ️  Initializing MCP service...
✅ MCP service initialized successfully
ℹ️  Service lookup for ANI: **********
✅ Customer found for **********:
{
  "ani": "**********",
  "customerName": "John Doe",
  "customerId": "cust-123",
  "accountStatus": "active"
}

=== Test Summary ===
✅ All MCP integration tests passed! 🎉
```

## Migration Notes

### What Changed

1. **Transport Layer**: Replaced custom HTTP client with official MCP SDK
2. **Protocol Compliance**: Now uses proper MCP JSON-RPC 2.0 protocol
3. **Type Safety**: Enhanced with official SDK types
4. **Error Handling**: Improved error handling with SDK error types

### Backward Compatibility

- All existing interfaces maintained
- Same method signatures preserved
- Configuration options unchanged
- Error handling patterns preserved

### Benefits

1. **Protocol Compliance**: Proper MCP protocol implementation
2. **Reliability**: Official SDK with better error handling
3. **Maintainability**: Reduced custom code, leverages official SDK
4. **Future-Proof**: Automatic updates with SDK improvements

## Dependencies

### Required Packages

```json
{
  "@modelcontextprotocol/sdk": "^1.0.0"
}
```

### Development Dependencies

```json
{
  "@types/jest": "^29.0.0",
  "jest": "^29.0.0",
  "ts-jest": "^29.0.0"
}
```

## Integration Points

### With Voice Connector

The MCP service integrates with the voice connector for customer identification:

1. **ANI Lookup**: Called during call setup to identify customers
2. **Session Context**: Provides customer information for call handling
3. **Error Handling**: Graceful fallback when MCP server unavailable

### With Genesys Cloud

Customer information retrieved from MCP is used to:

1. **Call Routing**: Route calls based on customer type
2. **Personalization**: Customize voice responses
3. **Context**: Provide agent context for escalated calls

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check MCP server URL and network connectivity
2. **Timeout Issues**: Increase timeout configuration
3. **Tool Not Found**: Verify tool availability with `listTools()`
4. **Authentication**: Ensure proper MCP server authentication

### Debugging

Enable debug logging:

```typescript
// Set log level to debug
process.env.LOG_LEVEL = 'debug';
```

### Health Checks

```typescript
// Test MCP server connectivity
const isHealthy = await client.testConnection();
```

## Future Enhancements

1. **Connection Pooling**: Implement connection pooling for better performance
2. **Caching**: Add customer data caching for reduced latency
3. **Metrics**: Enhanced metrics and monitoring
4. **Failover**: Multiple MCP server support for high availability
