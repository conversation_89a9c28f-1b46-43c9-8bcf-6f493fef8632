# Transcript Service: Technical Overview

## Purpose

The Transcript Service handles transcript processing, deduplication, and barge-in flag management for the application. It ensures that only new, relevant transcripts are processed, preventing duplicate processing of the same text and managing special handling for barge-in transcripts.

---

## Key Responsibilities

- Detect and filter duplicate transcripts
- Manage barge-in flag for special transcript handling
- Track transcript processing history
- Provide a clean interface for transcript processing
- Optimize processing by avoiding redundant operations

---

## Core Components and Their Roles

### TranscriptProcessor (`src/services/transcript/transcript-processor.ts`)

- Handles duplicate transcript detection
- Manages barge-in flag for special handling
- Tracks processing history and timestamps
- Provides methods for transcript validation

---

## Transcript Deduplication

The TranscriptProcessor implements transcript deduplication:

- Tracks the last processed transcript text and timestamp
- Compares incoming transcripts against the history
- Considers a transcript duplicate if the text matches and was processed recently
- Provides special handling for transcripts following barge-in events
- Logs detailed information about duplicate detection when enabled

This prevents the system from processing the same user input multiple times, which could lead to duplicate responses and confusion.

---

## Barge-In Flag Management

The TranscriptProcessor manages a special flag for barge-in transcripts:

- When a barge-in event occurs, the flag is set
- The next transcript is processed regardless of duplication
- This ensures that transcripts following barge-in are always processed
- The flag is automatically reset after use

This special handling ensures that user interruptions are properly processed, even if the text is similar to previously processed input.

---

## Component Interactions

1. **Initialization**: TranscriptProcessor is created during session initialization
2. **Barge-In Handling**: When barge-in occurs, the flag is set
3. **Duplicate Detection**: Incoming transcripts are checked for duplication
4. **Transcript Processing**: Non-duplicate transcripts are processed
5. **History Tracking**: Processed transcripts are added to the history

---

## Configuration Options

The Transcript Service can be configured through logging settings:

- `loggingConfig.logDuplicateChecks`: Enable detailed logging of duplicate detection

---

## Design Principles

- **Stateful Processing**: Maintains history for intelligent deduplication
- **Special Case Handling**: Provides special handling for barge-in transcripts
- **Configurable Logging**: Detailed logging for debugging when needed
- **Clean Interface**: Simple API for transcript validation
- **Performance Optimization**: Prevents redundant processing

---

## File Structure

- `src/services/transcript/`
  - `transcript-processor.ts` – Core implementation of the TranscriptProcessor

---

## Usage Example

```typescript
// Create TranscriptProcessor
const transcriptProcessor = new TranscriptProcessor();

// Set barge-in flag when barge-in is detected
transcriptProcessor.setBargeInFlag(true);

// Check if transcript is a duplicate
const isDuplicate = transcriptProcessor.isDuplicate('Hello there', 'continuous');

if (!isDuplicate) {
  // Process the transcript
  processTranscript('Hello there');

  // Update the last processed transcript
  transcriptProcessor.updateLastProcessed('Hello there');
}
```

---

## Integration with Other Services

The Transcript Service integrates with several other services:

- **Session**: Uses the processor to validate transcripts before processing
- **AudioManager**: Provides transcripts that need validation
- **BargeInManager**: Triggers barge-in flag setting when barge-in is detected
- **BotServiceAdapter**: Processes validated transcripts
