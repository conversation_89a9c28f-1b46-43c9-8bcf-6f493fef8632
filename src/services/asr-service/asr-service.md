# ASR Service: Technical Overview

## Purpose

The ASR (Automatic Speech Recognition) service provides speech-to-text functionality for the application, supporting multiple providers (Azure and Google) and different streaming modes. It handles audio processing, transcript generation, and integrates with the session lifecycle.

---

## Key Responsibilities

- Process audio data and convert it to text transcripts
- Support multiple ASR providers (Azure, Google)
- Provide different streaming modes (standard, hybrid, continuous)
- Emit transcript events for interim and final results
- Handle error cases and resource cleanup
- Support barge-in detection during audio playback

---

## Core Components and Their Roles

### BaseSpeechService (`src/services/asr-service/base-speech-service.ts`)

- Abstract base class defining the interface for all speech services
- Provides common event handling and state management
- Defines the core methods all ASR services must implement

### AzureSpeechService (`src/services/asr-service/azure-speech-service.ts`)

- Implements Azure Cognitive Services speech recognition
- Handles audio stream processing and event emission
- Manages Azure-specific configuration and initialization

### GoogleSpeechAdapter (`src/services/asr-service/google-speech-adapter.ts`)

- Adapts Google Cloud Speech API to the BaseSpeechService interface
- Handles Google-specific configuration and initialization
- Converts Google transcript format to the application's format

### ASRServiceWithPool (`src/services/asr-service/asr-service-with-pool.ts`)

---

## Service Call Measurement

All ASR provider calls are measured using the `callWithMeasurement` utility at the top-level service entry points (`EnhancedASRService.processAudio`, `ASRServiceWithPool.processAudio`). This ensures that every speech-to-text operation is timed and logged as a service timing entry in the metrics context.

- **Provider-level methods** (e.g., `AzureSpeechService.processAudio`, `GoogleSpeechAdapter.processAudio`) are not measured unless called directly in tests or custom code.
- **Best Practice:** Always use the top-level ASR service for audio processing to ensure metrics are recorded.

---

- Provides connection pooling for Azure speech services
- Manages multiple concurrent speech recognition sessions
- Improves performance and resource utilization

### EnhancedASRService (`src/services/asr-service/index.ts`)

- Wraps the underlying ASR service with additional functionality
- Provides a simplified interface for session integration
- Handles initialization and event routing

---

## Streaming Modes

The ASR service supports three streaming modes:

1. **Standard Mode**: Uses the ASR provider's built-in final transcript detection

   - Relies on the provider to determine when speech is complete
   - Processes both interim and final transcripts from the provider

2. **Hybrid Mode**: Uses both provider finals and pause detection

   - Processes final transcripts from the provider
   - Also uses pause detection to finalize transcripts when natural pauses occur

3. **Continuous Mode**: Uses only pause detection
   - Ignores final transcripts from the provider
   - Relies solely on pause detection to determine when speech is complete
   - Provides more control over transcript finalization

---

## Component Interactions

1. **Initialization**: ASR service is created and initialized with provider-specific configuration
2. **Audio Processing**: Audio data is sent to the service for processing
3. **Transcript Generation**: Service emits interim and final transcript events
4. **Event Handling**: Consumers (e.g., Session) handle transcript events
5. **Resource Cleanup**: Service resources are properly disposed when no longer needed

---

## Configuration Options

The ASR service can be configured through environment variables:

- `SPEECH_SERVICE`: Provider selection ('azure' or 'google')
- `AZURE_SPEECH_KEY`: Azure subscription key
- `AZURE_SPEECH_REGION`: Azure region
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google credentials file
- `ASR_STREAMING_MODE`: Streaming mode ('standard', 'hybrid', or 'continuous')
- `PAUSE_THRESHOLD_MS`: Pause threshold for transcript finalization (default: 1500ms)
- `ENABLE_STABLE_STREAM`: Enable pause detection (true/false)

---

## Design Principles

- **Provider Abstraction**: Common interface for different ASR providers
- **Flexible Configuration**: Support for different streaming modes and settings
- **Event-Driven Architecture**: Asynchronous event emission for transcripts
- **Resource Management**: Proper initialization and cleanup of resources
- **Error Handling**: Consistent error reporting and recovery

---

## File Structure

- `src/services/asr-service/`
  - `index.ts` – Main entry point and EnhancedASRService
  - `base-speech-service.ts` – Abstract base class
  - `azure-speech-service.ts` – Azure implementation
  - `google-speech-adapter.ts` – Google implementation
  - `asr-service-with-pool.ts` – Connection pooling
  - `azure-speech-service-pool.ts` – Azure-specific pooling
  - `types.ts` – Type definitions

---

## Usage Example

```typescript
// Create an EnhancedASRService with handlers
const asrService = new EnhancedASRService(
  (transcript, isFinal) => {
    // Handle transcript
    console.log(`Transcript ${isFinal ? '(final)' : '(interim)'}: ${transcript.text}`);
  },
  error => {
    // Handle error
    console.error('ASR error:', error);
  }
);

// Initialize with conversation ID
await asrService.initialize('conversation-123');

// Process audio data
await asrService.processAudio(audioData);

// Clean up resources when done
asrService.dispose();
```
