/**
 * Represents a speech recognition transcript with text and optional confidence score
 */
export interface Transcript {
  text: string;
  confidence?: number;
  isFinal: boolean;
}

/**
 * Represents the possible states of a speech recognition service
 */
export type SpeechServiceState = 'None' | 'Processing' | 'Complete' | 'Error';

/**
 * Events emitted by the speech service
 */
export interface SpeechServiceEvents {
  transcript: (transcript: Transcript) => void;
  'final-transcript': (transcript: Transcript) => void;
  error: (error: Error | string) => void;
}
