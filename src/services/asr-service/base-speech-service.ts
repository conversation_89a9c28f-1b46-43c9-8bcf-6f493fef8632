import EventEmitter from 'events';
import { SpeechServiceState, SpeechServiceEvents, Transcript } from './types';
import { RequestContext } from '../monitoring/performance-logger';
import { logDebug } from '../logging/logger';

/**
 * Abstract base class for speech recognition services
 * Provides common functionality and defines the interface that all speech services must implement
 */
export abstract class BaseSpeechService {
  protected emitter: EventEmitter;
  protected state: SpeechServiceState;

  constructor() {
    this.emitter = new EventEmitter();
    this.state = 'None';
  }

  /**
   * Get the last final transcript (default: null, override in subclasses)
   */
  getLastTranscript(): Transcript | null {
    return null;
  }

  /**
   * Process a chunk of audio data for speech recognition
   * @param data Raw audio data as Uint8Array (8kHz PCMU/µ-law format)
   */
  /**
   * Process a chunk of audio data for speech recognition, optionally measuring timing.
   * @param data Raw audio data as Uint8Array (8kHz PCMU/µ-law format)
   * @param metricsContext Optional metrics context for timing
   */
  abstract processAudio(data: Uint8Array, metricsContext: RequestContext): Promise<void>;

  /**
   * Clean up resources used by the speech service
   */
  abstract dispose(): void;

  /**
   * Add an event listener for speech service events
   * @param event Event name ('transcript', 'final-transcript', or 'error')
   * @param listener Callback function to handle the event
   */
  on<K extends keyof SpeechServiceEvents>(event: K, listener: SpeechServiceEvents[K]): this {
    this.emitter.addListener(event, listener);
    return this;
  }

  /**
   * Remove an event listener
   * @param event Event name
   * @param listener Callback function to remove
   */
  off<K extends keyof SpeechServiceEvents>(event: K, listener: SpeechServiceEvents[K]): this {
    this.emitter.removeListener(event, listener);
    return this;
  }

  /**
   * Get the current state of the speech service
   */
  getState(): SpeechServiceState {
    return this.state;
  }

  /**
   * Emit a transcript event (protected helper method)
   */
  protected emitTranscript(transcript: Transcript): void {
    logDebug(
      `[BaseSpeechService] emitTranscript: Emitting transcript | text="${
        transcript?.text ?? ''
      }" | isFinal=${transcript.isFinal}`
    );
    this.emitter.emit('transcript', transcript);
  }

  /**
   * Emit an error event (protected helper method)
   */
  protected emitError(error: Error | string): void {
    this.state = 'Error';
    this.emitter.emit('error', error);
  }
}
