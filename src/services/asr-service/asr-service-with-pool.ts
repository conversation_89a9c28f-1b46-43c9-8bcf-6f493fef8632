import { AzureSpeechServicePool } from './azure-speech-service-pool';
import { Transcript } from './types';
import { BaseSpeechService } from './base-speech-service';
import { logWarning } from '../logging';
import { RequestContext } from '../monitoring/performance-logger';
import { callWithMeasurement } from '../monitoring/measure-call';

export class ASRServiceWithPool extends BaseSpeechService {
  private speechPool: AzureSpeechServicePool;

  constructor(maxConcurrent = 3, queueTimeout = 30000) {
    super();
    this.state = 'None';
    this.speechPool = new AzureSpeechServicePool(maxConcurrent, queueTimeout);
    this.setupPoolCallbacks();
  }

  private setupPoolCallbacks(): void {
    this.speechPool.onTranscript((transcript: Transcript, isFinal?: boolean) => {
      if (isFinal === undefined) {
        logWarning('[ASR]: Speech isFinal property is undefined.');
      }
      this.emitTranscript({ ...transcript, isFinal: !!isFinal });
    });

    this.speechPool.onError((error: Error) => {
      this.emitError(error);
      console.error('[ASR Error]', error.message);
    });

    // this.speechPool.onQueueSizeChanged((size: number) => {
    //   if (size > 0) {
    //     console.log(`[ASR Pool] ${size} requests waiting in queue`);
    //   }
    // });
  }

  async processAudio(data: Uint8Array, metricsContext: RequestContext): Promise<void> {
    try {
      this.state = 'Processing';
      await callWithMeasurement(metricsContext, 'ASRServiceCall', async () =>
        this.speechPool.processAudio(data)
      );
    } catch (error) {
      this.emitError(error instanceof Error ? error : new Error('Failed to process audio'));
    }
  }

  getPoolStats() {
    return this.speechPool.getStats();
  }

  dispose(): void {
    this.state = 'Complete';
    this.speechPool.dispose();
    this.emitter.removeAllListeners();
  }

  // Track the last transcript
  private lastTranscript: Transcript | null = null;

  /**
   * Override the emitTranscript method to track the last transcript
   */
  protected emitTranscript(transcript: Transcript): void {
    // Store the transcript if it's final
    if (transcript.isFinal) {
      this.lastTranscript = transcript;
    }
    // Call the parent method to emit the event
    super.emitTranscript(transcript);
  }

  /**
   * Get the last final transcript
   * @returns The last transcript or null if none exists
   */
  getLastTranscript(): Transcript | null {
    return this.lastTranscript;
  }
}
