import { EventEmitter } from 'events';

interface QueuedRequest {
  resolve: (connection: any) => void;
  reject: (error: Error) => void;
  timestamp: number;
}

export class ConnectionPoolManager {
  protected pool: any[] = [];
  protected activeConnections: Map<any, boolean> = new Map();
  private queue: QueuedRequest[] = [];
  protected maxPoolSize: number;
  protected queueTimeout: number;
  private events: EventEmitter;
  protected validateConnection?: (connection: any) => Promise<boolean>;

  constructor(maxPoolSize = 10, queueTimeout = 30000) {
    this.maxPoolSize = maxPoolSize;
    this.queueTimeout = queueTimeout;
    this.events = new EventEmitter();
  }

  /**
   * Acquires a connection from the pool or queues the request if none available
   */
  async acquireConnection(): Promise<any> {
    // First try to find an available connection
    for (const connection of this.pool) {
      if (!this.activeConnections.get(connection)) {
        // Validate connection if validator is provided
        if (this.validateConnection) {
          try {
            const isValid = await this.validateConnection(connection);
            if (!isValid) {
              await this.removeConnection(connection);
              continue;
            }
          } catch (error) {
            console.error('[Pool] Connection validation failed:', error);
            await this.removeConnection(connection);
            continue;
          }
        }

        this.activeConnections.set(connection, true);
        return connection;
      }
    }

    // If pool is not at max size, create new connection
    if (this.pool.length < this.maxPoolSize) {
      try {
        const newConnection = await this.createConnection();
        this.pool.push(newConnection);
        this.activeConnections.set(newConnection, true);
        return newConnection;
      } catch (error) {
        console.error('[Pool] Failed to create new connection:', error);
        throw error;
      }
    }

    // Queue the request if no connections available
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        const index = this.queue.findIndex(request => request.resolve === resolve);
        if (index !== -1) {
          this.queue.splice(index, 1);
          reject(new Error('Connection request timeout'));
        }
      }, this.queueTimeout);

      this.queue.push({
        resolve: (connection: any) => {
          clearTimeout(timeoutId);
          resolve(connection);
        },
        reject: (error: Error) => {
          clearTimeout(timeoutId);
          reject(error);
        },
        timestamp: Date.now(),
      });

      this.events.emit('queueSizeChanged', this.queue.length);
    });
  }

  /**
   * Releases a connection back to the pool
   */
  releaseConnection(connection: any): void {
    this.activeConnections.set(connection, false);

    // Check if there are any queued requests
    if (this.queue.length > 0) {
      const nextRequest = this.queue.shift();
      if (nextRequest) {
        this.activeConnections.set(connection, true);
        nextRequest.resolve(connection);
        this.events.emit('queueSizeChanged', this.queue.length);
      }
    }
  }

  /**
   * Creates a new connection
   */
  protected async createConnection(): Promise<any> {
    // This will be implemented by the specific service (AzureSpeechService)
    throw new Error('createConnection must be implemented by service class');
  }

  /**
   * Removes a connection from the pool
   */
  protected async removeConnection(connection: any): Promise<void> {
    const index = this.pool.indexOf(connection);
    if (index !== -1) {
      this.pool.splice(index, 1);
      this.activeConnections.delete(connection);

      // If the connection has a dispose method, call it
      if (connection && typeof connection.dispose === 'function') {
        try {
          await connection.dispose();
        } catch (error) {
          console.error('[Pool] Error disposing connection:', error);
        }
      }
    }
  }

  /**
   * Gets current pool statistics
   */
  getStats() {
    return {
      poolSize: this.pool.length,
      activeConnections: Array.from(this.activeConnections.values()).filter(Boolean).length,
      queueLength: this.queue.length,
      maxPoolSize: this.maxPoolSize,
    };
  }

  /**
   * Disposes all connections in the pool
   */
  async dispose(): Promise<void> {
    // Reject any queued requests
    this.queue.forEach(request => {
      request.reject(new Error('Connection pool is shutting down'));
    });
    this.queue = [];

    // Close all connections
    this.pool = [];
    this.activeConnections.clear();
    this.events.removeAllListeners();
  }

  /**
   * Subscribe to pool events
   */
  onQueueSizeChanged(callback: (size: number) => void): void {
    this.events.on('queueSizeChanged', callback);
  }
}
