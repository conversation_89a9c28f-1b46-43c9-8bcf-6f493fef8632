import { AzureSpeechService } from './azure-speech-service';
import { BaseSpeechService } from './base-speech-service';
import { ASRServiceWithPool } from './asr-service-with-pool';
import { GoogleSpeechAdapter } from './google-speech-adapter';
import { GoogleSpeechService } from '../speech/google/google-speech-service';
import { GoogleSpeechFactory } from '../speech/google/google-speech-factory';
import { TTSProvider } from '../speech/base/types';
export { Transcript, SpeechServiceState } from './types';
import { logError, logDebug, logWarning } from '../logging/logger';

// Default pool configuration
const DEFAULT_MAX_CONCURRENT = 1;
const DEFAULT_QUEUE_TIMEOUT = 30000;

/**
 * Creates a new instance of the speech recognition service
 * @param usePool Whether to use the connection pool implementation (default: true)
 * @param maxConcurrent Maximum number of concurrent connections when using pool
 * @param queueTimeout Timeout for queued requests when using pool
 * @returns A speech service instance
 */
export async function createSpeechService(
  usePool = true,
  maxConcurrent: number = DEFAULT_MAX_CONCURRENT,
  queueTimeout: number = DEFAULT_QUEUE_TIMEOUT
): Promise<BaseSpeechService> {
  // Get the speech service provider from environment
  const speechProvider = process.env.ASR_SERVICE?.toLowerCase() || 'azure';

  logDebug(`[ASR] Creating speech service with provider: ${speechProvider}, usePool: ${usePool}`);

  if (speechProvider === 'google') {
    // For Google, we use our adapter class
    logDebug('[ASR] Creating Google Speech Adapter');
    // Use the GoogleSpeechFactory to create and initialize the service
    const config = {
      asrProvider: 'google' as 'azure' | 'google',
      ttsProvider: 'google' as TTSProvider,
      projectId: process.env.GOOGLE_PROJECT_ID,
      keyFilePath: process.env.GOOGLE_APPLICATION_CREDENTIALS,
    };
    const factory = new GoogleSpeechFactory(config);
    const googleService = (await factory.createASRService()) as GoogleSpeechService;
    const googleAdapter = new GoogleSpeechAdapter(googleService);
    await googleAdapter.initialize();
    return googleAdapter;
  } else {
    // For Azure, we can use the pool implementation
    if (usePool) {
      logDebug(`[ASR] Creating pooled Azure Speech service (max concurrent: ${maxConcurrent})`);
      return new ASRServiceWithPool(maxConcurrent, queueTimeout);
    } else {
      logDebug('[ASR] Creating single-connection Azure Speech service');
      return new AzureSpeechService();
    }
  }
}

// Export base class and implementations for type usage
export { BaseSpeechService, ASRServiceWithPool };
/**
 * EnhancedASRService provides callback-based ASR initialization and event handling.
 */
import type { Transcript } from './types';
import { RequestContext } from '../monitoring/performance-logger';
import { callWithMeasurement } from '../monitoring/measure-call';

export class EnhancedASRService {
  // Changed from private to protected to allow access from AudioServiceAdapter
  protected asrService: BaseSpeechService | null = null;
  private onTranscriptCallback: (transcript: Transcript, isFinal: boolean) => void;
  private onErrorCallback: (error: Error | string) => void;

  /**
   * Get the underlying ASR service
   * This allows other components to access the ASR service directly
   */
  getASRService(): BaseSpeechService | null {
    return this.asrService;
  }

  constructor(
    onTranscript: (transcript: Transcript, isFinal: boolean) => void,
    onError: (error: Error | string) => void
  ) {
    this.onTranscriptCallback = onTranscript;
    this.onErrorCallback = onError;
  }

  async initialize(conversationId: string): Promise<void> {
    if (this.asrService) {
      return;
    }
    // For now, we use the default pool config; can be extended as needed
    this.asrService = await createSpeechService();
    this.setupEventHandlers();
    // If the underlying ASR service needs conversationId, set it here
    if (typeof (this.asrService as any).setConversationId === 'function') {
      (this.asrService as any).setConversationId(conversationId);
    }
  }

  async processAudio(data: Uint8Array, metricsContext: RequestContext): Promise<void> {
    if (!this.asrService) {
      throw new Error('ASR service not initialized');
    }
    await callWithMeasurement(metricsContext, 'ASRServiceCall', async () =>
      this.asrService!.processAudio(data, metricsContext)
    );
  }
  /**
   * Arm the ASR service to be ready for new user input.
   * This should start or restart recognition as needed.
   */
  public async armForInput(): Promise<void> {
    if (!this.asrService) {
      logError('[EnhancedASRService] armForInput: ASR service not initialized');
      throw new Error('ASR service not initialized');
    }
    const serviceType = this.asrService.constructor?.name || typeof this.asrService;
    if (typeof (this.asrService as any).restartRecognition === 'function') {
      logDebug(`[EnhancedASRService] armForInput: Calling restartRecognition on ${serviceType}`);
      await (this.asrService as any).restartRecognition();
      logDebug(`[EnhancedASRService] armForInput: restartRecognition completed on ${serviceType}`);
    } else if (typeof (this.asrService as any).startListening === 'function') {
      logDebug(`[EnhancedASRService] armForInput: Calling startListening on ${serviceType}`);
      await (this.asrService as any).startListening();
      logDebug(`[EnhancedASRService] armForInput: startListening completed on ${serviceType}`);
    } else if (typeof (this.asrService as any).stopIgnoringAudioInput === 'function') {
      logDebug(
        `[EnhancedASRService] armForInput: Calling stopIgnoringAudioInput on ${serviceType}`
      );
      (this.asrService as any).stopIgnoringAudioInput();
      logDebug(
        `[EnhancedASRService] armForInput: stopIgnoringAudioInput completed on ${serviceType}`
      );
    } else {
      logWarning(`[EnhancedASRService] armForInput: No suitable method found on ${serviceType}`);
    }
  }

  private setupEventHandlers(): void {
    if (!this.asrService) {
      return;
    }
    this.asrService
      .on('error', (error: any) => {
        this.onErrorCallback(error);
      })
      .on('transcript', (transcript: Transcript) => {
        this.onTranscriptCallback(transcript, false);
      })
      .on('final-transcript', (transcript: Transcript) => {
        this.onTranscriptCallback(transcript, true);
      });
  }

  async dispose(): Promise<void> {
    if (this.asrService) {
      try {
        // Just call dispose and let TypeScript handle it
        // The BaseSpeechService.dispose is synchronous, but our adapter makes it async
        this.asrService.dispose();
      } catch (error) {
        logError(`[EnhancedASRService] Error disposing ASR service: ${error}`);
      }
    }
    this.asrService = null;
  }
}
