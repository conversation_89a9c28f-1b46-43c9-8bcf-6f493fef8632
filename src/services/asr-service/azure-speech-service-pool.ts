// @ts-ignore
// This require cannot be converted to an import due to SDK compatibility issues
const speechSDK = require('microsoft-cognitiveservices-speech-sdk');
import { ConnectionPoolManager } from './connection-pool-manager';
import { EventEmitter } from 'events';
import { Transcript } from './types';

interface ASRConnection {
  recognizer: any;
  audioStream: any;
  active: boolean;
  lastUsed: number;
  writeCount: number;
}

export class AzureSpeechServicePool extends ConnectionPoolManager {
  private emitter: EventEmitter;
  private config: any;
  private initialized = false;
  private currentConnection: ASRConnection | null = null;

  constructor(maxPoolSize = 3, queueTimeout = 5000) {
    super(maxPoolSize, queueTimeout);
    this.emitter = new EventEmitter();
    this.initializeConfig();

    let lastReportedSize = 0;
    let lastReportTime = Date.now();

    // Log queue size changes only periodically or on significant changes
    this.onQueueSizeChanged(size => {
      const now = Date.now();
      const timeSinceLastReport = now - lastReportTime;

      // Report if: queue empties, size changes significantly, or 5 seconds passed
      if (size === 0 || Math.abs(size - lastReportedSize) >= 10 || timeSinceLastReport > 5000) {
        const stats = this.getStats();
        if (size > 0) {
          console.log(
            `[ASR Pool] Status: ${size} requests queued, ${stats.activeConnections}/${stats.maxPoolSize} connections active`
          );
        } else {
          console.log(
            `[ASR Pool] Queue empty, ${stats.activeConnections}/${stats.maxPoolSize} connections active`
          );
        }

        lastReportedSize = size;
        lastReportTime = now;
      }
    });

    // Set up connection validation
    this.validateConnection = async (connection: ASRConnection) => {
      if (!connection || !connection.recognizer || !connection.audioStream) {
        return false;
      }

      try {
        // Check if the connection is disposed
        try {
          connection.recognizer.properties.getProperty('test');
        } catch (err) {
          console.log('[ASR Pool] Connection is disposed, removing from pool');
          return false;
        }

        // Verify recognizer is in valid state and connection is usable
        const isActive = connection.active;
        const hasRecognizing = typeof connection.recognizer.recognizing !== 'undefined';
        return hasRecognizing && !isActive;
      } catch (error) {
        console.error('[ASR Pool] Connection validation failed:', error);
        return false;
      }
    };
  }

  private initializeConfig(): void {
    const key = process.env.AZURE_SPEECH_KEY;
    const region = process.env.AZURE_SPEECH_REGION;

    if (!key || !region) {
      throw new Error(
        'Missing required Azure Speech configuration. Check AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables.'
      );
    }

    this.config = speechSDK.SpeechConfig.fromSubscription(key, region);
    this.config.speechRecognitionLanguage = 'cs-CZ';

    // Configure speech service properties
    this.config.setProperty('SpeechServiceConnection_SpeakerAudioLowQuality', 'true');
    this.config.setProperty('SpeechServiceConnection_MaxRetryTimeMs', '5000');
    this.config.setProperty('SpeechServiceConnection_EnableAudioPreprocessing', 'true');
    this.config.setProperty('SpeechServiceConnection_EnableBandwidthOptimization', 'true');
    this.config.setProperty('SpeechServiceConnection_InitialSilenceTimeoutMs', '10000');
    this.config.setProperty('SpeechServiceConnection_EndSilenceTimeoutMs', '5000');

    this.initialized = true;
  }

  protected async createConnection(): Promise<ASRConnection> {
    if (!this.initialized) {
      throw new Error('Speech service not initialized');
    }

    // Create audio format for 8kHz mono 16-bit PCM
    const audioFormat = speechSDK.AudioStreamFormat.getWaveFormatPCM(8000, 16, 1);

    // Create push stream with format
    const pushStream = speechSDK.AudioInputStream.createPushStream(audioFormat);

    // Create audio config from the push stream
    const audioConfig = speechSDK.AudioConfig.fromStreamInput(pushStream);

    // Create recognizer with the configured audio input
    const recognizer = new speechSDK.SpeechRecognizer(this.config, audioConfig);

    // Create connection object with state tracking
    const connection: ASRConnection = {
      recognizer,
      audioStream: pushStream,
      active: false,
      lastUsed: Date.now(),
      writeCount: 0,
    };

    await this.setupRecognizerCallbacks(recognizer, pushStream, connection);

    console.log('[ASR Pool] Created new connection with initialized audio stream');

    return connection;
  }

  private async setupRecognizerCallbacks(
    recognizer: any,
    audioStream: any,
    _connection: ASRConnection
  ): Promise<void> {
    return new Promise<void>((resolve, _reject) => {
      recognizer.recognizing = (_: any, event: any) => {
        if (event.result.text) {
          const transcript: Transcript = {
            text: event.result.text,
            confidence: 0.5,
            isFinal: false,
          };
          // Only log interim transcripts if they're substantially different
          if (transcript.text.length > 3) {
            console.log('[ASR] Interim: ' + transcript.text);
          }
          this.emitter.emit('transcript', transcript);
        }
      };

      recognizer.recognized = (_: any, event: any) => {
        if (event.result.reason === speechSDK.ResultReason.RecognizedSpeech && event.result.text) {
          const transcript: Transcript = {
            text: event.result.text,
            confidence: event.result.confidence || 0.9,
            isFinal: true,
          };
          // Log and emit the transcript, but keep recognition active
          console.log('[ASR] Final: ' + transcript.text);
          this.emitter.emit('transcript', transcript, true);
        }
      };

      recognizer.canceled = (_: any, event: any) => {
        if (event.reason === speechSDK.CancellationReason.Error) {
          console.log('[Error] Speech recognition error:', event.errorDetails);
          this.emitter.emit('error', new Error(event.errorDetails));
        }
      };

      if (audioStream.on) {
        audioStream.on('error', (error: any) => {
          console.log('[Error] Audio stream error:', error);
          this.emitter.emit('error', new Error(`Audio stream error: ${error}`));
        });
      }

      resolve();
    });
  }

  async processAudio(data: Uint8Array): Promise<void> {
    let connection: ASRConnection | null = null;
    try {
      // Use existing active connection if available
      if (this.currentConnection?.active) {
        connection = this.currentConnection;
      }

      // Get new connection if needed
      if (!connection) {
        // Get new connection
        connection = (await this.acquireConnection()) as ASRConnection;
        this.currentConnection = connection;

        console.log('[ASR Pool] Starting new recognition session');
        try {
          await this.startRecognition(connection);
          // Increased delay to ensure recognizer is fully initialized
          await new Promise(resolve => setTimeout(resolve, 500));

          if (!connection.recognizer) {
            throw new Error('Recognizer was not properly initialized');
          }

          // Verify the stream is ready
          if (!connection.audioStream || typeof connection.audioStream.write !== 'function') {
            throw new Error('Audio stream not properly initialized');
          }

          connection.active = true;
        } catch (error) {
          console.error('[ASR Pool] Failed to start recognition:', error);
          if (connection === this.currentConnection) {
            this.currentConnection = null;
          }
          throw error;
        }
      }

      const pcmData = this.convertMulawToPcm(data);
      if (!pcmData?.buffer) {
        console.warn('[ASR Pool] Invalid PCM data buffer');
        return;
      }

      try {
        // Update connection state
        connection.lastUsed = Date.now();
        connection.writeCount++;

        // Write the PCM data directly to the stream
        connection.audioStream.write(pcmData.buffer);
      } catch (writeError: unknown) {
        const errorMessage = writeError instanceof Error ? writeError.message : 'Unknown error';
        console.error('[ASR Pool] Write error:', errorMessage);
        throw new Error(`Failed to write to audio stream: ${errorMessage}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to process audio';
      console.error('[ASR Pool] Error:', errorMessage);
      this.emitter.emit('error', new Error(errorMessage));

      // Only cleanup if there was a critical error with the stream
      if (connection && errorMessage.includes('Stream closed')) {
        try {
          console.log('[ASR Pool] Critical error detected, cleaning up connection');
          await this.stopRecognition(connection);
          connection.active = false;
          if (connection === this.currentConnection) {
            this.currentConnection = null;
          }
        } catch (stopError) {
          console.error('[ASR Pool] Error stopping recognition:', stopError);
        }
      }
    }
  }

  private async startRecognition(connection: ASRConnection, retryCount = 0): Promise<void> {
    const maxRetries = 3;
    try {
      await new Promise<void>((resolve, reject) => {
        connection.recognizer.startContinuousRecognitionAsync(
          () => resolve(),
          (error: any) => reject(error)
        );
      });
    } catch (error) {
      if (retryCount < maxRetries) {
        console.log(`[ASR Pool] Retrying recognition start (attempt ${retryCount + 1})`);
        await new Promise(resolve => setTimeout(resolve, 500));
        return this.startRecognition(connection, retryCount + 1);
      }
      throw error;
    }
  }

  private async stopRecognition(connection: ASRConnection): Promise<void> {
    if (!connection?.recognizer) {
      return;
    }

    return new Promise<void>((resolve, reject) => {
      try {
        connection.recognizer.stopContinuousRecognitionAsync(
          () => {
            connection.active = false;
            resolve();
          },
          (error: Error) => reject(error)
        );
      } catch (error) {
        reject(error);
      }
    });
  }

  private convertMulawToPcm(mulawData: Uint8Array): Int16Array {
    // μ-law to linear PCM conversion table
    const MULAW_TO_PCM_TABLE = new Int16Array(256);
    for (let i = 0; i < 256; i++) {
      const mu = i ^ 0xff;
      const sign = mu & 0x80 ? -1 : 1;
      let magnitude = ((mu & 0x7f) << 1) | 1;
      magnitude = ((magnitude + 33) * 2049) >> 11;
      MULAW_TO_PCM_TABLE[i] = sign * magnitude;
    }

    const pcmData = new Int16Array(mulawData.length);
    for (let i = 0; i < mulawData.length; i++) {
      pcmData[i] = MULAW_TO_PCM_TABLE[mulawData[i]];
    }
    return pcmData;
  }

  onTranscript(callback: (transcript: Transcript, isFinal?: boolean) => void): void {
    this.emitter.on('transcript', callback);
  }

  onError(callback: (error: Error) => void): void {
    this.emitter.on('error', callback);
  }

  async dispose(): Promise<void> {
    const stats = this.getStats();
    console.log('Disposing speech service pool', stats);

    try {
      // Stop all active recognitions first
      const stopPromises = (this.pool as ASRConnection[])
        .filter(conn => conn.active)
        .map(conn =>
          this.stopRecognition(conn).catch(err =>
            console.error('[ASR Pool] Error stopping recognition during disposal:', err)
          )
        );
      await Promise.all(stopPromises);

      // Then close all resources
      for (const connection of this.pool as ASRConnection[]) {
        try {
          if (connection.recognizer) {
            connection.recognizer.close();
          }
          if (connection.audioStream) {
            connection.audioStream.close();
          }
        } catch (error) {
          console.error('[ASR Pool] Error closing connection resources:', error);
        }
      }

      // Clear current connection
      this.currentConnection = null;

      // Finally dispose the pool
      await super.dispose();
      this.emitter.removeAllListeners();
      this.initialized = false;

      console.log('[ASR Pool] Successfully disposed speech service pool');
    } catch (error) {
      console.error('[ASR Pool] Error during pool disposal:', error);
      throw error;
    }
  }
}
