import { BaseSpeechService } from '../base/base-speech-service';
import { BaseTTSService } from '../base/base-tts-service';
import { AzureSpeechFactory } from '../azure/azure-speech-factory';
import { GoogleSpeechFactory } from '../google/google-speech-factory';
import { ElevenLabsFactory } from '../elevenlabs/elevenlabs-factory';
import { TTSProvider } from '../base/types';

/**
 * Configuration interface for speech services
 */
export interface SpeechConfig {
  asrProvider: 'google' | 'azure';
  ttsProvider: TTSProvider;
  projectId?: string; // For Google Cloud
  region?: string; // For Azure
  keyFilePath?: string; // For Google Cloud credentials
  subscriptionKey?: string; // For Azure subscription
}

/**
 * Abstract factory interface for creating speech services
 */
export interface SpeechServiceFactory {
  createASRService(): Promise<BaseSpeechService>;
  createTTSService(): BaseTTSService;
}

/**
 * Service container for managing speech service instances
 */
export class ServiceContainer {
  private static instance: ServiceContainer;
  private speechFactory: SpeechServiceFactory | null = null;

  private constructor() {}

  /**
   * Get the singleton instance of ServiceContainer
   */
  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Initialize the service container with a speech configuration
   */
  public initialize(config: SpeechConfig): void {
    if (this.speechFactory) {
      throw new Error('ServiceContainer already initialized');
    }

    // Create appropriate factory based on ASR provider since STT is primary
    switch (config.asrProvider) {
      case 'google':
        this.speechFactory = new GoogleSpeechFactory(config);
        break;
      case 'azure':
        this.speechFactory = new AzureSpeechFactory(config);
        break;
      default:
        throw new Error(`Unsupported ASR provider: ${config.asrProvider}`);
    }

    // If TTS provider is different from ASR, override TTS service
    if (config.ttsProvider !== config.asrProvider) {
      const originalFactory = this.speechFactory;
      this.speechFactory = {
        createASRService: () => originalFactory.createASRService(),
        createTTSService: () => {
          switch (config.ttsProvider) {
            case TTSProvider.GOOGLE:
              return new GoogleSpeechFactory(config).createTTSService();
            case TTSProvider.AZURE:
              return new AzureSpeechFactory(config).createTTSService();
            case TTSProvider.ELEVENLABS:
              return new ElevenLabsFactory().createTTSService();
            default:
              throw new Error(`Unsupported TTS provider: ${config.ttsProvider}`);
          }
        },
      };
    }
  }

  /**
   * Get the current speech service factory
   */
  public getSpeechFactory(): SpeechServiceFactory {
    if (!this.speechFactory) {
      throw new Error('ServiceContainer not initialized');
    }
    return this.speechFactory;
  }

  /**
   * Clean up resources when changing configuration
   */
  public dispose(): void {
    this.speechFactory = null;
  }
}

/**
 * Get speech configuration from environment variables
 */
export function getSpeechConfig(): SpeechConfig {
  const asrProvider = process.env.ASR_SERVICE?.toLowerCase() as 'google' | 'azure';
  if (!asrProvider || (asrProvider !== 'google' && asrProvider !== 'azure')) {
    throw new Error("ASR_SERVICE environment variable must be 'google' or 'azure'");
  }

  const ttsProviderStr = process.env.TTS_PROVIDER?.toLowerCase() || asrProvider;
  let ttsProvider: TTSProvider;
  switch (ttsProviderStr) {
    case 'google':
      ttsProvider = TTSProvider.GOOGLE;
      break;
    case 'azure':
      ttsProvider = TTSProvider.AZURE;
      break;
    case 'elevenlabs':
      ttsProvider = TTSProvider.ELEVENLABS;
      break;
    default:
      throw new Error("TTS_PROVIDER must be 'google', 'azure', or 'elevenlabs'");
  }

  const config: SpeechConfig = { asrProvider, ttsProvider };

  if (asrProvider === 'google' || ttsProvider === TTSProvider.GOOGLE) {
    const projectId = process.env.GOOGLE_PROJECT_ID;
    const keyFilePath = process.env.GOOGLE_APPLICATION_CREDENTIALS;

    if (!projectId || !keyFilePath) {
      throw new Error(
        'Google Cloud configuration requires GOOGLE_PROJECT_ID and GOOGLE_APPLICATION_CREDENTIALS'
      );
    }

    config.projectId = projectId;
    config.keyFilePath = keyFilePath;
  }

  if (asrProvider === 'azure' || ttsProvider === TTSProvider.AZURE) {
    const subscriptionKey = process.env.AZURE_SPEECH_KEY;
    const region = process.env.AZURE_SPEECH_REGION;

    if (!subscriptionKey || !region) {
      throw new Error('Azure configuration requires AZURE_SPEECH_KEY and AZURE_SPEECH_REGION');
    }

    config.subscriptionKey = subscriptionKey;
    config.region = region;
  }

  if (ttsProvider === TTSProvider.ELEVENLABS) {
    if (!process.env.ELEVENLABS_API_KEY || !process.env.ELEVENLABS_VOICE_ID) {
      throw new Error(
        'ElevenLabs configuration requires ELEVENLABS_API_KEY and ELEVENLABS_VOICE_ID'
      );
    }
  }

  return config;
}
