import { ServiceContainer, getSpeechConfig } from './factories/speech-factory';

/**
 * Initialize the speech services during application startup
 * This ensures the ServiceContainer is ready before any services try to use it
 */
export function initializeSpeechServices(): void {
  try {
    const config = getSpeechConfig();
    const container = ServiceContainer.getInstance();

    // Initialize container with configuration
    container.initialize(config);

    console.log(`[Speech] Initialized speech services with provider: ${config.asrProvider}`);
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    console.error(`[Speech] Failed to initialize speech services: ${message}`);

    // Re-throw to prevent application startup if speech services are critical
    throw new Error(`Speech service initialization failed: ${message}`);
  }
}
