import { BaseTTSService } from '../base/base-tts-service';
import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { createExternalHttpClient } from '../../../utils/http-client';
import { logDebug, logError, logInfo } from '../../logging';

export class ElevenLabsTTSService extends BaseTTSService {
  public readonly provider = 'elevenlabs';
  private tempDir: string;
  private debugDir: string;

  constructor() {
    super();

    // Create temp directory for audio processing
    this.tempDir = path.join(process.cwd(), 'temp');
    this.debugDir = path.join(process.cwd(), 'debug-audio');

    [this.tempDir, this.debugDir].forEach(dir => {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    });
  }

  // Method removed as it was unused

  // Removed unused method

  protected async initializeService(): Promise<void> {
    try {
      logInfo('[TTS] Initializing ElevenLabs TTS service...');

      const baseUrl = 'https://api.elevenlabs.io/v1';
      const voicesUrl = `${baseUrl}/voices`;
      const client = createExternalHttpClient(voicesUrl, {
        headers: {
          'xi-api-key': process.env.ELEVENLABS_API_KEY || '',
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
      });

      // Pass the full URL to be explicit
      await client.get(voicesUrl);
      logInfo(`[TTS] Successfully initialized ElevenLabs TTS service`);
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const message = error.response.data?.detail || error.message;
        throw new Error(`ElevenLabs initialization error: ${message}`);
      }
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`ElevenLabs initialization error: ${message}`);
    }
  }

  // Removed unused method

  protected async synthesizeAudio(text: string): Promise<Uint8Array> {
    const voiceId = process.env.ELEVENLABS_VOICE_ID;
    if (!voiceId) {
      ``;
      throw new Error('ElevenLabs voice ID not properly configured');
    }

    try {
      console.log('[TTS] Synthesizing audio with ElevenLabs...');

      const baseUrl = 'https://api.elevenlabs.io/v1';
      const ttsUrl = `${baseUrl}/text-to-speech/${voiceId}?output_format=ulaw_8000`;
      const client = createExternalHttpClient(ttsUrl, {
        headers: {
          'xi-api-key': process.env.ELEVENLABS_API_KEY || '',
          'Content-Type': 'application/json',
        },
        responseType: 'arraybuffer' as const,
      });

      // Pass the full URL to be explicit
      const response = await client.post(ttsUrl, {
        text,
        model_id: 'eleven_flash_v2_5',
        language_code: 'cs',
        voice_settings: {
          similarity_boost: 0.75,
          stability: 0.5,
          style: 0.0,
          use_speaker_boost: true,
          speed: 0.9,
        },
      });

      logDebug(`[TTS] Response: ${response.status}`);

      const originalAudio = new Uint8Array(response.data);
      logDebug(`[TTS] Received audio from ElevenLabs, size ${originalAudio.length} bytes`);

      if (originalAudio.length === 0) {
        throw new Error('Received empty audio data from ElevenLabs');
      }

      return originalAudio;
    } catch (error) {
      if (axios.isAxiosError(error) && error.response) {
        const message = error.response.data?.detail || error.message;
        throw new Error(`ElevenLabs synthesis error: ${message}`);
      }
      const message = error instanceof Error ? error.message : String(error);
      throw new Error(`ElevenLabs synthesis error: ${message}`);
    }
  }

  dispose(): void {
    logInfo('[TTS] Disposed ElevenLabs TTS service');
    // Clean up temp directory
    try {
      if (fs.existsSync(this.tempDir)) {
        fs.rmSync(this.tempDir, { recursive: true, force: true });
      }
    } catch (error) {
      logError(`[TTS] Failed to cleanup temp directory`);
    }
  }
}
