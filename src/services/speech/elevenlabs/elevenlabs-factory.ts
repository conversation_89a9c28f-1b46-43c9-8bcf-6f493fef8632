import { BaseSpeechService } from '../base/base-speech-service';
import { BaseTTSService } from '../base/base-tts-service';
import { SpeechServiceFactory } from '../factories/speech-factory';
import { ElevenLabsTTSService } from './elevenlabs-tts-service';

/**
 * Factory for creating ElevenLabs speech services
 * Note: ElevenLabs only provides TTS, so ASR will throw an error
 */
export class ElevenLabsFactory implements SpeechServiceFactory {
  async createASRService(): Promise<BaseSpeechService> {
    throw new Error('ASR not supported by ElevenLabs');
  }

  createTTSService(): BaseTTSService {
    return new ElevenLabsTTSService();
  }
}
