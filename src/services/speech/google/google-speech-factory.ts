import { SpeechServiceFactory, SpeechConfig } from '../factories/speech-factory';
import { BaseSpeechService } from '../base/base-speech-service';
import { BaseTTSService } from '../base/base-tts-service';
import { GoogleSpeechService } from './google-speech-service';
import { GoogleTTSService } from './google-tts-service';

export class GoogleSpeechFactory implements SpeechServiceFactory {
  constructor(config: SpeechConfig) {
    if (!config.projectId || !config.keyFilePath) {
      throw new Error('Google Speech Factory requires projectId and keyFilePath in configuration');
    }

    // Set environment variables for Google services
    process.env.GOOGLE_PROJECT_ID = config.projectId;
    process.env.GOOGLE_APPLICATION_CREDENTIALS = config.keyFilePath;
  }

  async createASRService(): Promise<BaseSpeechService> {
    const service = new GoogleSpeechService();
    await service.initialize();
    return service;
  }

  createTTSService(): BaseTTSService {
    return new GoogleTTSService();
  }
}
