import { TextToSpeechClient } from '@google-cloud/text-to-speech';
import { BaseTTSService } from '../base/base-tts-service';
import { protos } from '@google-cloud/text-to-speech';
import { logInfo, logDebug, logError } from '../../../services/logging/logger';

export class GoogleTTSService extends BaseTTSService {
  public readonly provider = 'google';
  private client: TextToSpeechClient | null = null;

  protected async initializeService(): Promise<void> {
    try {
      logInfo('[TTS] Initializing Google TTS service...');

      if (!process.env.GOOGLE_APPLICATION_CREDENTIALS) {
        throw new Error('GOOGLE_APPLICATION_CREDENTIALS environment variable is not set');
      }

      logDebug(
        `[TTS] Creating client with credentials from: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}`
      );

      // Verify environment variables are set correctly
      logDebug('[TTS] Verifying environment configuration...');
      logDebug(
        `[TTS] Environment state: ${JSON.stringify({
          hasGoogleCredentials: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
          credentialsPath: process.env.GOOGLE_APPLICATION_CREDENTIALS,
          hasProjectId: !!process.env.GOOGLE_PROJECT_ID,
          projectId: process.env.GOOGLE_PROJECT_ID,
          hasProxy: !!process.env.HTTP_PROXY,
          proxy: process.env.HTTP_PROXY,
        })}`
      );

      // Read credentials file to verify it exists and is accessible
      try {
        const fs = require('fs');
        const credentialsContent = await fs.readFileSync(
          process.env.GOOGLE_APPLICATION_CREDENTIALS,
          'utf8'
        );
        const credentials = JSON.parse(credentialsContent);
        logDebug(
          `[TTS] Successfully read credentials file. Contains: ${JSON.stringify({
            hasPrivateKey: !!credentials.private_key,
            hasClientEmail: !!credentials.client_email,
            projectId: credentials.project_id,
            credentialsType: credentials.type,
          })}`
        );
      } catch (credError: unknown) {
        logError(
          `[TTS] Failed to read credentials file: ${
            credError instanceof Error ? credError.message : String(credError)
          }`
        );
        throw new Error(
          `Failed to read Google credentials file: ${
            credError instanceof Error ? credError.message : String(credError)
          }`
        );
      }

      // Prepare client configuration
      const clientConfig: any = {
        projectId: process.env.GOOGLE_PROJECT_ID,
        keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
      };

      // Configure proxy if set
      if (process.env.HTTP_PROXY) {
        logInfo('[TTS] Configuring proxy settings...');
        process.env.HTTPS_PROXY = process.env.HTTP_PROXY;
        clientConfig.apiEndpoint = 'texttospeech.googleapis.com';
        clientConfig.protocol = 'https';
        clientConfig.proxy = process.env.HTTP_PROXY;
        logDebug(
          `[TTS] Proxy configuration applied: ${JSON.stringify({
            HTTPS_PROXY: process.env.HTTPS_PROXY,
            apiEndpoint: clientConfig.apiEndpoint,
            proxy: clientConfig.proxy,
          })}`
        );
      }

      // Create client
      logInfo('[TTS] Attempting to create TextToSpeechClient...');
      try {
        logDebug(
          `[TTS] Client configuration: ${JSON.stringify({
            ...clientConfig,
            keyFilename: clientConfig.keyFilename, // Log path without content
          })}`
        );

        this.client = new TextToSpeechClient(clientConfig);

        if (!this.client) {
          throw new Error('TextToSpeechClient constructor returned null');
        }

        // Verify client has expected methods
        logDebug(
          `[TTS] Verifying client interface: ${JSON.stringify({
            hasInitialize: typeof this.client.initialize === 'function',
            hasListVoices: typeof this.client.listVoices === 'function',
            hasClose: typeof this.client.close === 'function',
          })}`
        );
      } catch (constructError: unknown) {
        logError(
          `[TTS] Failed to construct client: ${
            constructError instanceof Error ? constructError.message : String(constructError)
          }\nStack: ${constructError instanceof Error ? constructError.stack : ''}`
        );
        throw constructError;
      }

      if (!this.client) {
        throw new Error('Failed to create Google TTS client');
      }

      logInfo('[TTS] Client created, testing initialization...');

      try {
        logInfo('[TTS] Starting client initialization...');
        logDebug(
          `[TTS] Pre-initialize client state: ${JSON.stringify({
            clientExists: !!this.client,
            clientType: this.client ? typeof this.client : 'null',
            hasInitialize: this.client && typeof this.client.initialize === 'function',
          })}`
        );

        const initResult = await this.client.initialize();

        logDebug(
          `[TTS] Post-initialize client state: ${JSON.stringify({
            initResult,
            clientExists: !!this.client,
            clientType: this.client ? typeof this.client : 'null',
            hasInitialize: this.client && typeof this.client.initialize === 'function',
            hasListVoices: this.client && typeof this.client.listVoices === 'function',
          })}`
        );
      } catch (initError: unknown) {
        logError(
          `[TTS] Client initialization failed with error: ${
            initError instanceof Error ? initError.message : String(initError)
          }\nStack: ${
            initError instanceof Error ? initError.stack : ''
          }\nClient state: ${JSON.stringify({
            exists: !!this.client,
            type: this.client ? typeof this.client : 'null',
          })}`
        );
        this.client = null;
        throw new Error(
          `Failed to initialize Google TTS client: ${
            initError instanceof Error ? initError.message : String(initError)
          }`
        );
      }

      logInfo('[TTS] Client initialized, proceeding to test voice list...');

      // Test connection with a basic voice list request
      if (!this.client) {
        throw new Error('Google TTS client is null after initialization');
      }

      let voices: protos.google.cloud.texttospeech.v1.IVoice[] = [];

      try {
        logInfo('[TTS] Attempting to call listVoices...');
        logDebug(
          `[TTS] Client state before listVoices: ${JSON.stringify({
            isClientNull: this.client === null,
            hasListVoices: typeof this.client.listVoices === 'function',
          })}`
        );

        const [response] = await this.client.listVoices({
          languageCode: 'cs-CZ',
        });

        logInfo('[TTS] listVoices call completed successfully');

        if (!response || !response.voices) {
          throw new Error('No voices list returned from Google TTS');
        }

        voices = response.voices;
        logDebug(
          `[TTS] Received voices list: ${JSON.stringify({
            totalVoices: voices.length,
            hasVoices: voices.length > 0,
          })}`
        );

        const czechVoices = voices.filter(
          (voice: protos.google.cloud.texttospeech.v1.IVoice) =>
            voice.languageCodes?.includes('cs-CZ') && voice.name?.includes('Wavenet')
        );

        if (czechVoices.length === 0) {
          throw new Error('No Czech Wavenet voices found');
        }

        logInfo(
          `[TTS] Available Czech Wavenet voices: ${czechVoices
            .map((voice: protos.google.cloud.texttospeech.v1.IVoice) => voice.name)
            .join(', ')}`
        );
      } catch (voicesError: unknown) {
        logError(
          `[TTS] Failed to get voices list: ${
            voicesError instanceof Error ? voicesError.message : String(voicesError)
          }\nClient state: ${JSON.stringify({
            isNull: this.client === null,
            hasListVoices: this.client ? typeof this.client.listVoices === 'function' : false,
          })}`
        );
        throw voicesError;
      }

      logInfo('[TTS] Google TTS service initialized successfully');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(
        `[Error] Failed to initialize Google TTS service: ${errorMessage}\nCredentials: ${process.env.GOOGLE_APPLICATION_CREDENTIALS}\nProjectId: ${process.env.GOOGLE_PROJECT_ID}`
      );
      throw error;
    }
  }

  protected async synthesizeAudio(text: string): Promise<Uint8Array> {
    if (!this.client) {
      throw new Error('TTS service not properly initialized');
    }

    try {
      // Check cache first
      const cachedAudio = await this.getCachedAudio(text);
      if (cachedAudio) {
        return cachedAudio;
      }

      // Prepare request
      const request: protos.google.cloud.texttospeech.v1.ISynthesizeSpeechRequest = {
        input: {
          text: this.isSSML(text) ? undefined : text,
          ssml: this.isSSML(text) ? text : undefined,
        },
        voice: {
          languageCode: 'cs-CZ', // Czech language
          name: 'cs-CZ-Wavenet-A', // Czech Wavenet voice for better quality
        },
        audioConfig: {
          audioEncoding: 'MULAW', // µ-law encoding for 8-bit PCM
          sampleRateHertz: 8000, // 8kHz sample rate
        },
      };

      // Synthesize speech
      const [response] = await this.client.synthesizeSpeech(request);

      if (!response.audioContent) {
        throw new Error('No audio content received from Google TTS');
      }

      const audioData = new Uint8Array(response.audioContent as Uint8Array);

      // Cache the audio for future use
      await this.cacheAudio(text, audioData);

      return audioData;
    } catch (error) {
      logError(
        `[Error] Error in speech synthesis: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
      throw error;
    }
  }

  private isSSML(text: string): boolean {
    return text.trim().startsWith('<speak');
  }

  dispose(): void {
    try {
      if (this.client) {
        this.client.close();
        this.client = null;
      }
    } catch (error) {
      logError(
        `[Error] Failed to clean up Google TTS service: ${
          error instanceof Error ? error.message : 'Unknown error'
        }`
      );
    }
  }
}
