import { SpeechClient } from '@google-cloud/speech';
import { BaseSpeechService } from '../base/base-speech-service';
import { Transcript } from '../base/types';
import { google } from '@google-cloud/speech/build/protos/protos';
import {
  getASRStreamingMode,
  logASRRawResponses,
  logASRParsedResults,
  logASRUnstableTranscripts,
  getInterimTranscriptStabilityThreshold,
} from '../../../common/environment-variables';
import { logInfo, logDebug, logError } from '../../../services/logging/logger';
import { loggingConfig, shouldLogMessage } from '../../../services/logging/logging-config';

// Constants for speech detection

export class GoogleSpeechService extends BaseSpeechService {
  private disposed = false;
  private client: SpeechClient | null = null;
  private recognizeStream: any = null;
  // Removed unused audioBuffer
  constructor() {
    super();
  }

  /**
   * Async initialization for GoogleSpeechService.
   * Must be called before using the service.
   */
  async initialize(): Promise<void> {
    if (this.client && this.state === 'Ready') {
      logDebug('[ASR] Google Speech service already initialized, skipping re-initialization.');
      return;
    }
    try {
      logInfo('[ASR] Initializing Google Speech service...');
      this.client = new SpeechClient({
        keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
      });
      this.state = 'Ready';
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Failed to initialize speech service: ${errorMessage}`);
      this.emitError(errorMessage);
      this.state = 'Error';
      throw error;
    }
  }

  /**
   * Emit event when no speech is detected for a period of time
   */
  protected emitSpeechTimeout(): void {
    this.emitter.emit('speech-timeout');
  }

  // (initializeSpeechService removed; all initialization is now async via initialize())

  private async sendAudioChunk(chunk: Uint8Array): Promise<void> {
    if (!this.recognizeStream) {
      throw new Error('Stream not initialized');
    }

    const writeSuccess = this.recognizeStream.write(chunk);
    if (!writeSuccess) {
      await new Promise<void>(resolve => {
        const timeout = setTimeout(() => {
          this.recognizeStream?.removeListener('drain', drainHandler);
          resolve();
        }, 100);

        const drainHandler = () => {
          clearTimeout(timeout);
          resolve();
        };

        this.recognizeStream!.once('drain', drainHandler);
      });
    }
  }

  async processAudio(data: Uint8Array): Promise<void> {
    if (!this.client || this.state === 'Error') {
      throw new Error('Speech service not properly initialized');
    }

    try {
      // If we're in TTS playback and barge-in is disabled, don't buffer or process audio
      if (this.isIgnoringAudioInput) {
        // Only log this occasionally to reduce noise
        if (shouldLogMessage('asrIgnoringInput', loggingConfig.asrAudioChunkInterval)) {
          logDebug('[ASR] Google Speech ignoring audio input due to isIgnoringAudioInput flag');
        }
        return;
      }

      // Log audio processing only periodically to reduce noise
      if (shouldLogMessage('asrProcessingAudio', loggingConfig.asrAudioChunkInterval)) {
        logDebug('[ASR] Google Speech processing audio chunk of size: ' + data.length + ' bytes');
      }

      // Create new stream if needed
      if (!this.recognizeStream) {
        logInfo('[ASR] No recognition stream exists, creating a new one');
        this.setupRecognizeStream();
      }

      // Update last audio timestamp
      this.lastAudioTimestamp = Date.now();

      // Process current audio chunk
      if (this.recognizeStream) {
        // Setup heartbeat monitoring if not already running
        if (!this.streamHeartbeatInterval) {
          logInfo('[ASR] Setting up heartbeat monitoring for the stream');
          this.setupHeartbeatMonitoring();
        }

        // Only log audio sending periodically
        if (
          loggingConfig.logDetailedASRProcessing &&
          shouldLogMessage('asrSendingAudio', loggingConfig.asrAudioChunkInterval)
        ) {
          logDebug('[ASR] Sending audio chunk to Google Speech API');
        }

        await this.sendAudioChunk(data);
        this.state = 'Processing';

        // Only log success periodically
        if (
          loggingConfig.logDetailedASRProcessing &&
          shouldLogMessage('asrAudioSent', loggingConfig.asrAudioChunkInterval)
        ) {
          logDebug('[ASR] Audio chunk sent successfully, state set to Processing');
        }
      } else {
        logError('[ASR] Failed to create recognition stream');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Error processing audio: ${errorMessage}`);
      this.emitError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  private restartStream(): void {
    logInfo('[ASR] Restarting recognition stream');
    if (this.recognizeStream) {
      this.recognizeStream.end();
      this.recognizeStream = null;
    }
    this.setupRecognizeStream();
  }

  private streamHeartbeatInterval: NodeJS.Timeout | null = null;
  private lastAudioTimestamp = 0;

  private setupHeartbeatMonitoring(): void {
    // Clear any existing interval
    if (this.streamHeartbeatInterval) {
      clearInterval(this.streamHeartbeatInterval);
      this.streamHeartbeatInterval = null;
    }

    const STREAM_REFRESH_THRESHOLD = 30000; // 30 seconds - longer timeout for conversation gaps
    const SILENCE_CHECK_INTERVAL = 5000; // Check every 5 seconds

    // let inSpeechSegment = false; // Removed: unused variable

    // Set up new heartbeat monitoring
    this.streamHeartbeatInterval = setInterval(() => {
      const timeSinceLastAudio = Date.now() - this.lastAudioTimestamp;

      // Don't monitor during TTS playback
      if (this.isIgnoringAudioInput) {
        return;
      }

      // If there's been complete silence for too long, restart stream
      if (timeSinceLastAudio > STREAM_REFRESH_THRESHOLD) {
        logInfo(
          `[ASR] Extended silence detected (${STREAM_REFRESH_THRESHOLD}ms), refreshing stream`
        );
        this.restartStream();
        // inSpeechSegment = false; // Removed: variable is unused
      }
    }, SILENCE_CHECK_INTERVAL);
  }

  // Method removed as it was unused

  private setupRecognizeStream(): void {
    if (!this.client) {
      return;
    }

    // Clear any existing heartbeat interval
    if (this.streamHeartbeatInterval) {
      clearInterval(this.streamHeartbeatInterval);
      this.streamHeartbeatInterval = null;
    }

    // End existing stream if any
    if (this.recognizeStream) {
      this.recognizeStream.end();
      this.recognizeStream = null;
    }

    // Reset audio timestamp
    this.lastAudioTimestamp = Date.now();

    // Get the ASR streaming mode configuration
    const streamingMode = getASRStreamingMode();

    // Create the base configuration
    const config: google.cloud.speech.v1.IRecognitionConfig = {
      encoding: 'MULAW',
      sampleRateHertz: 8000,
      languageCode: 'cs-CZ',
      enableAutomaticPunctuation: true,
      model: 'latest_long',
      useEnhanced: true,
      metadata: {
        interactionType: 'PHONE_CALL',
        microphoneDistance: 'NEARFIELD',
        originalMediaType: 'AUDIO',
        recordingDeviceType: 'PHONE_LINE',
      },
      maxAlternatives: 1,
      enableWordTimeOffsets: false,
    };

    // Adjust configuration based on streaming mode
    if (streamingMode === 'continuous') {
      logInfo('[ASR] Configuring Google Speech for continuous streaming mode');
      // Add speech contexts to improve recognition
      config.speechContexts = [
        {
          phrases: [],
          boost: 0,
        },
      ];
      // Use very relaxed endpointing to minimize final results
      // @ts-ignore - endpointing is a valid property but not in the type definition
      config.endpointing = 'ENDPOINTING_CONFIG_VERY_RELAXED';
    } else if (streamingMode === 'hybrid') {
      logInfo('[ASR] Configuring Google Speech for hybrid streaming mode');
      // Use moderately relaxed endpointing for hybrid mode
      // @ts-ignore - endpointing is a valid property but not in the type definition
      config.endpointing = 'ENDPOINTING_CONFIG_RELAXED';
    } else {
      logInfo('[ASR] Configuring Google Speech for standard streaming mode');
      // Standard configuration - no changes needed
    }

    const request: google.cloud.speech.v1.IStreamingRecognitionConfig = {
      config,
      interimResults: true,
      singleUtterance: false, // Disable single utterance mode to maintain conversation context
    };

    logDebug(`[ASR] Creating new stream with config: ${JSON.stringify(request.config, null, 2)}`);

    this.recognizeStream = this.client
      .streamingRecognize(request)
      .on('error', (error: Error) => {
        if (this.disposed) {
          // Suppress errors after disposal
          return;
        }
        logError(`[ASR] Stream recognition error: ${error}`);

        // Clean up the current stream
        if (this.recognizeStream) {
          this.recognizeStream.removeAllListeners();
          this.recognizeStream.end();
          this.recognizeStream = null;
        }

        // Only recreate stream for fatal errors
        const isFatalError =
          !error.message.includes('Timeout') &&
          !error.message.includes('duration') &&
          !error.message.includes('Long duration elapsed without audio');

        if (isFatalError) {
          logInfo('[ASR] Fatal error detected, will recreate stream');
          this.emitError(error.message);

          setTimeout(() => {
            if (!this.isIgnoringAudioInput) {
              logInfo('[ASR] Creating new stream after fatal error');
              this.setupRecognizeStream();
            }
          }, 1000);
        } else {
          // For timeout errors, just log and continue
          logInfo('[ASR] Non-fatal error detected, continuing with current stream');
        }
      })
      .on('data', (response: google.cloud.speech.v1.StreamingRecognizeResponse) => {
        // Only log raw responses if enabled
        if (logASRRawResponses()) {
          logDebug(`[ASR] [GoogleSpeechService] Raw Google response: ${JSON.stringify(response)}`);
        }

        // Only log the full response if detailed ASR processing is enabled
        if (loggingConfig.logDetailedASRProcessing) {
          logDebug(`[ASR] Received recognition response: ${JSON.stringify(response, null, 2)}`);
        }

        // Process only if we have results
        if (!response.results?.length) {
          return;
        }

        // Accumulate all results to get complete context
        let finalTranscript = '';
        let interimTranscript = '';
        let highestConfidence = 0;

        for (const result of response.results) {
          const alternative = result.alternatives?.[0];
          if (!alternative?.transcript) {
            continue;
          }

          // Only log parsed results if enabled
          if (logASRParsedResults()) {
            logDebug(
              `[ASR] [GoogleSpeechService] Parsed result: text="${alternative.transcript}", isFinal=${result.isFinal}, stability=${result.stability}, confidence=${alternative.confidence}`
            );
          }

          if (result.isFinal) {
            finalTranscript = alternative.transcript;
            if (typeof alternative.confidence === 'number') {
              highestConfidence = alternative.confidence;
            }
            logInfo(`[ASR] Final transcript from Google: "${finalTranscript}"`);
          } else if (
            result.stability &&
            result.stability > getInterimTranscriptStabilityThreshold()
          ) {
            // Using configurable threshold for faster barge-in
            interimTranscript = alternative.transcript;
            // Only log stable interim results
            logDebug(`[ASR] Stable interim transcript: "${interimTranscript}"`);
          } else if (logASRUnstableTranscripts()) {
            // Only log unstable interim results if explicitly enabled
            logDebug(`[ASR] Unstable interim transcript: "${alternative.transcript}"`);
          }
        }

        // Emit final result if we have one
        if (finalTranscript) {
          // Get the ASR streaming mode configuration
          const streamingMode = getASRStreamingMode();

          // In continuous mode, we NEVER emit final transcripts directly
          if (streamingMode === 'continuous') {
            // In continuous mode, we still emit the final transcript as an INTERIM transcript
            // This ensures the pause detection mechanism has text to work with
            const transcript: Transcript = {
              text: finalTranscript,
              confidence: highestConfidence || undefined,
            };

            logDebug(`[ASR] Emitting as interim in continuous mode: "${finalTranscript}"`);
            this.emitTranscript(transcript, false);
          } else {
            // In standard or hybrid mode, emit final transcripts normally
            const transcript: Transcript = {
              text: finalTranscript,
              confidence: highestConfidence || undefined,
            };

            logDebug(`[ASR] Emitting as final transcript: "${finalTranscript}"`);
            this.emitTranscript(transcript, true);
          }

          // Don't create a new stream - let it continue for the conversation
        }
        // Emit interim result if we have a stable one
        else if (interimTranscript) {
          const transcript: Transcript = {
            text: interimTranscript,
            confidence: undefined,
          };

          // Only log at debug level to reduce noise
          if (loggingConfig.logDetailedASRProcessing) {
            logDebug(`[ASR] Emitting stable interim transcript: "${interimTranscript}"`);
          }
          this.emitTranscript(transcript, false);
        }
      })
      .on('end', () => {
        logInfo('[ASR] Stream recognition ended');
        this.recognizeStream = null;
      });
  }

  async dispose(): Promise<void> {
    this.disposed = true;
    try {
      // Clean up heartbeat interval
      if (this.streamHeartbeatInterval) {
        clearInterval(this.streamHeartbeatInterval);
        this.streamHeartbeatInterval = null;
      }

      // Clean up stream
      if (this.recognizeStream) {
        this.recognizeStream.removeAllListeners(); // Remove all event listeners
        this.recognizeStream.end();
        this.recognizeStream = null;
      }

      // Clean up client
      if (this.client) {
        await this.client.close();
        this.client = null;
      }

      // Removed unused audioBuffer reset
      this.state = 'None';
      this.lastAudioTimestamp = 0;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`[ASR] Error disposing speech service: ${errorMessage}`);
      throw new Error(errorMessage);
    }
  }
}
