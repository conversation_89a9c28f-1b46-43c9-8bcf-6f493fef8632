import { SpeechServiceFactory, SpeechConfig } from '../factories/speech-factory';
import { BaseSpeechService } from '../base/base-speech-service';
import { BaseTTSService } from '../base/base-tts-service';
import { AzureSpeechService } from './azure-speech-service';
import { AzureTTSService } from './azure-tts-service';

export class AzureSpeechFactory implements SpeechServiceFactory {
  constructor(config: SpeechConfig) {
    if (!config.subscriptionKey || !config.region) {
      throw new Error('Azure Speech Factory requires subscriptionKey and region in configuration');
    }

    // Set environment variables for Azure services
    process.env.AZURE_SPEECH_KEY = config.subscriptionKey;
    process.env.AZURE_SPEECH_REGION = config.region;
  }

  async createASRService(): Promise<BaseSpeechService> {
    return new AzureSpeechService();
  }

  createTTSService(): BaseTTSService {
    return new AzureTTSService();
  }
}
