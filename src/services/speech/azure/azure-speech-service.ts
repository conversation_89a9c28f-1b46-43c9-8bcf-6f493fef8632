import { BaseSpeechService } from '../base/base-speech-service';
import { Transcript } from '../base/types';

// @ts-ignore - Azure Speech SDK types are not available
// This require cannot be converted to an import due to SDK compatibility issues
const speechSDK = require('microsoft-cognitiveservices-speech-sdk');

export class AzureSpeechService extends BaseSpeechService {
  private recognizer: any;
  private isProcessing = false;

  constructor() {
    super();
    this.initializeSpeechService();
  }

  private initializeSpeechService(): void {
    try {
      const key = process.env.AZURE_SPEECH_KEY;
      const region = process.env.AZURE_SPEECH_REGION;

      if (!key || !region) {
        throw new Error(
          'Missing required Azure Speech configuration. Check AZURE_SPEECH_KEY and AZURE_SPEECH_REGION environment variables.'
        );
      }

      const speechConfig = speechSDK.SpeechConfig.fromSubscription(key, region);
      speechConfig.speechRecognitionLanguage = 'cs-CZ';

      const audioFormat = speechSDK.AudioStreamFormat.getWaveFormatPCM(8000, 8, 1);
      const audioConfig = speechSDK.AudioConfig.fromStreamInput(
        () => new speechSDK.PushAudioInputStream(audioFormat)
      );

      this.recognizer = new speechSDK.SpeechRecognizer(speechConfig, audioConfig);

      this.setupRecognitionEvents();
      this.state = 'Ready';
      console.log('[ASR] Azure Speech service initialized');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[ASR] Failed to initialize speech service:', errorMessage);
      this.emitError(errorMessage);
    }
  }

  private setupRecognitionEvents(): void {
    if (!this.recognizer) {
      return;
    }

    this.recognizer.recognized = (_sender: any, event: any) => {
      const result = event.result;
      if (result.reason === speechSDK.ResultReason.RecognizedSpeech) {
        const transcript: Transcript = {
          text: result.text,
          confidence: result.confidence,
        };
        this.emitTranscript(transcript, true);
      }
    };

    this.recognizer.recognizing = (_sender: any, event: any) => {
      const result = event.result;
      if (result.reason === speechSDK.ResultReason.RecognizingSpeech) {
        const transcript: Transcript = {
          text: result.text,
        };
        this.emitTranscript(transcript, false);
      }
    };

    this.recognizer.canceled = (_sender: any, event: any) => {
      if (event.reason === speechSDK.CancellationReason.Error) {
        this.emitError(`Speech recognition canceled: ${event.errorDetails}`);
      }
    };
  }

  async processAudio(data: Uint8Array): Promise<void> {
    if (!this.recognizer || this.state === 'Error') {
      throw new Error('Speech service not properly initialized');
    }

    try {
      // If we're ignoring audio input, stop recognition if it's running
      if (this.isIgnoringAudioInput && this.isProcessing) {
        console.log('[ASR] Stopping recognition due to audio input being ignored');
        await this.recognizer.stopContinuousRecognitionAsync();
        this.isProcessing = false;
        return;
      }

      // Start recognition if we're not ignoring input and not already processing
      if (!this.isIgnoringAudioInput && !this.isProcessing) {
        console.log('[ASR] Starting continuous recognition');
        await this.recognizer.startContinuousRecognitionAsync();
        this.isProcessing = true;
        this.state = 'Processing';
      }

      // Only push audio data if we're processing and not ignoring input
      if (this.isProcessing && !this.isIgnoringAudioInput && this.recognizer.pushStream) {
        this.recognizer.pushStream.write(data);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.error('[ASR] Error processing audio:', errorMessage);
      this.emitError(errorMessage);
      throw new Error(errorMessage);
    }
  }

  async dispose(): Promise<void> {
    if (this.recognizer) {
      try {
        if (this.isProcessing) {
          await this.recognizer.stopContinuousRecognitionAsync();
          this.isProcessing = false;
        }
        this.recognizer.close();
        this.recognizer = null;
        this.state = 'None';
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        console.error('[ASR] Error disposing speech service:', errorMessage);
        throw new Error(errorMessage);
      }
    }
  }
}
