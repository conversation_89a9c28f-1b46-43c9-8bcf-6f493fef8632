/**
 * Available TTS providers
 */
export enum TTSProvider {
  GOOGLE = 'google',
  AZURE = 'azure',
  ELEVENLABS = 'elevenlabs',
}

/**
 * TTS service configuration options
 */
export interface TTSConfig {
  provider: TTSProvider;
  voiceId?: string; // Required for ElevenLabs
  languageCode?: string; // Required for Google/Azure
}

/**
 * Possible states for a speech service
 */
export type SpeechServiceState = 'None' | 'Ready' | 'Processing' | 'Error';

/**
 * Structure representing a speech transcript
 */
export interface Transcript {
  text: string;
  confidence?: number;
}

/**
 * Event handlers for speech service events
 */
export interface SpeechServiceEvents {
  transcript: (transcript: Transcript) => void;
  'final-transcript': (transcript: Transcript) => void;
  error: (error: Error | string) => void;
  'speech-timeout': () => void;
}
