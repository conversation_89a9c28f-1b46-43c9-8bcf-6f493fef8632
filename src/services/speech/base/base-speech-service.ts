import EventEmitter from 'events';
import { SpeechServiceState, SpeechServiceEvents, Transcript } from './types';

// import { logDebug } from '../../logging/logger'; // Unused import removed

/**
 * Abstract base class for speech recognition services
 * Provides common functionality and defines the interface that all speech services must implement
 */
export abstract class BaseSpeechService {
  protected emitter: EventEmitter;
  protected state: SpeechServiceState;
  protected isIgnoringAudioInput = false;

  constructor() {
    this.emitter = new EventEmitter();
    this.state = 'None';
  }

  /**
   * Get the last final transcript (default: null, override in subclasses)
   */
  getLastTranscript(): Transcript | null {
    return null;
  }

  /**
   * Start ignoring audio input (e.g. during TTS playback)
   */
  startIgnoringAudioInput(): void {
    this.isIgnoringAudioInput = true;
    console.log('[ASR] Base Speech Service: Started ignoring audio input');
  }

  /**
   * Stop ignoring audio input and resume processing
   */
  stopIgnoringAudioInput(): void {
    this.isIgnoringAudioInput = false;
    // logDebug('[ASR] Base Speech Service: Stopped ignoring audio input (processing enabled)');
  }

  /**
   * Process a chunk of audio data for speech recognition
   * @param data Raw audio data as Uint8Array (8kHz PCMU/µ-law format)
   */
  abstract processAudio(data: Uint8Array): Promise<void>;

  /**
   * Clean up resources used by the speech service
   */
  abstract dispose(): void;

  /**
   * Add an event listener for speech service events
   * @param event Event name ('transcript', 'final-transcript', or 'error')
   * @param listener Callback function to handle the event
   */
  on<K extends keyof SpeechServiceEvents>(event: K, listener: SpeechServiceEvents[K]): this {
    this.emitter.addListener(event, listener);
    return this;
  }

  /**
   * Remove an event listener
   * @param event Event name
   * @param listener Callback function to remove
   */
  off<K extends keyof SpeechServiceEvents>(event: K, listener: SpeechServiceEvents[K]): this {
    this.emitter.removeListener(event, listener);
    return this;
  }

  /**
   * Get the current state of the speech service
   */
  getState(): SpeechServiceState {
    return this.state;
  }

  /**
   * Emit a transcript event (protected helper method)
   */
  protected emitTranscript(transcript: Transcript, isFinal = false): void {
    const eventName = isFinal ? 'final-transcript' : 'transcript';
    this.emitter.emit(eventName, transcript);
  }

  /**
   * Emit an error event (protected helper method)
   */
  protected emitError(error: Error | string): void {
    this.state = 'Error';
    this.emitter.emit('error', error);
  }
}
