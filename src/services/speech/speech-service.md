# Speech Service: Technical Overview

## Purpose

The Speech Service provides a unified interface for both speech recognition (ASR) and text-to-speech (TTS) functionality, supporting multiple providers (Azure, Google, ElevenLabs) through a factory-based architecture. It handles audio processing, speech synthesis, and provider-specific configuration.

---

## Key Responsibilities

- Provide speech recognition (ASR) functionality
- Provide text-to-speech (TTS) functionality
- Support multiple providers (Azure, Google, ElevenLabs)
- Handle provider-specific configuration and initialization
- Manage audio format conversion and caching
- Provide a factory-based architecture for service creation

---

## Core Components and Their Roles

### Base Classes

#### BaseSpeechService (`src/services/speech/base/base-speech-service.ts`)

- Abstract base class for speech recognition services
- Defines common interface and functionality
- Provides event handling and state management

#### BaseTTSService (`src/services/speech/base/base-tts-service.ts`)

- Abstract base class for text-to-speech services
- Provides audio caching and file management
- Defines common interface for TTS services

### Provider Implementations

#### Azure Speech Services

- `AzureSpeechService`: Implements Azure speech recognition
- `AzureTTSService`: Implements Azure text-to-speech
- `AzureSpeechFactory`: Creates Azure-specific service instances

#### Google Speech Services

- `GoogleSpeechService`: Implements Google speech recognition
- `GoogleTTSService`: Implements Google text-to-speech
- `GoogleSpeechFactory`: Creates Google-specific service instances

#### ElevenLabs Services

- `ElevenLabsTTSService`: Implements ElevenLabs text-to-speech
- `ElevenLabsFactory`: Creates ElevenLabs-specific service instances

### Factory System

#### SpeechFactory (`src/services/speech/factories/speech-factory.ts`)

- Abstract factory interface for creating speech services
- Defines methods for creating ASR and TTS services

#### ServiceContainer

- Singleton container for managing speech factories
- Provides access to the appropriate factory based on configuration
- Handles initialization and configuration management

---

## Provider Support

The Speech Service supports the following providers:

### ASR Providers

- **Azure Cognitive Services**: Microsoft's speech recognition service
- **Google Cloud Speech**: Google's speech recognition service

### TTS Providers

- **Azure Cognitive Services**: Microsoft's text-to-speech service
- **Google Cloud TTS**: Google's text-to-speech service
- **ElevenLabs**: High-quality voice synthesis service

---

## Component Interactions

1. **Configuration**: Speech service is configured with provider and settings
2. **Initialization**: ServiceContainer creates the appropriate factory
3. **Service Creation**: Factory creates provider-specific service instances
4. **Audio Processing**: Services process audio data or synthesize speech
5. **Event Handling**: ASR services emit transcript events
6. **Resource Cleanup**: Services properly dispose of resources when done

---

## Configuration Options

The Speech Service can be configured through environment variables:

- `SPEECH_SERVICE`: Provider selection for ASR ('azure' or 'google')
- `TTS_PROVIDER`: Provider selection for TTS ('azure', 'google', or 'elevenlabs')
- `AZURE_SPEECH_KEY`: Azure subscription key
- `AZURE_SPEECH_REGION`: Azure region
- `GOOGLE_APPLICATION_CREDENTIALS`: Path to Google credentials file
- `ELEVENLABS_API_KEY`: ElevenLabs API key
- `ELEVENLABS_VOICE_ID`: ElevenLabs voice ID

---

## Design Principles

- **Abstract Factory Pattern**: Flexible provider selection and creation
- **Dependency Injection**: Services receive their dependencies at creation
- **Caching**: TTS services cache audio to improve performance
- **Event-Driven Architecture**: ASR services emit events for transcripts
- **Resource Management**: Services properly initialize and clean up resources

---

## File Structure

- `src/services/speech/`
  - `base/` – Base classes and interfaces
    - `base-speech-service.ts` – Base ASR service
    - `base-tts-service.ts` – Base TTS service
    - `types.ts` – Common type definitions
  - `azure/` – Azure-specific implementations
    - `azure-speech-service.ts` – Azure ASR
    - `azure-tts-service.ts` – Azure TTS
    - `azure-speech-factory.ts` – Azure factory
  - `google/` – Google-specific implementations
    - `google-speech-service.ts` – Google ASR
    - `google-tts-service.ts` – Google TTS
    - `google-speech-factory.ts` – Google factory
  - `elevenlabs/` – ElevenLabs-specific implementations
    - `elevenlabs-tts-service.ts` – ElevenLabs TTS
    - `elevenlabs-factory.ts` – ElevenLabs factory
  - `factories/` – Factory system
    - `speech-factory.ts` – Abstract factory and container

---

## Usage Example

```typescript
// Get speech configuration
const config = getSpeechConfig();

// Initialize service container
const container = ServiceContainer.getInstance();
container.initialize(config);

// Create ASR service
const asrService = await container.getSpeechFactory().createASRService();

// Set up event handlers
asrService.on('transcript', transcript => {
  console.log(`Interim transcript: ${transcript.text}`);
});

asrService.on('final-transcript', transcript => {
  console.log(`Final transcript: ${transcript.text}`);
});

// Process audio data
await asrService.processAudio(audioData);

// Create TTS service
const ttsService = container.getSpeechFactory().createTTSService();

// Convert text to speech
const audioBytes = await ttsService.getAudioBytes('Hello, world!');

// Clean up resources
asrService.dispose();
ttsService.dispose();
```
