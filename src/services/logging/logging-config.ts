/**
 * Logging Configuration
 *
 * This file contains configuration options for controlling log verbosity
 * and frequency to reduce noise in the logs while maintaining important information.
 */

import {
  logASRRawResponses,
  logASRParsedResults,
  logASRUnstableTranscripts,
} from '../../common/environment-variables';

// General logging configuration
export const loggingConfig = {
  // How often to log repeated messages (in milliseconds)
  // Set to 0 to log every occurrence
  botServiceCheckInterval: 5000, // Log "Checking if bot exists" every 5 seconds
  asrAudioChunkInterval: 2000, // Log audio chunk processing every 2 seconds
  webSocketMessageInterval: 1000, // Log WebSocket messages every 1 second

  // Whether to log every state transition or only important ones
  logAllStateTransitions: true, // Changed to true to see all state transitions

  // Whether to log detailed ASR processing information
  logDetailedASRProcessing: false,

  // Whether to log duplicate transcript checks
  logDuplicateChecks: false,

  // Whether to log ping messages
  logPingMessages: false,

  // Whether to log detailed state machine action execution
  logStateActionExecution: true,

  // Whether to log raw Google Speech API responses
  get logRawGoogleResponses() {
    return logASRRawResponses();
  },

  // Whether to log parsed Google Speech results
  get logParsedGoogleResults() {
    return logASRParsedResults();
  },

  // Whether to log unstable interim transcripts
  get logUnstableInterimTranscripts() {
    return logASRUnstableTranscripts();
  },
};

// Cache for tracking when messages were last logged
const messageTimestamps: Record<string, number> = {};

/**
 * Check if a message should be logged based on its frequency
 * @param key Unique identifier for the message type
 * @param interval Minimum interval between logs in milliseconds
 * @returns true if the message should be logged, false otherwise
 */
export function shouldLogMessage(key: string, interval: number): boolean {
  if (interval <= 0) {
    return true; // Always log if interval is 0 or negative
  }

  const now = Date.now();
  const lastTime = messageTimestamps[key] || 0;

  if (now - lastTime >= interval) {
    messageTimestamps[key] = now;
    return true;
  }

  return false;
}

/**
 * Reset the timestamp for a message key
 * Useful when you want to force the next occurrence to be logged
 */
export function resetMessageTimestamp(key: string): void {
  delete messageTimestamps[key];
}
