# Logging System Guide

This document is the canonical and sole guide for the application's logging system, implemented in [`src/services/logging/logger.ts`](logger.ts). All logging usage, configuration, and extension should reference this file and the logger implementation directly.

---

## Architecture Overview

- **Canonical Implementation:** [`src/services/logging/logger.ts`](logger.ts)
- **Configuration:** [`src/services/logging/logging-config.ts`](logging-config.ts)
- **No compatibility layers or alternate logging entrypoints.**
> **Note:** The `logMetrics` function is intended for emitting human-readable log messages related to metrics events. It is **not** a mechanism for structured metrics storage or analysis. For all metrics collection and aggregation, use the appropriate metrics adapter or logger as described in the monitoring subsystem documentation.
> **Update:** For the full metrics data model and correct usage of metrics adapters/loggers, see [performance-metrics.md](../monitoring/performance-metrics.md#metrics-data-model).
> Logging utilities (including `logMetrics`) are for human-readable output only and must not be used for structured metrics storage, aggregation, or analysis.

All logging in the application should import directly from `src/services/logging/logger.ts`:

```typescript
import {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
  logHttpRequest,
  logHttpResponse,
  logOpenAIRequest,
  logOpenAIResponse,
  logWebSocketMessage,
  logASRInterim,
  logStateTransition,
  flushSuppressedLogs,
  prepareMetadataForLogging,
  colors,
  LOG_LEVEL,
  currentLogLevel,
} from './logger';
```

---

- **Color-coded, timestamped logs** for readability.
- **Hierarchical log levels:** DEBUG, INFO, METRICS, WARN, ERROR.
- **Log flooding protection:** Suppresses repeated identical messages, logs a summary on change or flush.
- **Category-specific logging** (HTTP, OpenAI, WebSocket, ASR) controlled by environment variables.
- **State transition logging** with distinct formatting.
- **Metadata preparation:** Whitelists and summarizes metadata for safe logging.
- **Flexible configuration** via environment variables and [`logging-config.ts`](logging-config.ts).
- **Helper utilities** for formatting and truncation.

---

## Log Levels

Set the log level globally using environment variables:

- `LOG_LEVEL` (string): `DEBUG`, `INFO`, `METRICS`, `WARN`, `ERROR`
- `METRICS_LOG_LEVEL` (number): `0` (DEBUG), `1` (INFO), `2` (METRICS, default), `3` (WARN), `4` (ERROR)

The lowest of the two is used. Example:

```bash
LOG_LEVEL=DEBUG npm start
# or
METRICS_LOG_LEVEL=0 npm start
```

---

## Usage

### Basic Logging

```typescript
logDebug('Detailed debug info');
logInfo('General information');
logMetrics('Metrics or performance data');
logWarning('A warning message');
logError('An error occurred');
```

### Category-Specific Logging

Enable these with environment variables:

- `LOG_HTTP_REQUESTS=true`
- `LOG_HTTP_RESPONSES=true`
- `LOG_OPENAI_REQUESTS=true`
- `LOG_OPENAI_RESPONSES=true`
- `LOG_WEBSOCKET_MESSAGES=true`
- `LOG_ASR_INTERIM=true`

Example:

```typescript
logHttpRequest('Sending request to API');
logOpenAIRequest('Sending request to OpenAI');
logWebSocketMessage('Received message from client');
logASRInterim('Interim transcript: Hello');
```

### State Transition Logging

For logging state changes with metadata:

```typescript
logStateTransition('idle', 'processing', 'StateManager', { reason: 'user input' });
```

---

## Log Flooding Protection

Repeated identical messages at each log level are suppressed to avoid log flooding. Only the first occurrence is logged; repeats are counted and a summary is logged when a new message arrives or on flush.

**Flush suppressed logs on shutdown:**

```typescript
flushSuppressedLogs();
```

---

## Metadata Preparation

To safely log metadata (excluding large/binary fields):

```typescript
const safeMeta = prepareMetadataForLogging(metadata);
logInfo(`Some event: ${JSON.stringify(safeMeta)}`);
```

---

## Helper Functions

- `truncateString(str, maxLength)` – Truncates long strings for log output.
- `formatSystemPrompt(prompt)` – Formats and truncates system prompts for logging.

---

## Configuration

See [`logging-config.ts`](logging-config.ts) for advanced options:

- Control log frequency for specific categories (e.g., ASR, WebSocket) via interval settings.
- Toggle detailed logging for state transitions, ASR, duplicate checks, etc.
- Use `shouldLogMessage(key, interval)` to rate-limit custom log types.

Example:

```typescript
import { loggingConfig, shouldLogMessage } from './logging-config';

if (shouldLogMessage('botServiceCheck', loggingConfig.botServiceCheckInterval)) {
  logInfo('Checking if bot exists...');
}
```

---

## Example

```typescript
import {
  logDebug,
  logInfo,
  logMetrics,
  logWarning,
  logError,
  logOpenAIRequest,
  logOpenAIResponse,
} from './logger';

function processRequest(request) {
  logInfo(`Processing request: ${request.id}`);

  try {
    logDebug(`Request details: ${JSON.stringify(request)}`);
    logOpenAIRequest(`Sending request to OpenAI: ${request.prompt}`);
    // ... processing logic ...
    logMetrics(`Request ${request.id} processed in ${request.duration}ms`);
    return { success: true };
  } catch (error) {
    logError(`Failed to process request: ${error instanceof Error ? error.message : 'Unknown error'}`);
    return { success: false };
  }
}
```

---

## Testing and Verification

- Set log level and category variables as needed.
- Run the application and verify log output, color coding, and suppression behavior.
- Use `flushSuppressedLogs()` before shutdown to ensure all summaries are logged.

---

## Extending

- Add new log categories or helpers in [`logger.ts`](logger.ts) as needed.
- Update this guide to reflect any changes.

---

## Summary

- **Canonical logger:** [`src/services/logging/logger.ts`](logger.ts)
- **Configuration:** [`src/services/logging/logging-config.ts`](logging-config.ts)
- **No other documentation files for logging exist.**
- **All usage, configuration, and extension should reference this guide and the canonical logger.**
