declare module '@modelcontextprotocol/sdk' {
  export class Server {
    constructor(config: { name: string; version: string });
    setRequestHandler(schema: unknown, handler: (request: any) => Promise<any>): void;
    connect(transport: any): Promise<void>;
    get name(): string;
    get version(): string;
  }

  export class StdioServerTransport {
    constructor();
    connect(): Promise<void>;
  }

  export class McpError extends Error {
    constructor(code: ErrorCode, message: string);
    code: ErrorCode;
  }

  export enum ErrorCode {
    ParseError = 'parse_error',
    InvalidRequest = 'invalid_request',
    MethodNotFound = 'method_not_found',
    InvalidParams = 'invalid_params',
    InternalError = 'internal_error',
  }

  export const ListToolsRequestSchema: {
    type: 'object';
    properties: Record<string, any>;
  };

  export const CallToolRequestSchema: {
    type: 'object';
    properties: Record<string, any>;
  };

  export interface ListToolsRequest {
    params: Record<string, unknown>;
  }

  export interface CallToolRequest {
    params: {
      name: string;
      arguments?: Record<string, unknown>;
    };
  }
}
