# Copyright © O2 Czech Republic a.s. - All Rights Reserved.
# Unauthorized copying of this file, via any medium is strictly prohibited.
# Terms and Conditions of usage are defined in file 'LICENSE.txt', which is part of this source code package.
# ---------------------------------------------------------------
# Source: https://network.git.cz.o2/ntwcl/gitopsntw/-/tree/main/templates/repository/gitops/.gitlab-ci.yml
# Examples of extending this CI/CD configuration: https://network.git.cz.o2/ntwcl/gitopsntw/-/blob/main/docs/APPLICATION_CICD_MODS.md
# ---------------------------------------------------------------
include:
  - project: "ntwcl/gitopsntw"
    ref: HEAD
    file:
      - "/templates/cicd/gitlab-ci-deployment_form.yml"
      - "/templates/cicd/gitlab-ci-deployment.yml"
# ---------------------------------------------------------------

stages:
  - gitops:deployment
  - preparation
  - test
  - build
  - deploy
  - versioning

variables:
  NODEJS_IMAGE: node:20-bullseye

## PREPARE
create release:
  image: $NODEJS_IMAGE
  stage: deploy
  tags:
    - baremetaldind
  script:
      - echo "$O2_CA" > custom_ca.crt
      - cp *.crt /usr/local/share/ca-certificates/
      - update-ca-certificates
      - export NODE_EXTRA_CA_CERTS=/etc/ssl/certs/ca-certificates.crt
      - npm ci
      - git config --global http.sslVerify false # we dont have ca root cert
      - export NODE_OPTIONS=--use-openssl-ca
      - chmod +x scripts/bump_version.sh # make sure the bump script is executable
      - npx --yes semantic-release
      - echo "Version bumped to $(<version)."
  rules:
    - if: $CI_PIPELINE_SOURCE == 'web' && ($ACTION != '-- select --' || $CLUSTER_NAME != '-- select --')
      when: never
    - if: '$CI_PROJECT_NAME != "aidcc-ccczautomationllmconnector"'
      when: manual
    - if: '$CI_COMMIT_MESSAGE =~ /\[release\]/'
      when: never
    - if: '$CI_COMMIT_REF_NAME == "main"'
      when: always
  needs: []

## DOCKER BUILD & PUSH
### Building docker image and pushing to local gitlab repository
build docker image and push:
    stage: deploy
    tags:
        - shell
    before_script:
        - export CURRENT_VERSION_ID="$(<version)"
        - echo ${CI_REGISTRY_PASSWORD} | docker login -u ${CI_REGISTRY_USER} ${CI_REGISTRY} --password-stdin
    script:
        - docker build --pull -t ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID} -f Dockerfile .
        - docker push ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
        - docker rmi ${CI_REGISTRY_IMAGE}:${CURRENT_VERSION_ID}
    needs: []
    rules:
        - if: $CI_PIPELINE_SOURCE == 'web' && ($ACTION != '-- select --' || $CLUSTER_NAME != '-- select --')
          when: never
        - if: '$CI_COMMIT_TAG'
          when: always
        - if: '$CI_COMMIT_REF_NAME == "main"'
          when: manual

## Pull Request checks
### Run typecheck
typecheck:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - npm ci
    - npm run build
  allow_failure: false
  only:
    - merge_requests

## run lint
lint:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - npm ci
    - npm run lint
  allow_failure: false
  only:
    - merge_requests

## run tests
test:
  image: $NODEJS_IMAGE
  stage: test
  tags:
    - baremetaldind
  script:
    - npm ci
    - npm test
  allow_failure: false
  only:
    - merge_requests