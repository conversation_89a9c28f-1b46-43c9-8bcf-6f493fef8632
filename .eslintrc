{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "prettier"], "rules": {"prettier/prettier": "error", "no-console": "warn", "no-unused-vars": "off", "@typescript-eslint/no-unused-vars": ["warn", {"argsIgnorePattern": "^_", "varsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-var-requires": "off", "curly": ["error", "all"], "eqeqeq": ["error", "always", {"null": "ignore"}], "no-return-await": "error", "no-throw-literal": "error", "prefer-const": "error"}, "env": {"browser": true, "node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module"}, "ignorePatterns": ["dist", "node_modules", "build", "coverage"], "overrides": [{"files": ["*.js"], "rules": {"@typescript-eslint/no-var-requires": "off"}}, {"files": ["**/services/**/*.ts", "**/services/**/*.js"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "@typescript-eslint/no-empty-function": "off", "@typescript-eslint/ban-ts-comment": "off", "no-case-declarations": "off", "no-useless-catch": "off"}}, {"files": ["**/__tests__/**/*.ts", "**/__tests__/**/*.js", "**/test-*.ts", "**/test-*.js"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off"}}, {"files": ["**/websocket/**/*.ts", "**/websocket/**/*.js"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-non-null-assertion": "off", "no-case-declarations": "off"}}, {"files": ["**/common/**/*.ts", "**/common/**/*.js", "**/utils/**/*.ts", "**/utils/**/*.js", "src/index.ts"], "rules": {"no-console": "off", "@typescript-eslint/no-explicit-any": "off", "no-empty": "off"}}, {"files": ["**/types/**/*.ts", "**/types/**/*.d.ts"], "rules": {"@typescript-eslint/no-explicit-any": "off"}}]}