#!/bin/bash

# Re-enable state machine tests by removing the .disabled suffix
cd /Users/<USER>/Dev/genesys-cloud-audio-connector\ 2

# Rename files in __tests__ directory
mv src/session/state-machine/__tests__/disconnecting-and-closed-state-actions.test.ts.disabled src/session/state-machine/__tests__/disconnecting-and-closed-state-actions.test.ts
mv src/session/state-machine/__tests__/idle-state-actions.test.ts.disabled src/session/state-machine/__tests__/idle-state-actions.test.ts

# Rename files in tests directory
mv src/session/state-machine/tests/disconnecting-and-closed-state-actions.test.ts.disabled src/session/state-machine/tests/disconnecting-and-closed-state-actions.test.ts
mv src/session/state-machine/tests/idle-state-actions.test.ts.disabled src/session/state-machine/tests/idle-state-actions.test.ts
mv src/session/state-machine/tests/initialize-asr-service-action.test.ts.disabled src/session/state-machine/tests/initialize-asr-service-action.test.ts
mv src/session/state-machine/tests/playing-state-actions.test.ts.disabled src/session/state-machine/tests/playing-state-actions.test.ts
mv src/session/state-machine/tests/process-bot-start-action.test.ts.disabled src/session/state-machine/tests/process-bot-start-action.test.ts
mv src/session/state-machine/tests/processing-bot-state-actions.test.ts.disabled src/session/state-machine/tests/processing-bot-state-actions.test.ts
mv src/session/state-machine/tests/processing-responding-state-actions.test.ts.disabled src/session/state-machine/tests/processing-responding-state-actions.test.ts
mv src/session/state-machine/tests/set-conversation-id-action.test.ts.disabled src/session/state-machine/tests/set-conversation-id-action.test.ts
mv src/session/state-machine/tests/set-playback-state-action.test.ts.disabled src/session/state-machine/tests/set-playback-state-action.test.ts

echo "Tests re-enabled successfully."
