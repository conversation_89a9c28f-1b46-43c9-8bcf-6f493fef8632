# Unified MCP + LLM Testing Terminal

A comprehensive terminal application for testing MCP (Model Context Protocol) integration with LLM function calling capabilities.

## Features

🔗 **MCP Connection Testing** - Verifies connection to your MCP server  
🛠️ **Tool Discovery** - Lists and displays all available MCP tools  
💬 **Interactive Chat** - Terminal-based chat interface with your LLM  
🤖 **Function Calling** - LLM can automatically use MCP tools when appropriate  
🔧 **Real Tool Execution** - Executes actual MCP tools and returns results to LLM  
📝 **Conversation Context** - Maintains chat history with tool results  

## Prerequisites

1. **MCP Server Running**:
   ```bash
   cd ~/Dev/aidcc-ccczautomationmcpserver
   npm start
   ```

2. **LLM Configuration**: Ensure your `.env.development` has proper LLM settings:
   ```bash
   LLM_PROVIDER_TYPE=azure  # or litellm
   AZURE_OPENAI_KEY=your_key
   AZURE_OPENAI_ENDPOINT=your_endpoint
   AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment
   ```

3. **MCP Server URL** (optional):
   ```bash
   MCP_SERVER_URL=http://127.0.0.1:6274  # default
   ```

## Usage

### Quick Start
```bash
npm run test:unified
```

### Direct Execution
```bash
npx tsx scripts/unified-mcp-llm-tester.ts
```

## What You'll See

### 1. Connection & Setup
```
🚀 Unified MCP + LLM Testing Terminal
MCP Server: http://127.0.0.1:6274
LLM Provider: azure

=== Testing MCP Server Connection ===
✅ Connected to MCP server at http://127.0.0.1:6274

=== Discovering Available Tools ===
✅ Found 3 tools:
  1. lookupCustomer
     Look up customer information by ANI
     Arguments: ani

  2. getDateTime
     Get current date and time
     Arguments: format

=== Initializing LLM with Function Calling ===
✅ LLM initialized with function calling capabilities
```

### 2. Interactive Chat
```
=== Interactive Chat with Function Calling ===
ℹ️ Chat started! The LLM can use MCP tools when appropriate.
ℹ️ Commands: "quit" to exit, "tools" to list tools, "clear" to clear history
ℹ️ Try asking about customers, accounts, or mention an ANI (10-digit number)

👤 You: Can you help me with customer **********?
ℹ️ Processing with LLM...
🔧 Executing tool: lookupCustomer with ANI: **********
🔧 Tool result: {
  "ani": "**********",
  "customerName": "John Doe",
  "customerId": "cust-123",
  "accountStatus": "active"
}
ℹ️ Getting final response with tool results...
🤖 I can see that customer ********** is John Doe (Customer ID: cust-123) with an active account. How can I help you with John's account today?
```

## Chat Commands

- **`quit`** - Exit the application
- **`tools`** - List all available MCP tools
- **`clear`** - Clear conversation history
- **Regular messages** - Chat with the LLM

## Function Calling Behavior

The LLM will automatically use MCP tools when:

- **Customer queries**: Mentions of "customer", "account", "ANI"
- **ANI detection**: 10-digit numbers in user input
- **Context appropriate**: When tools can help answer the question

### Example Triggers

✅ **Will use tools**:
- "What's the status of customer **********?"
- "Can you help me with my account?"
- "Look up ANI **********"

❌ **Won't use tools**:
- "Hello, how are you?"
- "What's the weather like?"
- "Tell me a joke"

## Technical Details

### Architecture
- **MCPClient**: Direct connection to MCP server using official SDK
- **MCPService**: High-level service wrapper for tool execution
- **FunctionCallingLLMProvider**: Enhanced LLM provider with tool calling
- **UnifiedMCPLLMTester**: Main application orchestrator

### Tool Detection
The system uses intelligent heuristics to detect when tools should be used:
1. Analyzes user input for keywords (customer, account, ANI)
2. Extracts relevant parameters (ANI numbers)
3. Executes appropriate MCP tools
4. Provides results to LLM for final response

### Conversation Flow
1. User sends message
2. LLM processes message and determines if tools are needed
3. If tools needed: Execute MCP tools → Get results → Generate final response
4. If no tools needed: Generate direct response
5. Continue conversation with full context

## Troubleshooting

### MCP Connection Issues
```bash
❌ MCP connection failed: connect ECONNREFUSED 127.0.0.1:6274
```
**Solution**: Ensure MCP server is running on the correct port

### LLM Initialization Issues
```bash
❌ LLM initialization failed: Missing required Azure OpenAI configuration
```
**Solution**: Check your `.env.development` file for proper LLM configuration

### No Tools Found
```bash
⚠️ No tools found on MCP server
```
**Solution**: Verify your MCP server has tools registered and accessible

## Development

The unified tester replaces the previous separate test scripts:
- ~~`test-mcp-integration.ts`~~ (removed)
- ~~`test-mcp-llm-integration.ts`~~ (removed)
- ~~`test-mcp-integration.js`~~ (removed)

All functionality is now consolidated into `unified-mcp-llm-tester.ts` for better maintainability and user experience.
