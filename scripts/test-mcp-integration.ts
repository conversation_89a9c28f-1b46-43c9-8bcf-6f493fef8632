#!/usr/bin/env tsx

/**
 * MCP Integration Local Testing Script (TypeScript)
 *
 * This script tests the MCP integration locally without running the full STT-LLM-TTS loop.
 * It connects directly to the MCP server and tests customer lookup functionality.
 *
 * Usage:
 *   npx tsx scripts/test-mcp-integration.ts
 *   # or
 *   npm run test:mcp-local
 *
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:6274
 *   2. Customer lookup tools available on the MCP server
 *
 * What it tests:
 *   - MCP server connectivity using official SDK
 *   - Tool discovery
 *   - Customer lookup by ANI
 *   - Error handling
 *   - Response parsing
 *   - Service-level integration
 */

import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import type { CustomerInfo } from '../src/services/mcp/types';

// Utility to generate dummy arguments for a tool
function getDummyArgs(tool: any): Record<string, unknown> {
  if (!tool.arguments) {
    return {};
  }
  const args: Record<string, unknown> = {};
  for (const [key, def] of Object.entries(tool.arguments)) {
    // Try to guess a dummy value based on type or name
    if (def?.type === 'string' || typeof def?.type === 'undefined') {
      args[key] = 'test';
    } else if (def?.type === 'number') {
      args[key] = 1;
    } else if (def?.type === 'boolean') {
      args[key] = true;
    } else {
      args[key] = null;
    }
  }
  return args;
}

// Test configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274';
const TEST_ANIS = [
  '1234567890', // Test ANI 1
  '0987654321', // Test ANI 2
  '5555555555', // Test ANI 3
  'invalid_ani', // Invalid ANI for error testing
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

async function testMCPClient(): Promise<boolean> {
  logHeader('Testing MCP Client (Low-Level SDK)');

  const client = new MCPClient({
    url: MCP_SERVER_URL,
    timeout: 5000,
  });

  try {
    // Test 1: Connection test
    logInfo('Testing MCP server connection...');
    if (typeof client.testConnection === 'function') {
      const isConnected = await client.testConnection();
      if (isConnected) {
        logSuccess('MCP server connection successful');
      } else {
        logError('MCP server connection failed');
        return false;
      }
    } else {
      logWarning('MCPClient.testConnection() is not available, skipping direct connection test.');
    }

    // Test 2: List available tools
    logInfo('Discovering available tools...');
    const tools = await client.listTools();
    logSuccess(`Found ${tools.length} tools:`);
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
      if (tool.arguments) {
        const argNames = Object.keys(tool.arguments);
        if (argNames.length > 0) {
          console.log(`    Arguments: ${argNames.join(', ')}`);
        }
      }
    });

    // Test 3: Find customer lookup tool
    const customerTool = tools.find(
      tool =>
        tool.name.toLowerCase().includes('customer') ||
        tool.name.toLowerCase().includes('lookup') ||
        tool.name.toLowerCase().includes('ani')
    );

    if (!customerTool) {
      logWarning('No customer lookup tool found. Skipping customer lookup tests.');
      logInfo('Testing all available tools with dummy arguments...');
      for (const tool of tools) {
        logInfo(`Testing tool: ${tool.name}`);
        try {
          const dummyArgs = getDummyArgs(tool);
          const result = await client.callTool(tool.name, dummyArgs);
          logSuccess(`Tool call successful:`);
          console.log(JSON.stringify(result, null, 2));
        } catch (error) {
          logError(`Tool call failed: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      return true;
    }

    // Test 4: Test customer lookup with various ANIs
    logInfo(`Testing customer lookup tool: ${customerTool.name}`);
    for (const ani of TEST_ANIS) {
      try {
        logInfo(`Looking up customer for ANI: ${ani}`);
        const result = await client.callTool(customerTool.name, { ani });
        logSuccess(`Customer lookup successful for ${ani}:`);
        console.log(JSON.stringify(result, null, 2));
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (ani === 'invalid_ani') {
          logSuccess(`Expected error for invalid ANI: ${errorMessage}`);
        } else {
          logError(`Customer lookup failed for ${ani}: ${errorMessage}`);
        }
      }
    }

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`MCP Client test failed: ${errorMessage}`);
    return false;
  }
}

async function testMCPService(): Promise<boolean> {
  logHeader('Testing MCP Service (High-Level)');

  const mcpService = new MCPService(
    new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 5000,
    })
  );

  try {
    // Test 1: Service initialization
    logInfo('Initializing MCP service...');
    await mcpService.initialize();
    logSuccess('MCP service initialized successfully');

    // Test 2: Check availability
    logInfo('Checking MCP service availability...');
    let isAvailable = false;
    if (typeof mcpService.client.testConnection === 'function') {
      try {
        isAvailable = await mcpService.isAvailable();
      } catch (err) {
        logWarning('MCPService.isAvailable() failed, continuing with tool tests.');
      }
    } else {
      logWarning(
        'MCPService.client.testConnection() is not available, skipping availability check.'
      );
      isAvailable = true; // Assume available for further tests
    }
    if (isAvailable) {
      logSuccess('MCP service is available');
    } else {
      logError('MCP service is not available');
      // Continue to tool tests anyway
    }

    // Test 3: List available tools and test them
    logInfo('Listing available tools via MCPService...');
    const tools = await mcpService.client.listTools();
    if (!tools.length) {
      logWarning('No tools available on MCP server.');
      return true;
    }

    // Try to find customer lookup tool
    const customerTool = tools.find(
      tool =>
        tool.name.toLowerCase().includes('customer') ||
        tool.name.toLowerCase().includes('lookup') ||
        tool.name.toLowerCase().includes('ani')
    );

    if (!customerTool) {
      logWarning('No customer lookup tool found. Skipping customer lookup tests.');
      logInfo('Testing all available tools with dummy arguments via MCPService...');
      for (const tool of tools) {
        logInfo(`Testing tool: ${tool.name}`);
        try {
          const dummyArgs = getDummyArgs(tool);
          const result = await mcpService.invokeTool(tool.name, dummyArgs);
          logSuccess(`Tool call successful:`);
          console.log(JSON.stringify(result, null, 2));
        } catch (error) {
          logError(`Tool call failed: ${error instanceof Error ? error.message : String(error)}`);
        }
      }
      return true;
    }

    // Test customer lookup with various ANIs
    logInfo(`Testing customer lookup tool: ${customerTool.name}`);
    for (const ani of TEST_ANIS) {
      try {
        logInfo(`Service lookup for ANI: ${ani}`);
        const customer: CustomerInfo | null = await mcpService.invokeTool(customerTool.name, {
          ani,
        });
        if (customer) {
          logSuccess(`Customer found for ${ani}:`);
          console.log(JSON.stringify(customer, null, 2));
        } else {
          logWarning(`No customer found for ${ani}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        if (ani === 'invalid_ani') {
          logSuccess(`Expected error for invalid ANI: ${errorMessage}`);
        } else {
          logError(`Service lookup failed for ${ani}: ${errorMessage}`);
        }
      }
    }

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`MCP Service test failed: ${errorMessage}`);
    return false;
  }
}

async function testErrorHandling(): Promise<void> {
  logHeader('Testing Error Handling');

  // Test with invalid URL
  const invalidClient = new MCPClient({
    url: 'http://localhost:9999', // Non-existent server
    timeout: 2000,
  });

  try {
    logInfo('Testing connection to invalid server...');
    const isConnected = await invalidClient.testConnection();
    if (!isConnected) {
      logSuccess('Correctly handled connection to invalid server');
    } else {
      logError('Unexpectedly connected to invalid server');
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logSuccess(`Correctly caught connection error: ${errorMessage}`);
  }
}

async function testSDKFeatures(): Promise<void> {
  logHeader('Testing SDK-Specific Features');

  const client = new MCPClient({
    url: MCP_SERVER_URL,
    timeout: 5000,
  });

  try {
    logInfo('Testing SDK client connection management...');

    // Test connection state
    logInfo('Checking initial connection state...');
    const initialState = client.isConnected();
    logInfo(`Initial connection state: ${initialState}`);

    // Test getting underlying SDK client
    logInfo('Testing SDK client access...');
    const sdkClient = client.getClient();
    if (sdkClient) {
      logSuccess('SDK client accessible');
    } else {
      logError('SDK client not accessible');
    }

    // Test disconnect
    logInfo('Testing disconnect...');
    await client.disconnect();
    logSuccess('Disconnect successful');
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`SDK features test failed: ${errorMessage}`);
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🧪 MCP Integration Local Testing (SDK-Based)${colors.reset}`);
  log(`Testing MCP server at: ${MCP_SERVER_URL}`);

  let allTestsPassed = true;

  // Run tests
  const clientTestPassed = await testMCPClient();
  const serviceTestPassed = await testMCPService();
  await testErrorHandling();
  await testSDKFeatures();

  allTestsPassed = clientTestPassed && serviceTestPassed;

  // Summary
  logHeader('Test Summary');
  if (allTestsPassed) {
    logSuccess('All MCP integration tests passed! 🎉');
    logInfo('The MCP integration is working correctly with the official SDK.');
    logInfo('Customer identification is ready for voice connector integration.');
  } else {
    logError('Some MCP integration tests failed! 🚨');
    logInfo('Please check the MCP server and configuration.');
  }

  // Instructions
  logHeader('Next Steps');
  logInfo('To test with the full voice connector:');
  logInfo('1. Start the voice connector: npm run start');
  logInfo('2. Make a test call with ANI');
  logInfo('3. Check logs for customer lookup during session initialization');
  logInfo('4. Verify customer context is passed to LLM for personalization');

  logHeader('MCP Server Setup');
  logInfo('If MCP server is not running:');
  logInfo('1. cd ~/Dev/aidcc-ccczautomationmcpserver');
  logInfo('2. npm start');
  logInfo('3. Verify server is running on http://127.0.0.1:6274');
  logInfo('4. Use MCP Inspector to verify tools are available');

  process.exit(allTestsPassed ? 0 : 1);
}

// Handle uncaught errors
process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

process.on('uncaughtException', error => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Run the tests
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Test execution failed: ${errorMessage}`);
  process.exit(1);
});
