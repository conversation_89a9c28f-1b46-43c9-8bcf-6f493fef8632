#!/usr/bin/env tsx

/**
 * MCP + LLM Integration CLI Testing Tool
 *
 * This CLI tool tests the complete MCP integration with your actual LLM configuration.
 * It uses UNMOCKED code to:
 * 1. Connect to real MCP server
 * 2. Discover available tools
 * 3. Use your configured LLM with MCP customer context
 * 4. Allow interactive testing with prompts
 *
 * Usage:
 *   npx tsx scripts/test-mcp-llm-integration.ts
 *   # or
 *   npm run test:mcp-llm
 *
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:6274
 *   2. LLM configured in .env.development
 *   3. Customer lookup tools available on MCP server
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import { BotService } from '../src/services/bot-service';
import { BotResource } from '../src/services/bot-service/bot-resource';
import { InMemoryDatabaseService } from '../src/services/in-memory-database-service';
import { PerformanceLogger } from '../src/services/monitoring/performance-logger';
import type { CustomerInfo } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logPrompt(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

class MCPLLMTester {
  private mcpClient: MCPClient;
  private mcpService: MCPService;
  private botService: BotService;
  private dbService: InMemoryDatabaseService;
  private rl: readline.Interface;

  constructor() {
    // Initialize services (UNMOCKED!)
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });

    this.mcpService = new MCPService(this.mcpClient);
    this.dbService = InMemoryDatabaseService.getInstance();
    this.botService = new BotService(this.mcpService);

    // Setup readline for interactive prompts
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async testMCPConnection(): Promise<boolean> {
    logHeader('Testing Real MCP Server Connection');

    try {
      logInfo('Connecting to MCP server...');
      const isConnected = await this.mcpClient.testConnection();

      if (isConnected) {
        logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);
        return true;
      } else {
        logError('Failed to connect to MCP server');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`MCP connection failed: ${errorMessage}`);
      return false;
    }
  }

  async discoverTools(): Promise<any[]> {
    logHeader('Discovering Available Tools (Real MCP Server)');

    try {
      logInfo('Fetching tools from MCP server...');
      const tools = await this.mcpClient.listTools();

      logSuccess(`Found ${tools.length} tools:`);
      tools.forEach((tool, index) => {
        console.log(
          `  ${index + 1}. ${colors.bold}${tool.name}${colors.reset}: ${
            tool.description || 'No description'
          }`
        );
        if (tool.arguments) {
          const argNames = Object.keys(tool.arguments);
          if (argNames.length > 0) {
            console.log(`     Arguments: ${argNames.join(', ')}`);
          }
        }
      });

      return tools;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to discover tools: ${errorMessage}`);
      return [];
    }
  }

  async testDirectToolCall(): Promise<void> {
    logHeader('Testing Direct Tool Calls');

    try {
      // Test customer lookup if available
      const tools = await this.mcpClient.listTools();
      const customerTool = tools.find(
        tool =>
          tool.name.toLowerCase().includes('customer') ||
          tool.name.toLowerCase().includes('lookup') ||
          tool.name.toLowerCase().includes('ani')
      );

      if (customerTool) {
        logInfo(`Testing customer lookup tool: ${customerTool.name}`);
        const testANI = '**********';

        const result = await this.mcpClient.callTool(customerTool.name, { ani: testANI });
        logSuccess(`Customer lookup successful for ANI ${testANI}:`);
        console.log(JSON.stringify(result, null, 2));
      } else {
        logWarning('No customer lookup tool found. Testing first available tool...');
        if (tools.length > 0) {
          const firstTool = tools[0];
          logInfo(`Testing tool: ${firstTool.name}`);
          const result = await this.mcpClient.callTool(firstTool.name, {});
          logSuccess(`Tool call successful:`);
          console.log(JSON.stringify(result, null, 2));
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Direct tool call failed: ${errorMessage}`);
    }
  }

  async testMCPService(): Promise<void> {
    logHeader('Testing MCP Service Integration');

    try {
      logInfo('Initializing MCP service...');
      await this.mcpService.initialize();
      logSuccess('MCP service initialized');

      logInfo('Testing service availability...');
      const isAvailable = await this.mcpService.isAvailable();
      if (isAvailable) {
        logSuccess('MCP service is available');
      } else {
        logError('MCP service is not available');
        return;
      }

      // Test customer lookup via service using invokeTool
      logInfo('Testing customer lookup via service...');
      const testANI = '**********';
      const customer = await this.mcpService.invokeTool<CustomerInfo>('lookupCustomer', {
        ani: testANI,
      });

      if (customer) {
        logSuccess(`Customer found via service for ANI ${testANI}:`);
        console.log(JSON.stringify(customer, null, 2));
      } else {
        logWarning(`No customer found for ANI ${testANI}`);
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`MCP service test failed: ${errorMessage}`);
    }
  }

  async testLLMWithMCP(): Promise<void> {
    logHeader('Testing LLM with MCP Customer Context');

    try {
      logInfo('Testing LLM with customer context from MCP...');

      // First, get customer data from MCP
      const testANI = '**********';
      const customer = await this.mcpService.invokeTool<CustomerInfo>('lookupCustomer', {
        ani: testANI,
      });

      // Create a bot resource with customer context
      const conversationId = 'test-conversation-' + Date.now();
      const botResource = await BotResource.create(
        testANI,
        conversationId,
        this.dbService,
        customer
      );

      // Test with customer context
      const testPrompt = 'Hello, can you help me with my account?';
      logInfo(`Testing LLM with customer context and prompt: "${testPrompt}"`);

      const performanceLogger = PerformanceLogger.getInstance();
      performanceLogger.setConversationId(conversationId);
      const metricsContext = performanceLogger.createRequest(testPrompt);
      const response = await botResource.getBotResponse(testPrompt, metricsContext);

      if (response) {
        logSuccess('LLM responded successfully with customer context:');
        logPrompt(response.text);
        logInfo(`Response confidence: ${response.confidence}`);
      } else {
        logWarning('LLM returned null response');
      }

      // Clean up
      await botResource.dispose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`LLM with MCP test failed: ${errorMessage}`);
    }
  }

  async interactiveMode(): Promise<void> {
    logHeader('Interactive Testing Mode');
    logInfo('You can now test prompts with MCP customer context');
    logInfo('Type "quit" to exit, "tools" to list available tools, or enter ANI:prompt format');
    logInfo('Example: "**********:Hello, can you help me with my account?"');

    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 Enter your prompt: ${colors.reset}`, resolve);
      });
    };

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const userInput = await askQuestion();

        if (userInput.toLowerCase() === 'quit') {
          logInfo('Exiting interactive mode...');
          break;
        }

        if (userInput.toLowerCase() === 'tools') {
          await this.discoverTools();
          continue;
        }

        logUser(userInput);

        // Parse ANI:prompt format
        let ani = '**********'; // Default test ANI
        let prompt = userInput;

        if (userInput.includes(':')) {
          const parts = userInput.split(':', 2);
          if (parts[0].match(/^\d{10}$/)) {
            ani = parts[0];
            prompt = parts[1];
            logInfo(`Using ANI: ${ani} for customer lookup`);
          }
        }

        try {
          // Get customer context
          const customer = await this.mcpService.invokeTool<CustomerInfo>('lookupCustomer', {
            ani,
          });

          // Create bot resource with customer context
          const conversationId = 'interactive-' + Date.now();
          const botResource = await BotResource.create(
            ani,
            conversationId,
            this.dbService,
            customer
          );

          // Setup metrics
          const performanceLogger = PerformanceLogger.getInstance();
          performanceLogger.setConversationId(conversationId);
          const metricsContext = performanceLogger.createRequest(prompt);

          logInfo('Processing with LLM and customer context...');
          const response = await botResource.getBotResponse(prompt, metricsContext);

          if (response) {
            logSuccess('LLM responded with customer context:');
            logPrompt(response.text);
            logInfo(`Confidence: ${response.confidence}`);

            if (customer) {
              logInfo('Customer context used:');
              console.log(JSON.stringify(customer, null, 2));
            }
          } else {
            logWarning('LLM returned null response');
          }

          // Clean up
          await botResource.dispose();
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : String(error);
          logError(`Error processing prompt: ${errorMessage}`);
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Error in interactive mode: ${errorMessage}`);
      }
    }
  }

  async cleanup(): Promise<void> {
    this.rl.close();
    try {
      await this.mcpClient.disconnect();
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🧪 MCP + LLM Integration CLI Tester${colors.reset}`);
  log(`Testing with MCP server: ${MCP_SERVER_URL}`);
  log(`Using LLM configuration from .env.development`);

  const tester = new MCPLLMTester();

  try {
    // Test MCP connection
    const mcpConnected = await tester.testMCPConnection();
    if (!mcpConnected) {
      logError('Cannot proceed without MCP server connection');
      logInfo('Please ensure MCP server is running:');
      logInfo('  cd ~/Dev/aidcc-ccczautomationmcpserver');
      logInfo('  npm start');
      process.exit(1);
    }

    // Discover tools
    await tester.discoverTools();

    // Test direct tool calls
    await tester.testDirectToolCall();

    // Test MCP service
    await tester.testMCPService();

    // Test LLM
    await tester.testLLMWithMCP();

    // Interactive mode
    await tester.interactiveMode();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Test execution failed: ${errorMessage}`);
  } finally {
    await tester.cleanup();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\nReceived SIGINT, cleaning up...');
  process.exit(0);
});

process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the CLI tool
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`CLI tool failed: ${errorMessage}`);
  process.exit(1);
});
