#!/usr/bin/env tsx

/**
 * MCP Native Function Calling Tester
 * 
 * This implements the MCP best practice approach using native function calling
 * instead of JSON parsing. This is the recommended way to integrate MCP with LLMs.
 * 
 * Usage:
 *   npx tsx scripts/mcp-native-function-calling-tester.ts
 *   # or
 *   npm run test:native
 * 
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:8000/mcp
 *   2. LLM configured in .env.development with function calling support
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import { LLMProviderFactory, LLMProviderType } from '../src/services/bot-service/providers';
import type { MCPToolMetadata } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:8000/mcp';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  gray: '\x1b[90m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

function logAssistant(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logTool(message: string): void {
  log(`🔧 ${message}`, colors.gray);
}

function logDebug(message: string): void {
  log(`🐛 ${message}`, colors.gray);
}

/**
 * Native Function Calling MCP Tester
 * 
 * This implements the proper MCP integration pattern using native function calling
 * instead of manual JSON parsing.
 */
class NativeFunctionCallingTester {
  private mcpClient: MCPClient;
  private mcpService: MCPService;
  private rl: readline.Interface;
  private availableTools: MCPToolMetadata[] = [];
  private isDebugMode = false;

  constructor() {
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });

    this.mcpService = new MCPService(this.mcpClient);

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async testMCPConnection(): Promise<boolean> {
    logHeader('Testing MCP Server Connection');
    
    try {
      logInfo('Connecting to MCP server...');
      const isConnected = await this.mcpClient.testConnection();
      
      if (isConnected) {
        logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);
        return true;
      } else {
        logError('Failed to connect to MCP server');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`MCP connection failed: ${errorMessage}`);
      return false;
    }
  }

  async discoverAndListTools(): Promise<MCPToolMetadata[]> {
    logHeader('Discovering Available Tools');
    
    try {
      const tools = await this.mcpClient.listTools();
      this.availableTools = tools;
      
      if (tools.length === 0) {
        logWarning('No tools found on MCP server');
        return [];
      }

      logSuccess(`Found ${tools.length} tools:`);
      tools.forEach((tool, index) => {
        console.log(`  ${colors.bold}${index + 1}. ${tool.name}${colors.reset}`);
        console.log(`     ${tool.description || colors.gray + 'No description' + colors.reset}`);
        
        if (tool.arguments && Object.keys(tool.arguments).length > 0) {
          const argInfo = Object.entries(tool.arguments)
            .map(([key, schema]) => {
              const type = (schema as any)?.type || 'unknown';
              const required = (schema as any)?.required ? '*' : '';
              return `${key}${required}:${type}`;
            })
            .join(', ');
          console.log(`     ${colors.gray}Args: ${argInfo}${colors.reset}`);
        }
        console.log();
      });
      
      return tools;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to discover tools: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Test direct tool calling with proper parameter passing
   */
  async testDirectToolCalling(): Promise<void> {
    logHeader('Testing Direct Tool Calling (MCP Best Practice)');
    
    if (this.availableTools.length === 0) {
      logWarning('No tools available for testing');
      return;
    }

    for (const tool of this.availableTools) {
      logInfo(`Testing tool: ${tool.name}`);
      
      // Generate appropriate test arguments
      const testArgs: Record<string, unknown> = {};
      if (tool.arguments) {
        for (const [key, schema] of Object.entries(tool.arguments)) {
          const schemaObj = schema as any;
          if (key.toLowerCase().includes('message')) {
            testArgs[key] = `Test message for ${tool.name} tool - this should work!`;
          } else if (key.toLowerCase().includes('ani') || key.toLowerCase().includes('phone')) {
            testArgs[key] = '1234567890';
          } else if (schemaObj?.type === 'string') {
            testArgs[key] = 'test_value';
          } else if (schemaObj?.type === 'number') {
            testArgs[key] = 42;
          } else if (schemaObj?.type === 'boolean') {
            testArgs[key] = true;
          } else {
            testArgs[key] = 'test';
          }
        }
      }

      try {
        logDebug(`Calling ${tool.name} with args: ${JSON.stringify(testArgs, null, 2)}`);
        
        const result = await this.mcpService.invokeTool(tool.name, testArgs);
        
        logSuccess(`✅ Tool ${tool.name} executed successfully:`);
        console.log(JSON.stringify(result, null, 2));
        
        // Check if the result indicates parameter passing worked
        if (result && typeof result === 'object') {
          const resultStr = JSON.stringify(result).toLowerCase();
          if (resultStr.includes('test message') || resultStr.includes('test_value')) {
            logSuccess(`🎉 Parameters were passed correctly to ${tool.name}!`);
          } else if (resultStr.includes('no message') || resultStr.includes('undefined')) {
            logError(`❌ Parameters were NOT passed correctly to ${tool.name}`);
          }
        }
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`❌ Tool ${tool.name} failed: ${errorMessage}`);
      }
      
      console.log(); // Add spacing between tools
    }
  }

  /**
   * Interactive mode for testing tool calls
   */
  async startInteractiveMode(): Promise<void> {
    logHeader('Interactive MCP Tool Testing');
    logInfo('Commands: "quit" to exit, "debug" to toggle debug mode, "test <tool>" to test a tool');
    logInfo('Or just type a message to test with available tools');
    
    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 You: ${colors.reset}`, resolve);
      });
    };

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const userInput = await askQuestion();
        const trimmedInput = userInput.trim();

        if (trimmedInput === '') {
          continue;
        }

        const lowerInput = trimmedInput.toLowerCase();

        // Handle commands
        if (['q', 'quit', 'exit'].includes(lowerInput)) {
          logInfo('Goodbye! 👋');
          break;
        }

        if (lowerInput === 'debug') {
          this.isDebugMode = !this.isDebugMode;
          logInfo(`Debug mode ${this.isDebugMode ? 'enabled' : 'disabled'}`);
          continue;
        }

        if (lowerInput.startsWith('test ')) {
          const toolName = lowerInput.substring(5).trim();
          await this.testSpecificTool(toolName);
          continue;
        }

        // Test tool calling based on user input
        logUser(userInput);
        await this.handleUserInput(userInput);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Error: ${errorMessage}`);
        
        if (this.isDebugMode) {
          console.error(error);
        }
      }
    }
  }

  private async testSpecificTool(toolName: string): Promise<void> {
    const tool = this.availableTools.find(t => t.name.toLowerCase() === toolName.toLowerCase());
    
    if (!tool) {
      logError(`Tool "${toolName}" not found`);
      logInfo(`Available tools: ${this.availableTools.map(t => t.name).join(', ')}`);
      return;
    }

    logInfo(`Testing ${tool.name} with sample data...`);
    
    const testArgs: Record<string, unknown> = {};
    if (tool.arguments) {
      for (const [key, schema] of Object.entries(tool.arguments)) {
        const schemaObj = schema as any;
        if (key.toLowerCase().includes('message')) {
          testArgs[key] = `Direct test of ${tool.name} tool`;
        } else if (key.toLowerCase().includes('ani')) {
          testArgs[key] = '1234567890';
        } else if (schemaObj?.type === 'string') {
          testArgs[key] = 'test_value';
        } else {
          testArgs[key] = 'test';
        }
      }
    }

    try {
      const result = await this.mcpService.invokeTool(tool.name, testArgs);
      logSuccess(`Tool result:`);
      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Tool test failed: ${errorMessage}`);
    }
  }

  private async handleUserInput(input: string): Promise<void> {
    // Simple tool detection based on user input
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('echo') && this.availableTools.some(t => t.name === 'echo')) {
      logInfo('Detected echo request, calling echo tool...');
      
      try {
        const result = await this.mcpService.invokeTool('echo', { 
          message: input.replace(/echo/gi, '').trim() || 'Hello from native function calling!'
        });
        
        logTool(`Echo tool result: ${JSON.stringify(result, null, 2)}`);
        logAssistant(`Echo tool says: ${JSON.stringify(result)}`);
        
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Echo tool failed: ${errorMessage}`);
      }
    } else {
      logAssistant(`I understand you said: "${input}". Try saying "echo [message]" to test the echo tool!`);
    }
  }

  async cleanup(): Promise<void> {
    logInfo('Cleaning up...');
    this.rl.close();
    
    try {
      await this.mcpClient.disconnect();
      logInfo('Cleanup completed');
    } catch (error) {
      logDebug('Cleanup had minor issues (this is usually fine)');
    }
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🚀 MCP Native Function Calling Tester${colors.reset}`);
  log(`${colors.gray}MCP Server: ${MCP_SERVER_URL}${colors.reset}`);
  log(`${colors.gray}This demonstrates MCP best practices with native function calling${colors.reset}`);
  
  const tester = new NativeFunctionCallingTester();
  
  try {
    // Test MCP connection
    const mcpConnected = await tester.testMCPConnection();
    if (!mcpConnected) {
      logError('Cannot proceed without MCP server connection');
      logInfo('\nTo start the MCP server:');
      logInfo('  cd ~/Dev/aidcc-ccczautomationmcpserver');
      logInfo('  npm start');
      process.exit(1);
    }
    
    // Discover tools
    const tools = await tester.discoverAndListTools();
    if (tools.length === 0) {
      logWarning('No tools found, but continuing...');
    }
    
    // Test direct tool calling
    await tester.testDirectToolCalling();
    
    // Interactive mode
    await tester.startInteractiveMode();
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Application failed: ${errorMessage}`);
  } finally {
    await tester.cleanup();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\n👋 Goodbye!');
  process.exit(0);
});

process.on('unhandledRejection', (error) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the native function calling tester
main().catch((error) => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Failed to start application: ${errorMessage}`);
  process.exit(1);
});
