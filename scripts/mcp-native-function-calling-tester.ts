#!/usr/bin/env tsx

/**
 * MCP Integration Tester with JSON-RPC 2.0 Support
 *
 * This implements proper MCP integration using JSON-RPC 2.0 format
 * for direct tool calling, bypassing SDK issues with parameter passing.
 *
 * Usage:
 *   npx tsx scripts/mcp-native-function-calling-tester.ts
 *   # or
 *   npm run test:mcp
 *
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:8000/mcp
 *   2. MCP server expecting JSON-RPC 2.0 format with method/params
 *
 * What this fixes:
 *   - SDK sends: { name: "echo", arguments: { message: "hello" } }
 *   - Server expects: { "jsonrpc": "2.0", "method": "echo", "params": { "message": "hello" }, "id": 1 }
 *   - This script sends the correct JSON-RPC 2.0 format
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import type { MCPToolMetadata } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:8000/mcp';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  gray: '\x1b[90m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

function logAssistant(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logTool(message: string): void {
  log(`🔧 ${message}`, colors.gray);
}

function logDebug(message: string): void {
  log(`🐛 ${message}`, colors.gray);
}

/**
 * Native Function Calling MCP Tester
 *
 * This implements the proper MCP integration pattern using native function calling
 * instead of manual JSON parsing.
 */
class NativeFunctionCallingTester {
  private mcpClient: MCPClient;
  private rl: readline.Interface;
  private availableTools: MCPToolMetadata[] = [];
  private isDebugMode = false;

  constructor() {
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async testMCPConnection(): Promise<boolean> {
    logHeader('Testing MCP Server Connection');

    try {
      logInfo('Connecting to MCP server...');
      const isConnected = await this.mcpClient.testConnection();

      if (isConnected) {
        logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);
        return true;
      } else {
        logError('Failed to connect to MCP server');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`MCP connection failed: ${errorMessage}`);
      return false;
    }
  }

  async discoverAndListTools(): Promise<MCPToolMetadata[]> {
    logHeader('Discovering Available Tools');

    try {
      const tools = await this.mcpClient.listTools();
      this.availableTools = tools;

      if (tools.length === 0) {
        logWarning('No tools found on MCP server');
        return [];
      }

      logSuccess(`Found ${tools.length} tools:`);
      tools.forEach((tool, index) => {
        console.log(`  ${colors.bold}${index + 1}. ${tool.name}${colors.reset}`);
        console.log(`     ${tool.description || colors.gray + 'No description' + colors.reset}`);

        if (tool.arguments && Object.keys(tool.arguments).length > 0) {
          const argInfo = Object.entries(tool.arguments)
            .map(([key, schema]) => {
              const type = (schema as any)?.type || 'unknown';
              const required = (schema as any)?.required ? '*' : '';
              return `${key}${required}:${type}`;
            })
            .join(', ');
          console.log(`     ${colors.gray}Args: ${argInfo}${colors.reset}`);
        }
        console.log();
      });

      return tools;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to discover tools: ${errorMessage}`);
      return [];
    }
  }

  /**
   * Test direct tool calling with proper JSON-RPC 2.0 format
   */
  async testDirectToolCalling(): Promise<void> {
    logHeader('Testing Direct Tool Calling (JSON-RPC 2.0 Format)');

    if (this.availableTools.length === 0) {
      logWarning('No tools available for testing');
      return;
    }

    for (const tool of this.availableTools) {
      logInfo(`Testing tool: ${tool.name}`);

      // Generate appropriate test arguments
      const testParams: Record<string, unknown> = {};
      if (tool.arguments) {
        for (const [key, schema] of Object.entries(tool.arguments)) {
          const schemaObj = schema as any;
          if (key.toLowerCase().includes('message')) {
            testParams[
              key
            ] = `Test message for ${tool.name} tool - this should work with JSON-RPC 2.0!`;
          } else if (key.toLowerCase().includes('ani') || key.toLowerCase().includes('phone')) {
            testParams[key] = '1234567890';
          } else if (schemaObj?.type === 'string') {
            testParams[key] = 'test_value';
          } else if (schemaObj?.type === 'number') {
            testParams[key] = 42;
          } else if (schemaObj?.type === 'boolean') {
            testParams[key] = true;
          } else {
            testParams[key] = 'test';
          }
        }
      }

      try {
        logDebug(`Testing ${tool.name} with JSON-RPC 2.0 format`);
        logDebug(`Params: ${JSON.stringify(testParams, null, 2)}`);

        // Use direct JSON-RPC 2.0 call instead of SDK's callTool
        const result = await this.callToolDirectly(tool.name, testParams);

        logSuccess(`✅ Tool ${tool.name} executed successfully:`);
        console.log(JSON.stringify(result, null, 2));

        // Check if the result indicates parameter passing worked
        if (result && typeof result === 'object') {
          const resultStr = JSON.stringify(result).toLowerCase();
          if (resultStr.includes('test message') || resultStr.includes('test_value')) {
            logSuccess(`🎉 Parameters were passed correctly to ${tool.name} using JSON-RPC 2.0!`);
          } else if (resultStr.includes('no message') || resultStr.includes('undefined')) {
            logError(`❌ Parameters were NOT passed correctly to ${tool.name}`);
            logInfo(`This suggests the server still isn't receiving the params correctly`);
          } else {
            logInfo(`Tool executed but unclear if params were received (check result above)`);
          }
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`❌ Tool ${tool.name} failed: ${errorMessage}`);
      }

      console.log(); // Add spacing between tools
    }
  }

  /**
   * Call tool directly using proper JSON-RPC 2.0 format
   */
  private async callToolDirectly(toolName: string, params: Record<string, unknown>): Promise<any> {
    const jsonRpcRequest = {
      jsonrpc: '2.0',
      method: toolName,
      params: params,
      id: Date.now(),
    };

    logDebug(`Sending JSON-RPC 2.0 request: ${JSON.stringify(jsonRpcRequest, null, 2)}`);

    try {
      const response = await fetch(MCP_SERVER_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: JSON.stringify(jsonRpcRequest),
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      logDebug(`JSON-RPC 2.0 response: ${JSON.stringify(result, null, 2)}`);

      if (result.error) {
        throw new Error(`JSON-RPC Error: ${result.error.message}`);
      }

      return result.result;
    } catch (error) {
      logError(
        `Direct JSON-RPC call failed: ${error instanceof Error ? error.message : String(error)}`
      );
      throw error;
    }
  }

  /**
   * Interactive mode for testing tool calls
   */
  async startInteractiveMode(): Promise<void> {
    logHeader('Interactive MCP Tool Testing');
    logInfo('Commands: "quit" to exit, "debug" to toggle debug mode, "test <tool>" to test a tool');
    logInfo('Or just type a message to test with available tools');

    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 You: ${colors.reset}`, resolve);
      });
    };

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const userInput = await askQuestion();
        const trimmedInput = userInput.trim();

        if (trimmedInput === '') {
          continue;
        }

        const lowerInput = trimmedInput.toLowerCase();

        // Handle commands
        if (['q', 'quit', 'exit'].includes(lowerInput)) {
          logInfo('Goodbye! 👋');
          break;
        }

        if (lowerInput === 'debug') {
          this.isDebugMode = !this.isDebugMode;
          logInfo(`Debug mode ${this.isDebugMode ? 'enabled' : 'disabled'}`);
          continue;
        }

        if (lowerInput.startsWith('test ')) {
          const toolName = lowerInput.substring(5).trim();
          await this.testSpecificTool(toolName);
          continue;
        }

        // Test tool calling based on user input
        logUser(userInput);
        await this.handleUserInput(userInput);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Error: ${errorMessage}`);

        if (this.isDebugMode) {
          console.error(error);
        }
      }
    }
  }

  private async testSpecificTool(toolName: string): Promise<void> {
    const tool = this.availableTools.find(t => t.name.toLowerCase() === toolName.toLowerCase());

    if (!tool) {
      logError(`Tool "${toolName}" not found`);
      logInfo(`Available tools: ${this.availableTools.map(t => t.name).join(', ')}`);
      return;
    }

    logInfo(`Testing ${tool.name} with sample data...`);

    const testArgs: Record<string, unknown> = {};
    if (tool.arguments) {
      for (const [key, schema] of Object.entries(tool.arguments)) {
        const schemaObj = schema as any;
        if (key.toLowerCase().includes('message')) {
          testArgs[key] = `Direct test of ${tool.name} tool`;
        } else if (key.toLowerCase().includes('ani')) {
          testArgs[key] = '1234567890';
        } else if (schemaObj?.type === 'string') {
          testArgs[key] = 'test_value';
        } else {
          testArgs[key] = 'test';
        }
      }
    }

    try {
      const result = await this.callToolDirectly(tool.name, testArgs);
      logSuccess(`Tool result (JSON-RPC 2.0):`);
      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Tool test failed: ${errorMessage}`);
    }
  }

  private async handleUserInput(input: string): Promise<void> {
    // Simple tool detection based on user input
    const lowerInput = input.toLowerCase();

    if (lowerInput.includes('echo') && this.availableTools.some(t => t.name === 'echo')) {
      logInfo('Detected echo request, calling echo tool with JSON-RPC 2.0...');

      try {
        const message =
          input.replace(/echo/gi, '').trim() || 'Hello from JSON-RPC 2.0 function calling!';
        const result = await this.callToolDirectly('echo', { message });

        logTool(`Echo tool result: ${JSON.stringify(result, null, 2)}`);
        logAssistant(`Echo tool says: ${JSON.stringify(result)}`);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Echo tool failed: ${errorMessage}`);
      }
    } else {
      logAssistant(
        `I understand you said: "${input}". Try saying "echo [message]" to test the echo tool with JSON-RPC 2.0!`
      );
    }
  }

  async cleanup(): Promise<void> {
    logInfo('Cleaning up...');
    this.rl.close();

    try {
      await this.mcpClient.disconnect();
      logInfo('Cleanup completed');
    } catch (error) {
      logDebug('Cleanup had minor issues (this is usually fine)');
    }
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🚀 MCP Integration Tester (JSON-RPC 2.0)${colors.reset}`);
  log(`${colors.gray}MCP Server: ${MCP_SERVER_URL}${colors.reset}`);
  log(`${colors.gray}Using proper JSON-RPC 2.0 format to fix parameter passing${colors.reset}`);

  const tester = new NativeFunctionCallingTester();

  try {
    // Test MCP connection
    const mcpConnected = await tester.testMCPConnection();
    if (!mcpConnected) {
      logError('Cannot proceed without MCP server connection');
      logInfo('\nTo start the MCP server:');
      logInfo('  cd ~/Dev/aidcc-ccczautomationmcpserver');
      logInfo('  npm start');
      process.exit(1);
    }

    // Discover tools
    const tools = await tester.discoverAndListTools();
    if (tools.length === 0) {
      logWarning('No tools found, but continuing...');
    }

    // Test direct tool calling
    await tester.testDirectToolCalling();

    // Interactive mode
    await tester.startInteractiveMode();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Application failed: ${errorMessage}`);
  } finally {
    await tester.cleanup();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\n👋 Goodbye!');
  process.exit(0);
});

process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the native function calling tester
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Failed to start application: ${errorMessage}`);
  process.exit(1);
});
