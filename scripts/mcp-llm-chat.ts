#!/usr/bin/env tsx

/**
 * MCP + LLM Chat Interface
 * 
 * This implements a proper chat interface where:
 * 1. You chat freely with the LLM
 * 2. LLM decides when to use MCP tools
 * 3. Uses the MCP SDK properly (no direct HTTP calls)
 * 4. Implements real function calling with your configured LLM
 * 
 * Usage:
 *   npm run test:mcp
 * 
 * Prerequisites:
 *   1. MCP server running locally
 *   2. LLM configured in .env.development
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import { LLMProviderFactory, LLMProviderType } from '../src/services/bot-service/providers';
import { LLMProvider, ChatMessage } from '../src/services/bot-service/types';
import type { MCPToolMetadata } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:8000/mcp';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  gray: '\x1b[90m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

function logAssistant(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logTool(message: string): void {
  log(`🔧 ${message}`, colors.gray);
}

function logDebug(message: string): void {
  log(`🐛 ${message}`, colors.gray);
}

/**
 * LLM + MCP Chat Interface
 * 
 * This implements proper integration where the LLM can decide to use tools
 */
class MCPLLMChat {
  private mcpClient: MCPClient;
  private mcpService: MCPService;
  private llmProvider: LLMProvider;
  private rl: readline.Interface;
  private availableTools: MCPToolMetadata[] = [];
  private conversationHistory: ChatMessage[] = [];
  private isDebugMode = false;

  constructor() {
    // Initialize MCP
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });
    this.mcpService = new MCPService(this.mcpClient);

    // Initialize LLM
    const providerType = (process.env.LLM_PROVIDER_TYPE || 'litellm') as LLMProviderType;
    this.llmProvider = LLMProviderFactory.getProvider(providerType);

    // Setup readline
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async initialize(): Promise<boolean> {
    try {
      // Test MCP connection
      logHeader('Initializing MCP + LLM Chat');
      logInfo('Testing MCP connection...');
      
      const mcpConnected = await this.mcpClient.testConnection();
      if (!mcpConnected) {
        logError('Failed to connect to MCP server');
        return false;
      }
      logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);

      // Get available tools
      this.availableTools = await this.mcpClient.listTools();
      logSuccess(`Found ${this.availableTools.length} tools: ${this.availableTools.map(t => t.name).join(', ')}`);

      // Initialize LLM
      logInfo('Initializing LLM...');
      await this.llmProvider.initialize();
      logSuccess('LLM initialized');

      // Setup system message with tool information
      const toolDescriptions = this.availableTools.map(tool => 
        `- ${tool.name}: ${tool.description || 'No description'}`
      ).join('\n');

      this.conversationHistory.push({
        role: 'system',
        content: `You are a helpful AI assistant. You have access to the following tools:

${toolDescriptions}

When you want to use a tool, respond with a JSON object in this format:
{
  "tool_call": {
    "name": "tool_name",
    "args": { "param": "value" }
  }
}

Use tools when they would be helpful to answer the user's question. For example:
- If user asks about echoing something, use the echo tool
- If user asks about customer information, use customer lookup tools
- If user just wants to chat, respond normally

Be conversational and helpful!`
      });

      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Initialization failed: ${errorMessage}`);
      return false;
    }
  }

  async startChat(): Promise<void> {
    logHeader('MCP + LLM Chat Started');
    logInfo('Chat freely! The LLM will decide when to use tools.');
    logInfo('Commands: "quit" to exit, "debug" to toggle debug mode, "clear" to clear history');

    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 You: ${colors.reset}`, resolve);
      });
    };

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const userInput = await askQuestion();
        const trimmedInput = userInput.trim();

        if (trimmedInput === '') {
          continue;
        }

        const lowerInput = trimmedInput.toLowerCase();

        // Handle commands
        if (['q', 'quit', 'exit'].includes(lowerInput)) {
          logInfo('Goodbye! 👋');
          break;
        }

        if (lowerInput === 'debug') {
          this.isDebugMode = !this.isDebugMode;
          logInfo(`Debug mode ${this.isDebugMode ? 'enabled' : 'disabled'}`);
          continue;
        }

        if (lowerInput === 'clear') {
          this.conversationHistory = this.conversationHistory.filter(m => m.role === 'system');
          logInfo('Conversation history cleared');
          continue;
        }

        // Process user message
        logUser(userInput);
        await this.processUserMessage(userInput);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Error: ${errorMessage}`);
        
        if (this.isDebugMode) {
          console.error(error);
        }
      }
    }
  }

  private async processUserMessage(userInput: string): Promise<void> {
    // Add user message to history
    this.conversationHistory.push({
      role: 'user',
      content: userInput
    });

    try {
      // Get LLM response
      logInfo('Processing with LLM...');
      const response = await this.llmProvider.getChatCompletion(this.conversationHistory);

      if (this.isDebugMode) {
        logDebug(`Raw LLM response: ${response}`);
      }

      // Check if LLM wants to use a tool
      let toolCall: { name: string; args: Record<string, unknown> } | null = null;
      
      try {
        // Try to parse as JSON tool call
        const cleanedResponse = response.replace(/```json\n?|\n?```/g, '').trim();
        const parsed = JSON.parse(cleanedResponse);
        
        if (parsed.tool_call && typeof parsed.tool_call.name === 'string') {
          toolCall = {
            name: parsed.tool_call.name,
            args: parsed.tool_call.args || {}
          };
          
          if (this.isDebugMode) {
            logDebug(`Detected tool call: ${JSON.stringify(toolCall, null, 2)}`);
          }
        }
      } catch {
        // Not a tool call, treat as normal response
      }

      if (toolCall) {
        // Execute the tool
        await this.executeTool(toolCall.name, toolCall.args);
      } else {
        // Normal response
        logAssistant(response);
        this.conversationHistory.push({
          role: 'assistant',
          content: response
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to process message: ${errorMessage}`);
    }
  }

  private async executeTool(toolName: string, args: Record<string, unknown>): Promise<void> {
    try {
      logTool(`Executing ${toolName} with args: ${JSON.stringify(args)}`);
      
      // Use MCP SDK to call the tool
      const result = await this.mcpService.invokeTool(toolName, args);
      
      logTool(`Tool result: ${JSON.stringify(result, null, 2)}`);

      // Add tool execution to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: null,
        tool_calls: [{
          id: `call_${Date.now()}`,
          type: 'function',
          function: {
            name: toolName,
            arguments: JSON.stringify(args)
          }
        }]
      });

      this.conversationHistory.push({
        role: 'tool',
        content: JSON.stringify(result),
        tool_call_id: `call_${Date.now()}`,
        name: toolName
      });

      // Get final response from LLM with tool result
      logInfo('Getting final response with tool result...');
      const finalResponse = await this.llmProvider.getChatCompletion(this.conversationHistory);
      
      logAssistant(finalResponse);
      
      this.conversationHistory.push({
        role: 'assistant',
        content: finalResponse
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Tool execution failed: ${errorMessage}`);
      
      // Continue conversation even if tool fails
      logAssistant(`I tried to use the ${toolName} tool but it failed. Let me help you another way.`);
    }
  }

  async cleanup(): Promise<void> {
    logInfo('Cleaning up...');
    this.rl.close();
    
    try {
      await this.llmProvider.dispose();
      await this.mcpClient.disconnect();
      logInfo('Cleanup completed');
    } catch (error) {
      logDebug('Cleanup had minor issues (this is usually fine)');
    }
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🚀 MCP + LLM Chat Interface${colors.reset}`);
  log(`${colors.gray}MCP Server: ${MCP_SERVER_URL}${colors.reset}`);
  log(`${colors.gray}LLM Provider: ${process.env.LLM_PROVIDER_TYPE || 'litellm'}${colors.reset}`);
  
  const chat = new MCPLLMChat();
  
  try {
    const initialized = await chat.initialize();
    if (!initialized) {
      logError('Failed to initialize. Please check your MCP server and LLM configuration.');
      process.exit(1);
    }
    
    await chat.startChat();
    
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Application failed: ${errorMessage}`);
  } finally {
    await chat.cleanup();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\n👋 Goodbye!');
  process.exit(0);
});

process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the chat interface
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Failed to start application: ${errorMessage}`);
  process.exit(1);
});
