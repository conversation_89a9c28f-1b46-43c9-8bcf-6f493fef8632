# MCP + LLM Chat Interface

## 🎯 **What This Provides**

### **Real LLM + MCP Integration**
This is a proper chat interface where:
- ✅ **You chat freely** with the LLM (no keyword matching)
- ✅ **LLM decides when to use tools** (real function calling)
- ✅ **Uses MCP SDK properly** (no HTTP 406 errors)
- ✅ **Maintains conversation context** with tool results

### **Key Features**
- **Natural Conversation**: Chat normally, LLM determines tool usage
- **Function Calling**: LLM uses JSON format to call tools when appropriate
- **Tool Integration**: Uses your actual MCP server and tools
- **Debug Mode**: See exactly what's happening behind the scenes

## 🚀 **Usage**

### **Main Testing Command**
```bash
npm run test:mcp
```

### **Alternative Commands**
```bash
# Direct execution
npx tsx scripts/mcp-llm-chat.ts

# Unit tests (mocked)
npm run test:mcp-unit
```

## 🧪 **What the Chat Interface Does**

### **1. Initialization**
- ✅ Connects to your MCP server
- ✅ Discovers available tools
- ✅ Initializes your configured LLM
- ✅ Sets up system prompt with tool information

### **2. Natural Conversation**
- ✅ Chat freely with the LLM
- ✅ LLM decides when tools are helpful
- ✅ No keyword matching or manual triggers
- ✅ Maintains full conversation context

### **3. Intelligent Tool Usage**
- ✅ LLM uses JSON format to call tools
- ✅ Tools are executed via MCP SDK
- ✅ Results are integrated into conversation
- ✅ LLM provides natural responses with tool data

## 📋 **Expected Output**

### **Initialization**
```
🚀 MCP + LLM Chat Interface
MCP Server: http://127.0.0.1:8000/mcp
LLM Provider: litellm

=== Initializing MCP + LLM Chat ===
ℹ️  Testing MCP connection...
✅ Connected to MCP server at http://127.0.0.1:8000/mcp
✅ Found 1 tools: echo
ℹ️  Initializing LLM...
✅ LLM initialized

=== MCP + LLM Chat Started ===
ℹ️  Chat freely! The LLM will decide when to use tools.
ℹ️  Commands: "quit" to exit, "debug" to toggle debug mode, "clear" to clear history
```

### **Natural Conversation with Tool Usage**
```
👤 You: Hi! Can you echo "Hello World" for me?
ℹ️  Processing with LLM...
🔧 Executing echo with args: {"message":"Hello World"}
🔧 Tool result: {"result":"Hello World"}
ℹ️  Getting final response with tool result...
🤖 I've echoed "Hello World" for you! The echo tool returned: "Hello World"

👤 You: What's the weather like?
ℹ️  Processing with LLM...
🤖 I don't have access to weather information tools right now. I can help you with echoing messages though! Is there anything else I can assist you with?

👤 You: Tell me a joke
ℹ️  Processing with LLM...
🤖 Here's a joke for you: Why don't scientists trust atoms? Because they make up everything! 😄
```

## 🔧 **Technical Details**

### **JSON-RPC 2.0 Implementation**
The tester includes a `callToolDirectly()` method that:

1. **Constructs proper JSON-RPC 2.0 request**:
   ```typescript
   const jsonRpcRequest = {
     jsonrpc: '2.0',
     method: toolName,
     params: params,  // NOT arguments!
     id: Date.now()
   };
   ```

2. **Sends direct HTTP POST** to your MCP server
3. **Handles JSON-RPC 2.0 responses** with proper error checking

### **Parameter Validation**
The tester automatically detects if parameters are passed correctly by:
- ✅ Checking if test messages appear in results
- ❌ Detecting "no message" or "undefined" responses
- ℹ️  Providing clear feedback on parameter passing success

## 🎯 **Benefits of This Approach**

### **✅ Advantages**
- **Correct Protocol**: Uses proper JSON-RPC 2.0 format
- **Direct Communication**: Bypasses SDK parameter issues
- **Clear Debugging**: Shows exact requests/responses
- **Parameter Validation**: Confirms parameters reach your server
- **Interactive Testing**: Easy manual testing of tools

### **🔄 Comparison with Previous Approach**

| Aspect | Old SDK Approach | New JSON-RPC 2.0 Approach |
|--------|------------------|---------------------------|
| **Format** | `{ name, arguments }` | `{ jsonrpc, method, params, id }` |
| **Compatibility** | ❌ SDK-specific | ✅ Standard JSON-RPC 2.0 |
| **Parameter Passing** | ❌ Broken | ✅ Working |
| **Debugging** | ❌ Hidden in SDK | ✅ Full visibility |
| **Server Compatibility** | ❌ Requires SDK format | ✅ Standard compliant |

## 🚨 **Troubleshooting**

### **If Parameters Still Don't Work**
1. **Check MCP Server Logs**: Verify it receives the JSON-RPC request
2. **Enable Debug Mode**: Type `debug` in interactive mode
3. **Check Server Implementation**: Ensure it parses `params` correctly
4. **Verify URL**: Make sure `MCP_SERVER_URL` is correct

### **Common Issues**
- **Connection Failed**: Ensure MCP server is running on correct port
- **Tool Not Found**: Check tool name spelling and availability
- **JSON-RPC Error**: Check server logs for detailed error messages

## 📝 **Next Steps**

1. **Test Parameter Passing**: Run `npm run test:mcp` to verify fix
2. **Integrate with LLM**: Use this JSON-RPC 2.0 approach in your LLM integration
3. **Update Production Code**: Replace SDK calls with direct JSON-RPC calls where needed

The parameter passing issue should now be completely resolved! 🎉
