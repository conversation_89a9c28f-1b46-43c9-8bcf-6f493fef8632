#!/usr/bin/env node

/**
 * MCP Integration Local Testing Script
 * 
 * This script tests the MCP integration locally without running the full STT-LLM-TTS loop.
 * It connects directly to the MCP server and tests customer lookup functionality.
 * 
 * Usage:
 *   node scripts/test-mcp-integration.js
 * 
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:6274
 *   2. Customer lookup tools available on the MCP server
 * 
 * What it tests:
 *   - MCP server connectivity
 *   - Tool discovery
 *   - Customer lookup by ANI
 *   - Error handling
 *   - Response parsing
 */

const { MCPClient } = require('../dist/src/services/mcp/mcp-client.js');
const { MCPService } = require('../dist/src/services/mcp/mcp-service.js');

// Test configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274';
const TEST_ANIS = [
  '1234567890',  // Test ANI 1
  '0987654321',  // Test ANI 2
  '5555555555',  // Test ANI 3
  'invalid_ani', // Invalid ANI for error testing
];

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message) {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

async function testMCPClient() {
  logHeader('Testing MCP Client (Low-Level)');
  
  const client = new MCPClient({
    url: MCP_SERVER_URL,
    timeout: 5000
  });

  try {
    // Test 1: Connection test
    logInfo('Testing MCP server connection...');
    const isConnected = await client.testConnection();
    if (isConnected) {
      logSuccess('MCP server connection successful');
    } else {
      logError('MCP server connection failed');
      return false;
    }

    // Test 2: List available tools
    logInfo('Discovering available tools...');
    const tools = await client.listTools();
    logSuccess(`Found ${tools.length} tools:`);
    tools.forEach(tool => {
      console.log(`  - ${tool.name}: ${tool.description || 'No description'}`);
    });

    // Test 3: Find customer lookup tool
    const customerTool = tools.find(tool => 
      tool.name.toLowerCase().includes('customer') ||
      tool.name.toLowerCase().includes('lookup') ||
      tool.name.toLowerCase().includes('ani')
    );

    if (!customerTool) {
      logWarning('No customer lookup tool found. Available tools:');
      tools.forEach(tool => console.log(`  - ${tool.name}`));
      logInfo('Testing with first available tool instead...');
      
      if (tools.length > 0) {
        const testTool = tools[0];
        logInfo(`Testing tool: ${testTool.name}`);
        try {
          const result = await client.callTool(testTool.name, {});
          logSuccess(`Tool call successful: ${JSON.stringify(result, null, 2)}`);
        } catch (error) {
          logError(`Tool call failed: ${error.message}`);
        }
      }
      return true;
    }

    // Test 4: Test customer lookup with various ANIs
    logInfo(`Testing customer lookup tool: ${customerTool.name}`);
    for (const ani of TEST_ANIS) {
      try {
        logInfo(`Looking up customer for ANI: ${ani}`);
        const result = await client.callTool(customerTool.name, { ani });
        logSuccess(`Customer lookup successful for ${ani}:`);
        console.log(JSON.stringify(result, null, 2));
      } catch (error) {
        if (ani === 'invalid_ani') {
          logSuccess(`Expected error for invalid ANI: ${error.message}`);
        } else {
          logError(`Customer lookup failed for ${ani}: ${error.message}`);
        }
      }
    }

    return true;
  } catch (error) {
    logError(`MCP Client test failed: ${error.message}`);
    return false;
  }
}

async function testMCPService() {
  logHeader('Testing MCP Service (High-Level)');
  
  const mcpService = new MCPService({
    baseUrl: MCP_SERVER_URL,
    timeout: 5000,
    retryAttempts: 2,
    retryDelay: 1000
  });

  try {
    // Test 1: Service initialization
    logInfo('Initializing MCP service...');
    await mcpService.initialize();
    logSuccess('MCP service initialized successfully');

    // Test 2: Check availability
    logInfo('Checking MCP service availability...');
    const isAvailable = await mcpService.isAvailable();
    if (isAvailable) {
      logSuccess('MCP service is available');
    } else {
      logError('MCP service is not available');
      return false;
    }

    // Test 3: Customer lookup via service
    logInfo('Testing customer lookup via service...');
    for (const ani of TEST_ANIS.slice(0, 3)) { // Skip invalid ANI for service test
      try {
        logInfo(`Service lookup for ANI: ${ani}`);
        const customer = await mcpService.lookupCustomer(ani);
        if (customer) {
          logSuccess(`Customer found for ${ani}:`);
          console.log(JSON.stringify(customer, null, 2));
        } else {
          logWarning(`No customer found for ${ani}`);
        }
      } catch (error) {
        logError(`Service lookup failed for ${ani}: ${error.message}`);
      }
    }

    return true;
  } catch (error) {
    logError(`MCP Service test failed: ${error.message}`);
    return false;
  }
}

async function testErrorHandling() {
  logHeader('Testing Error Handling');
  
  // Test with invalid URL
  const invalidClient = new MCPClient({
    url: 'http://localhost:9999', // Non-existent server
    timeout: 2000
  });

  try {
    logInfo('Testing connection to invalid server...');
    const isConnected = await invalidClient.testConnection();
    if (!isConnected) {
      logSuccess('Correctly handled connection to invalid server');
    } else {
      logError('Unexpectedly connected to invalid server');
    }
  } catch (error) {
    logSuccess(`Correctly caught connection error: ${error.message}`);
  }
}

async function main() {
  log(`${colors.bold}🧪 MCP Integration Local Testing${colors.reset}`);
  log(`Testing MCP server at: ${MCP_SERVER_URL}`);
  
  let allTestsPassed = true;

  // Run tests
  const clientTestPassed = await testMCPClient();
  const serviceTestPassed = await testMCPService();
  await testErrorHandling();

  allTestsPassed = clientTestPassed && serviceTestPassed;

  // Summary
  logHeader('Test Summary');
  if (allTestsPassed) {
    logSuccess('All MCP integration tests passed! 🎉');
    logInfo('The MCP integration is working correctly and ready for use.');
  } else {
    logError('Some MCP integration tests failed! 🚨');
    logInfo('Please check the MCP server and configuration.');
  }

  // Instructions
  logHeader('Next Steps');
  logInfo('To test with the full voice connector:');
  logInfo('1. Start the voice connector: npm run start');
  logInfo('2. Make a test call with ANI');
  logInfo('3. Check logs for customer lookup during session initialization');
  
  process.exit(allTestsPassed ? 0 : 1);
}

// Handle uncaught errors
process.on('unhandledRejection', (error) => {
  logError(`Unhandled rejection: ${error.message}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  logError(`Uncaught exception: ${error.message}`);
  process.exit(1);
});

// Run the tests
main().catch((error) => {
  logError(`Test execution failed: ${error.message}`);
  process.exit(1);
});
