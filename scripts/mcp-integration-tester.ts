#!/usr/bin/env tsx

/**
 * Unified MCP + LLM Testing Terminal Application
 *
 * This comprehensive CLI tool provides:
 * 1. MCP server connection testing
 * 2. Available tools discovery and listing
 * 3. Interactive chat with LLM that can use MCP tools
 * 4. Function calling - LLM decides when to use tools
 *
 * Usage:
 *   npx tsx scripts/unified-mcp-llm-tester.ts
 *   # or
 *   npm run test:unified
 *
 * Prerequisites:
 *   1. MCP server running locally on http://127.0.0.1:6274
 *   2. LLM configured in .env.development
 *   3. Tools available on MCP server
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import { LLMProviderFactory, LLMProviderType } from '../src/services/bot-service/providers';
import { LLMProvider, ChatMessage, LLMProviderOptions } from '../src/services/bot-service/types';
import type { MCPToolMetadata } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  gray: '\x1b[90m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
} as const;

function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

function logAssistant(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logTool(message: string): void {
  log(`🔧 ${message}`, colors.gray);
}

/**
 * Enhanced LLM Provider that supports function calling with MCP tools
 */
class FunctionCallingLLMProvider {
  private baseProvider: LLMProvider;
  private mcpService: MCPService;
  private availableTools: MCPToolMetadata[] = [];

  constructor(baseProvider: LLMProvider, mcpService: MCPService) {
    this.baseProvider = baseProvider;
    this.mcpService = mcpService;
  }

  async initialize(): Promise<void> {
    await this.baseProvider.initialize();

    // Load available tools
    try {
      this.availableTools = await this.mcpService.client.listTools();
      logInfo(`Loaded ${this.availableTools.length} MCP tools for function calling`);
    } catch (error) {
      logWarning('Failed to load MCP tools for function calling');
      this.availableTools = [];
    }
  }

  /**
   * Convert MCP tools to OpenAI function format
   */
  private getToolsForLLM(): any[] {
    return this.availableTools.map(tool => ({
      type: 'function',
      function: {
        name: tool.name,
        description: tool.description || `Execute ${tool.name} tool`,
        parameters: {
          type: 'object',
          properties: tool.arguments || {},
          required: Object.keys(tool.arguments || {}),
        },
      },
    }));
  }

  /**
   * Enhanced chat completion with function calling support
   */
  async getChatCompletionWithTools(
    messages: ChatMessage[],
    options?: LLMProviderOptions
  ): Promise<{ message: string; toolCalls?: any[] }> {
    // Add system message about available tools if not present
    const systemMessage = messages.find(m => m.role === 'system');
    if (systemMessage && this.availableTools.length > 0) {
      const toolDescriptions = this.availableTools
        .map(tool => `- ${tool.name}: ${tool.description || 'No description'}`)
        .join('\n');

      systemMessage.content += `\n\nAvailable tools:\n${toolDescriptions}\n\nYou can use these tools when appropriate to help the user.`;
    }

    // For now, we'll simulate function calling by detecting when tools should be used
    // and manually calling them. In a real implementation, you'd use the LLM's native function calling.

    const response = await this.baseProvider.getChatCompletion(messages, options);

    // Check if the response suggests using a tool
    const toolCalls = await this.detectAndExecuteToolCalls(response, messages);

    return {
      message: response,
      toolCalls,
    };
  }

  /**
   * Detect if the LLM response suggests using a tool and execute it
   */
  private async detectAndExecuteToolCalls(
    response: string,
    messages: ChatMessage[]
  ): Promise<any[]> {
    const toolCalls: any[] = [];

    // Simple heuristic: look for mentions of customer, ANI, lookup, etc.
    const lastUserMessage = messages.filter(m => m.role === 'user').pop();
    if (!lastUserMessage) {
      return toolCalls;
    }

    const userInput = lastUserMessage.content?.toLowerCase() || '';

    // Check for customer lookup scenarios
    if (
      (userInput.includes('customer') ||
        userInput.includes('account') ||
        userInput.includes('ani')) &&
      this.availableTools.some(t => t.name.toLowerCase().includes('customer'))
    ) {
      // Extract ANI if present
      const aniMatch = userInput.match(/\b\d{10}\b/);
      const ani = aniMatch ? aniMatch[0] : '**********'; // Default test ANI

      const customerTool = this.availableTools.find(t => t.name.toLowerCase().includes('customer'));
      if (customerTool) {
        try {
          logTool(`Executing tool: ${customerTool.name} with ANI: ${ani}`);
          const result = await this.mcpService.invokeTool(customerTool.name, { ani });

          toolCalls.push({
            id: `call_${Date.now()}`,
            type: 'function',
            function: {
              name: customerTool.name,
              arguments: JSON.stringify({ ani }),
            },
            result,
          });

          logTool(`Tool result: ${JSON.stringify(result, null, 2)}`);
        } catch (error) {
          logError(
            `Tool execution failed: ${error instanceof Error ? error.message : String(error)}`
          );
        }
      }
    }

    return toolCalls;
  }

  async dispose(): Promise<void> {
    await this.baseProvider.dispose();
  }
}

/**
 * Main unified testing application
 */
class UnifiedMCPLLMTester {
  private mcpClient: MCPClient;
  private mcpService: MCPService;
  private llmProvider: FunctionCallingLLMProvider;
  private rl: readline.Interface;
  private conversationHistory: ChatMessage[] = [];

  constructor() {
    // Initialize MCP services
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });

    this.mcpService = new MCPService(this.mcpClient);

    // Initialize LLM provider
    const providerType = (process.env.LLM_PROVIDER_TYPE || 'azure') as LLMProviderType;
    const baseLLMProvider = LLMProviderFactory.getProvider(providerType);
    this.llmProvider = new FunctionCallingLLMProvider(baseLLMProvider, this.mcpService);

    // Setup readline for interactive chat
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async testMCPConnection(): Promise<boolean> {
    logHeader('Testing MCP Server Connection');

    try {
      logInfo('Connecting to MCP server...');
      const isConnected = await this.mcpClient.testConnection();

      if (isConnected) {
        logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);
        return true;
      } else {
        logError('Failed to connect to MCP server');
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`MCP connection failed: ${errorMessage}`);
      return false;
    }
  }

  async discoverAndListTools(): Promise<MCPToolMetadata[]> {
    logHeader('Discovering Available Tools');

    try {
      logInfo('Fetching tools from MCP server...');
      const tools = await this.mcpClient.listTools();

      logSuccess(`Found ${tools.length} tools:`);
      tools.forEach((tool, index) => {
        console.log(`  ${index + 1}. ${colors.bold}${tool.name}${colors.reset}`);
        console.log(`     ${tool.description || 'No description'}`);
        if (tool.arguments) {
          const argNames = Object.keys(tool.arguments);
          if (argNames.length > 0) {
            console.log(`     Arguments: ${colors.gray}${argNames.join(', ')}${colors.reset}`);
          }
        }
        console.log();
      });

      return tools;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to discover tools: ${errorMessage}`);
      return [];
    }
  }

  async initializeLLM(): Promise<boolean> {
    logHeader('Initializing LLM with Function Calling');

    try {
      logInfo('Initializing LLM provider...');
      await this.llmProvider.initialize();

      // Set up system message
      this.conversationHistory.push({
        role: 'system',
        content: `You are a helpful AI assistant with access to various tools. You can help users with their requests and use available tools when appropriate. Be conversational and helpful.`,
      });

      logSuccess('LLM initialized with function calling capabilities');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`LLM initialization failed: ${errorMessage}`);
      return false;
    }
  }

  async startInteractiveChat(): Promise<void> {
    logHeader('Interactive Chat with Function Calling');
    logInfo('Chat started! The LLM can use MCP tools when appropriate.');
    logTool('Commands: "quit" to exit, "tools" to list tools, "clear" to clear history');

    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 You: ${colors.reset}`, resolve);
      });
    };

    // eslint-disable-next-line no-constant-condition
    while (true) {
      try {
        const userInput = await askQuestion();
        const trimmedInput = userInput.trim().toLowerCase();

        // Shortcuts: q for quit, t for tools, c for clear
        if (['q', 'quit'].includes(trimmedInput)) {
          logInfo('Goodbye! 👋');
          break;
        }
        if (['t', 'tools'].includes(trimmedInput)) {
          await this.discoverAndListTools();
          continue;
        }
        if (['c', 'clear'].includes(trimmedInput)) {
          this.conversationHistory = this.conversationHistory.filter(m => m.role === 'system');
          logInfo('Conversation history cleared');
          continue;
        }

        // Handle commands
        if (userInput.toLowerCase() === 'quit') {
          logInfo('Goodbye! 👋');
          break;
        }

        if (userInput.toLowerCase() === 'tools') {
          await this.discoverAndListTools();
          continue;
        }

        if (userInput.toLowerCase() === 'clear') {
          this.conversationHistory = this.conversationHistory.filter(m => m.role === 'system');
          logInfo('Conversation history cleared');
          continue;
        }

        if (userInput.trim() === '') {
          continue;
        }

        logUser(userInput);

        // Add user message to history
        this.conversationHistory.push({
          role: 'user',
          content: userInput,
        });

        // Get LLM response with potential tool calls
        logInfo('Processing with LLM...');
        const { message, toolCalls } = await this.llmProvider.getChatCompletionWithTools(
          this.conversationHistory
        );

        // If tools were called, add tool results to conversation
        if (toolCalls && toolCalls.length > 0) {
          // Add assistant message with tool calls
          this.conversationHistory.push({
            role: 'assistant',
            content: null,
            tool_calls: toolCalls.map(tc => ({
              id: tc.id,
              type: tc.type,
              function: tc.function,
            })),
          });

          // Add tool results
          for (const toolCall of toolCalls) {
            this.conversationHistory.push({
              role: 'tool',
              content: JSON.stringify(toolCall.result),
              tool_call_id: toolCall.id,
              name: toolCall.function.name,
            });
          }

          // Get final response from LLM with tool results
          logInfo('Getting final response with tool results...');
          const finalResponse = await this.llmProvider.getChatCompletionWithTools(
            this.conversationHistory
          );

          logAssistant(finalResponse.message);

          // Add final response to history
          this.conversationHistory.push({
            role: 'assistant',
            content: finalResponse.message,
          });
        } else {
          // No tools called, just show the response
          logAssistant(message);

          // Add response to history
          this.conversationHistory.push({
            role: 'assistant',
            content: message,
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Error in chat: ${errorMessage}`);
      }
    }
  }

  async cleanup(): Promise<void> {
    this.rl.close();
    try {
      await this.llmProvider.dispose();
      await this.mcpClient.disconnect();
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

async function main(): Promise<void> {
  log(`${colors.bold}🚀 Unified MCP + LLM Testing Terminal${colors.reset}`);
  log(`MCP Server: ${MCP_SERVER_URL}`);
  log(`LLM Provider: ${process.env.LLM_PROVIDER_TYPE || 'azure'}`);

  const tester = new UnifiedMCPLLMTester();

  try {
    // Step 1: Test MCP connection
    const mcpConnected = await tester.testMCPConnection();
    if (!mcpConnected) {
      logError('Cannot proceed without MCP server connection');
      logInfo('Please ensure MCP server is running:');
      logInfo('  cd ~/Dev/aidcc-ccczautomationmcpserver');
      logInfo('  npm start');
      process.exit(1);
    }

    // Step 2: Discover and list tools
    const tools = await tester.discoverAndListTools();
    if (tools.length === 0) {
      logWarning('No tools found on MCP server');
    }

    // Step 3: Initialize LLM
    const llmInitialized = await tester.initializeLLM();
    if (!llmInitialized) {
      logError('Cannot proceed without LLM initialization');
      logInfo('Please check your LLM configuration in .env.development');
      process.exit(1);
    }

    // Step 4: Start interactive chat
    await tester.startInteractiveChat();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Application failed: ${errorMessage}`);
  } finally {
    await tester.cleanup();
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  log('\n\n👋 Goodbye!');
  process.exit(0);
});

process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the unified tester
main().catch(error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Failed to start application: ${errorMessage}`);
  process.exit(1);
});
