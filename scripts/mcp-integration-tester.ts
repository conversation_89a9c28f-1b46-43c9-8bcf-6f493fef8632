#!/usr/bin/env tsx

/**
 * Enhanced Unified MCP + LLM Testing Terminal Application
 *
 * Improvements:
 * 1. Cleaner function calling integration
 * 2. Better tool call detection and execution
 * 3. Enhanced error handling and recovery
 * 4. More intuitive user commands
 * 5. Better conversation flow management
 */

import * as dotenv from 'dotenv';
import * as readline from 'readline';
import { MCPClient } from '../src/services/mcp/mcp-client';
import { MCPService } from '../src/services/mcp/mcp-service';
import { LLMProviderFactory, LLMProviderType } from '../src/services/bot-service/providers';
import { LLMProvider, ChatMessage, LLMProviderOptions } from '../src/services/bot-service/types';
import type { MCPToolMetadata } from '../src/services/mcp/types';

// Load environment variables
dotenv.config({ path: '.env.development' });

// Configuration
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://127.0.0.1:6274';
const MAX_RETRIES = 3;
const TOOL_TIMEOUT = 30000; // 30 seconds

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m',
  gray: '\x1b[90m',
  reset: '\x1b[0m',
  bold: '\x1b[1m',
  dim: '\x1b[2m',
} as const;

// Enhanced logging functions
function log(message: string, color: string = colors.reset): void {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message: string): void {
  log(`✅ ${message}`, colors.green);
}

function logError(message: string): void {
  log(`❌ ${message}`, colors.red);
}

function logWarning(message: string): void {
  log(`⚠️  ${message}`, colors.yellow);
}

function logInfo(message: string): void {
  log(`ℹ️  ${message}`, colors.blue);
}

function logDebug(message: string): void {
  log(`🐛 ${message}`, colors.dim);
}

function logHeader(message: string): void {
  log(`\n${colors.bold}=== ${message} ===${colors.reset}`);
}

function logUser(message: string): void {
  log(`👤 ${message}`, colors.magenta);
}

function logAssistant(message: string): void {
  log(`🤖 ${message}`, colors.cyan);
}

function logTool(message: string): void {
  log(`🔧 ${message}`, colors.gray);
}

/**
 * Enhanced tool call result interface
 */
interface ToolCallResult {
  id: string;
  name: string;
  args: Record<string, unknown>;
  result: unknown;
  success: boolean;
  error?: string;
  executionTime: number;
}

/**
 * Enhanced function calling provider with better tool integration
 */
class EnhancedFunctionCallingProvider {
  private baseProvider: LLMProvider;
  private mcpService: MCPService;
  private availableTools: MCPToolMetadata[] = [];
  private toolCallHistory: ToolCallResult[] = [];

  constructor(baseProvider: LLMProvider, mcpService: MCPService) {
    this.baseProvider = baseProvider;
    this.mcpService = mcpService;
  }

  async initialize(): Promise<void> {
    logInfo('Initializing enhanced LLM provider...');
    await this.baseProvider.initialize();

    // Load available tools with retry logic
    await this.loadAvailableTools();
  }

  private async loadAvailableTools(retries = MAX_RETRIES): Promise<void> {
    try {
      this.availableTools = await this.mcpService.client.listTools();
      logSuccess(`Loaded ${this.availableTools.length} MCP tools`);

      if (this.availableTools.length > 0) {
        logDebug(`Available tools: ${this.availableTools.map(t => t.name).join(', ')}`);
      }
    } catch (error) {
      if (retries > 0) {
        logWarning(`Failed to load tools, retrying... (${retries} attempts left)`);
        await new Promise(resolve => setTimeout(resolve, 1000));
        return this.loadAvailableTools(retries - 1);
      }
      logError('Failed to load MCP tools after all retries');
      this.availableTools = [];
    }
  }

  /**
   * Generate system prompt with tool information
   */
  private generateSystemPrompt(): string {
    if (this.availableTools.length === 0) {
      return `You are a helpful AI assistant. You currently don't have access to any tools, but you can still help with questions and conversations.`;
    }

    const toolDescriptions = this.availableTools
      .map(tool => {
        const args = tool.arguments ? Object.keys(tool.arguments) : [];
        return `- ${tool.name}: ${tool.description || 'No description'} ${
          args.length > 0 ? `(args: ${args.join(', ')})` : ''
        }`;
      })
      .join('\n');

    return `You are a helpful AI assistant with access to the following tools:

${toolDescriptions}

CRITICAL: When a user asks you to use a tool (like "use the echo tool", "echo something", "try using the tool"), you MUST respond with ONLY a JSON object in this exact format:

{
  "tool_call": {
    "name": "tool_name",
    "args": { "param1": "value1" }
  }
}

Examples of when to use JSON format:
- User: "Use the echo tool to say hello" → Respond with JSON
- User: "Echo me a joke" → Respond with JSON
- User: "Try using the echo tool" → Respond with JSON

Examples of normal responses:
- User: "What tools do you have?" → Respond normally
- User: "Hello" → Respond normally

IMPORTANT:
- When using a tool, respond with ONLY the JSON object, no other text
- After the tool executes, you'll get the result and can respond normally
- Be very literal about tool usage requests`;
  }

  /**
   * Execute a tool call with enhanced error handling and timing
   */
  private async executeToolCall(
    toolName: string,
    args: Record<string, unknown>
  ): Promise<ToolCallResult> {
    const startTime = Date.now();
    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    logTool(`Executing ${toolName} with args: ${JSON.stringify(args)}`);
    logDebug(
      `Tool metadata: ${JSON.stringify(
        this.availableTools.find(t => t.name === toolName),
        null,
        2
      )}`
    );

    try {
      // Validate tool exists
      const toolMeta = this.availableTools.find(t => t.name === toolName);
      if (!toolMeta) {
        throw new Error(`Tool "${toolName}" not found in available tools`);
      }

      logDebug(
        `About to call mcpService.invokeTool with: ${JSON.stringify({ toolName, args }, null, 2)}`
      );

      // Execute with timeout
      const result = await Promise.race([
        this.mcpService.invokeTool(toolName, args),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Tool execution timeout')), TOOL_TIMEOUT)
        ),
      ]);

      logDebug(`Raw tool result from MCP: ${JSON.stringify(result, null, 2)}`);

      const executionTime = Date.now() - startTime;

      const toolResult: ToolCallResult = {
        id: callId,
        name: toolName,
        args,
        result,
        success: true,
        executionTime,
      };

      logTool(`✅ Tool completed in ${executionTime}ms: ${JSON.stringify(result, null, 2)}`);
      this.toolCallHistory.push(toolResult);

      return toolResult;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);

      const toolResult: ToolCallResult = {
        id: callId,
        name: toolName,
        args,
        result: null,
        success: false,
        error: errorMessage,
        executionTime,
      };

      logError(`Tool execution failed after ${executionTime}ms: ${errorMessage}`);
      this.toolCallHistory.push(toolResult);

      return toolResult;
    }
  }

  /**
   * Enhanced chat completion with improved tool calling logic
   */
  async getChatCompletionWithTools(messages: ChatMessage[]): Promise<{
    message: string;
    toolCalls: ToolCallResult[];
    needsFollowup: boolean;
  }> {
    // Ensure we have a proper system message
    const systemMessage = messages.find(m => m.role === 'system');
    if (systemMessage) {
      systemMessage.content = this.generateSystemPrompt();
    } else {
      messages.unshift({
        role: 'system',
        content: this.generateSystemPrompt(),
      });
    }

    // Get initial LLM response
    const response = await this.baseProvider.getChatCompletion(messages);

    // Add debug logging to see what LLM actually returned
    logDebug(`Raw LLM response: ${response}`);

    // Try to parse tool call
    let toolCall: { name: string; args: Record<string, unknown> } | null = null;

    // Clean the response for JSON parsing
    const cleanedResponse = response.replace(/```json\n?|\n?```/g, '').trim();
    logDebug(`Cleaned response for parsing: ${cleanedResponse}`);

    try {
      const parsed = JSON.parse(cleanedResponse);
      logDebug(`Parsed JSON: ${JSON.stringify(parsed, null, 2)}`);

      if (parsed.tool_call && typeof parsed.tool_call.name === 'string') {
        toolCall = {
          name: parsed.tool_call.name,
          args: parsed.tool_call.args || {},
        };
        logDebug(`Extracted tool call: ${JSON.stringify(toolCall, null, 2)}`);
      }
    } catch (parseError) {
      logDebug(
        `JSON parsing failed: ${
          parseError instanceof Error ? parseError.message : String(parseError)
        }`
      );
      logDebug(`Response is not JSON format, checking for fallback tool usage...`);

      // Fallback: detect tool usage intent from natural language
      const lastUserMessage = messages.filter(m => m.role === 'user').pop();
      if (lastUserMessage) {
        const userInput = lastUserMessage.content?.toLowerCase() || '';
        const responseText = response.toLowerCase();

        // Check if user asked for tool usage and LLM mentioned using tools
        if (
          (userInput.includes('echo') || userInput.includes('use') || userInput.includes('try')) &&
          (responseText.includes('echo') || responseText.includes('tool'))
        ) {
          // Force tool execution for echo tool
          const echoTool = this.availableTools.find(t => t.name.toLowerCase() === 'echo');
          if (echoTool) {
            logDebug(`Fallback: Forcing echo tool execution based on user intent`);

            // Extract message from user input or use default
            let message = 'This is a fallback tool execution!';
            if (userInput.includes('joke')) {
              message = "Why don't scientists trust atoms? Because they make up everything!";
            } else if (userInput.includes('hello')) {
              message = 'Hello from the echo tool!';
            }

            toolCall = {
              name: 'echo',
              args: { message },
            };
            logDebug(`Fallback tool call created: ${JSON.stringify(toolCall, null, 2)}`);
          }
        }
      }
    }

    if (toolCall) {
      // Execute the tool
      const toolResult = await this.executeToolCall(toolCall.name, toolCall.args);

      // Add tool execution to conversation and get follow-up response
      const updatedMessages = [
        ...messages,
        { role: 'assistant' as const, content: `I'll use the ${toolCall.name} tool to help you.` },
        {
          role: 'user' as const,
          content: `Tool "${toolCall.name}" executed with result: ${JSON.stringify(
            toolResult.result
          )}. Please provide your response based on this result.`,
        },
      ];

      const followUpResponse = await this.baseProvider.getChatCompletion(updatedMessages);

      return {
        message: followUpResponse,
        toolCalls: [toolResult],
        needsFollowup: false,
      };
    }

    return {
      message: response,
      toolCalls: [],
      needsFollowup: false,
    };
  }

  /**
   * Get tool call statistics
   */
  getToolCallStats(): {
    total: number;
    successful: number;
    failed: number;
    avgExecutionTime: number;
  } {
    const total = this.toolCallHistory.length;
    const successful = this.toolCallHistory.filter(t => t.success).length;
    const failed = total - successful;
    const avgExecutionTime =
      total > 0 ? this.toolCallHistory.reduce((sum, t) => sum + t.executionTime, 0) / total : 0;

    return { total, successful, failed, avgExecutionTime };
  }

  async dispose(): Promise<void> {
    await this.baseProvider.dispose();
  }
}

/**
 * Enhanced unified testing application
 */
class EnhancedUnifiedTester {
  private mcpClient: MCPClient;
  private mcpService: MCPService;
  private llmProvider: EnhancedFunctionCallingProvider;
  private rl: readline.Interface;
  private conversationHistory: ChatMessage[] = [];
  private isDebugMode = false;

  constructor() {
    this.mcpClient = new MCPClient({
      url: MCP_SERVER_URL,
      timeout: 10000,
    });

    this.mcpService = new MCPService(this.mcpClient);

    const providerType = (process.env.LLM_PROVIDER_TYPE || 'azure') as LLMProviderType;
    const baseLLMProvider = LLMProviderFactory.getProvider(providerType);
    this.llmProvider = new EnhancedFunctionCallingProvider(baseLLMProvider, this.mcpService);

    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async testMCPConnection(): Promise<boolean> {
    logHeader('Testing MCP Server Connection');

    for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        logInfo(`Connection attempt ${attempt}/${MAX_RETRIES}...`);
        const isConnected = await this.mcpClient.testConnection();

        if (isConnected) {
          logSuccess(`Connected to MCP server at ${MCP_SERVER_URL}`);
          return true;
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logWarning(`Attempt ${attempt} failed: ${errorMessage}`);

        if (attempt < MAX_RETRIES) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }

    logError('Failed to connect to MCP server after all attempts');
    return false;
  }

  async discoverAndListTools(): Promise<MCPToolMetadata[]> {
    logHeader('Discovering Available Tools');

    try {
      const tools = await this.mcpClient.listTools();

      if (tools.length === 0) {
        logWarning('No tools found on MCP server');
        return [];
      }

      logSuccess(`Found ${tools.length} tools:`);
      tools.forEach((tool, index) => {
        console.log(`  ${colors.bold}${index + 1}. ${tool.name}${colors.reset}`);
        console.log(`     ${tool.description || colors.dim + 'No description' + colors.reset}`);

        if (tool.arguments && Object.keys(tool.arguments).length > 0) {
          const argInfo = Object.entries(tool.arguments)
            .map(([key, schema]) => {
              const type = (schema as any)?.type || 'unknown';
              const required = (schema as any)?.required ? '*' : '';
              return `${key}${required}:${type}`;
            })
            .join(', ');
          console.log(`     ${colors.gray}Args: ${argInfo}${colors.reset}`);
        }
        console.log();
      });

      return tools;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Failed to discover tools: ${errorMessage}`);
      return [];
    }
  }

  async initializeLLM(): Promise<boolean> {
    logHeader('Initializing Enhanced LLM Provider');

    try {
      await this.llmProvider.initialize();

      // Initialize conversation with system message
      this.conversationHistory = [
        {
          role: 'system',
          content: '', // Will be populated by the provider
        },
      ];

      logSuccess('Enhanced LLM provider initialized successfully');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`LLM initialization failed: ${errorMessage}`);
      return false;
    }
  }

  private showHelp(): void {
    console.log(`
${colors.bold}Available Commands:${colors.reset}
  ${colors.cyan}help, h${colors.reset}     - Show this help message
  ${colors.cyan}quit, q${colors.reset}     - Exit the application
  ${colors.cyan}tools, t${colors.reset}    - List available MCP tools
  ${colors.cyan}clear, c${colors.reset}    - Clear conversation history
  ${colors.cyan}stats, s${colors.reset}    - Show tool call statistics
  ${colors.cyan}debug${colors.reset}       - Toggle debug mode (currently ${
      this.isDebugMode ? 'ON' : 'OFF'
    })
  ${colors.cyan}test <tool>${colors.reset} - Test a specific tool (e.g., "test echo")

${colors.bold}Examples:${colors.reset}
  "Use the echo tool to say hello"
  "Look up customer information for phone number 1234567890"
  "What tools are available?"
    `);
  }

  private async testSpecificTool(toolName: string): Promise<void> {
    logInfo(`Testing tool: ${toolName}`);

    const tools = await this.mcpClient.listTools();
    const tool = tools.find(t => t.name.toLowerCase() === toolName.toLowerCase());

    if (!tool) {
      logError(`Tool "${toolName}" not found`);
      logInfo(`Available tools: ${tools.map(t => t.name).join(', ')}`);
      return;
    }

    // Generate test arguments
    const testArgs: Record<string, unknown> = {};
    if (tool.arguments) {
      for (const [key, schema] of Object.entries(tool.arguments)) {
        const schemaObj = schema as any;
        if (key.toLowerCase().includes('message')) {
          testArgs[key] = `Test message for ${tool.name}`;
        } else if (key.toLowerCase().includes('ani') || key.toLowerCase().includes('phone')) {
          testArgs[key] = '1234567890';
        } else if (schemaObj?.type === 'string') {
          testArgs[key] = 'test_value';
        } else if (schemaObj?.type === 'number') {
          testArgs[key] = 42;
        } else if (schemaObj?.type === 'boolean') {
          testArgs[key] = true;
        } else {
          testArgs[key] = 'test';
        }
      }
    }

    try {
      const result = await this.mcpService.invokeTool(tool.name, testArgs);
      logSuccess(`Tool test successful:`);
      console.log(JSON.stringify(result, null, 2));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logError(`Tool test failed: ${errorMessage}`);
    }
  }

  async startInteractiveChat(): Promise<void> {
    logHeader('Enhanced Interactive Chat');
    logInfo('Chat started! Type "help" for commands or start chatting.');

    const askQuestion = (): Promise<string> => {
      return new Promise(resolve => {
        this.rl.question(`\n${colors.magenta}👤 You: ${colors.reset}`, resolve);
      });
    };

    while (true) {
      try {
        const userInput = await askQuestion();
        const trimmedInput = userInput.trim();

        if (trimmedInput === '') {
          continue;
        }

        const lowerInput = trimmedInput.toLowerCase();

        // Handle commands
        if (['q', 'quit', 'exit'].includes(lowerInput)) {
          logInfo('Goodbye! 👋');
          break;
        }

        if (['h', 'help'].includes(lowerInput)) {
          this.showHelp();
          continue;
        }

        if (['t', 'tools'].includes(lowerInput)) {
          await this.discoverAndListTools();
          continue;
        }

        if (['c', 'clear'].includes(lowerInput)) {
          this.conversationHistory = this.conversationHistory.filter(m => m.role === 'system');
          logInfo('Conversation history cleared');
          continue;
        }

        if (['s', 'stats'].includes(lowerInput)) {
          const stats = this.llmProvider.getToolCallStats();
          logInfo(`Tool Call Statistics:`);
          console.log(`  Total calls: ${stats.total}`);
          console.log(`  Successful: ${stats.successful}`);
          console.log(`  Failed: ${stats.failed}`);
          console.log(`  Avg execution time: ${stats.avgExecutionTime.toFixed(2)}ms`);
          continue;
        }

        if (lowerInput === 'debug') {
          this.isDebugMode = !this.isDebugMode;
          logInfo(`Debug mode ${this.isDebugMode ? 'enabled' : 'disabled'}`);
          continue;
        }

        if (lowerInput.startsWith('test ')) {
          const toolName = lowerInput.substring(5).trim();
          await this.testSpecificTool(toolName);
          continue;
        }

        // Process as chat message
        logUser(userInput);

        this.conversationHistory.push({
          role: 'user',
          content: userInput,
        });

        if (this.isDebugMode) {
          logDebug(`Conversation history length: ${this.conversationHistory.length}`);
        }

        logInfo('Processing...');

        const { message, toolCalls } = await this.llmProvider.getChatCompletionWithTools(
          this.conversationHistory
        );

        if (toolCalls.length > 0) {
          const successfulCalls = toolCalls.filter(tc => tc.success);
          const failedCalls = toolCalls.filter(tc => !tc.success);

          if (successfulCalls.length > 0) {
            logTool(`✅ Executed ${successfulCalls.length} tool(s) successfully`);
          }
          if (failedCalls.length > 0) {
            logTool(`❌ ${failedCalls.length} tool(s) failed`);
          }
        }

        logAssistant(message);

        this.conversationHistory.push({
          role: 'assistant',
          content: message,
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logError(`Chat error: ${errorMessage}`);

        if (this.isDebugMode) {
          console.error(error);
        }
      }
    }
  }

  async cleanup(): Promise<void> {
    logInfo('Cleaning up...');
    this.rl.close();

    try {
      await this.llmProvider.dispose();
      await this.mcpClient.disconnect();
      logInfo('Cleanup completed');
    } catch (error) {
      logDebug('Cleanup had minor issues (this is usually fine)');
    }
  }
}

// Main execution
async function main(): Promise<void> {
  log(`${colors.bold}🚀 Enhanced Unified MCP + LLM Testing Terminal${colors.reset}`);
  log(`${colors.dim}MCP Server: ${MCP_SERVER_URL}`);
  log(`LLM Provider: ${process.env.LLM_PROVIDER_TYPE || 'azure'}${colors.reset}`);

  const tester = new EnhancedUnifiedTester();

  try {
    // Test MCP connection
    const mcpConnected = await tester.testMCPConnection();
    if (!mcpConnected) {
      logError('Cannot proceed without MCP server connection');
      logInfo('\nTo start the MCP server:');
      logInfo('  cd ~/Dev/aidcc-ccczautomationmcpserver');
      logInfo('  npm start');
      process.exit(1);
    }

    // Discover tools
    const tools = await tester.discoverAndListTools();
    if (tools.length === 0) {
      logWarning('No tools found, but continuing with chat-only mode');
    }

    // Initialize LLM
    const llmInitialized = await tester.initializeLLM();
    if (!llmInitialized) {
      logError('Cannot proceed without LLM initialization');
      logInfo('Please check your LLM configuration in .env.development');
      process.exit(1);
    }

    // Start interactive session
    await tester.startInteractiveChat();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Application failed: ${errorMessage}`);
    process.exit(1);
  } finally {
    await tester.cleanup();
  }
}

// Process handlers
process.on('SIGINT', async () => {
  log('\n\n👋 Shutting down gracefully...');
  process.exit(0);
});

process.on('unhandledRejection', error => {
  const errorMessage = error instanceof Error ? error.message : String(error);
  logError(`Unhandled rejection: ${errorMessage}`);
  process.exit(1);
});

// Run the application
if (require.main === module) {
  main().catch(error => {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logError(`Failed to start: ${errorMessage}`);
    process.exit(1);
  });
}
