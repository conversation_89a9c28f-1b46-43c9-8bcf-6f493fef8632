#!/bin/bash

# Get the current version ID from the first argument
CURRENT_VERSION_ID=$1

echo "Bumping version to $CURRENT_VERSION_ID"

# Update the image tag in connector.yaml
sed -i "s|- image: network.git.cz.o2:5005/deployments/aidcc-ccczautomationllmconnector:.*$|- image: network.git.cz.o2:5005/deployments/aidcc-ccczautomationllmconnector:$CURRENT_VERSION_ID|g" charts/ccczautomationllmconnector/templates/deployment.yaml

# Update the package version in package.json
sed -i "s|\"version\":.*$|\"version\": \"$CURRENT_VERSION_ID\",|g" package.json

# Update the version and appVersion in Chart.yaml (only at the beginning of the line)
sed -i "s|^version:.*$|version: ${CURRENT_VERSION_ID}|g" charts/ccczautomationllmconnector/Chart.yaml
sed -i "s|^appVersion:.*$|appVersion: ${CURRENT_VERSION_ID}|g" charts/ccczautomationllmconnector/Chart.yaml

# Update the version file
echo $CURRENT_VERSION_ID > version
