{"branches": ["main"], "plugins": ["@semantic-release/commit-analyzer", "@semantic-release/release-notes-generator", ["@semantic-release/npm", {"npmPublish": false}], ["@semantic-release/exec", {"prepareCmd": "./scripts/bump_version.sh ${nextRelease.version}"}], ["@semantic-release/git", {"assets": ["package.json", "charts/ccczautomationllmconnector/templates/deployment.yaml", "charts/ccczautomationllmconnector/Chart.yaml", "CHANGELOG.md", "version"], "message": "chore(release): ${nextRelease.version} [release]\n\n${nextRelease.notes}"}], ["@semantic-release/gitlab", {"gitlabUrl": "https://network.git.cz.o2/", "assets": ["package.json", "charts/ccczautomationllmconnector/templates/deployment.yaml", "charts/ccczautomationllmconnector/Chart.yaml", "CHANGELOG.md", "version"]}]]}