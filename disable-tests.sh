#!/bin/bash

# Disable state machine tests by renaming them
cd /Users/<USER>/Dev/genesys-cloud-audio-connector\ 2

# Rename files in __tests__ directory
mv src/session/state-machine/__tests__/disconnecting-and-closed-state-actions.test.ts src/session/state-machine/__tests__/disconnecting-and-closed-state-actions.test.ts.disabled
mv src/session/state-machine/__tests__/idle-state-actions.test.ts src/session/state-machine/__tests__/idle-state-actions.test.ts.disabled

# Rename files in tests directory
mv src/session/state-machine/tests/disconnecting-and-closed-state-actions.test.ts src/session/state-machine/tests/disconnecting-and-closed-state-actions.test.ts.disabled
mv src/session/state-machine/tests/idle-state-actions.test.ts src/session/state-machine/tests/idle-state-actions.test.ts.disabled
mv src/session/state-machine/tests/initialize-asr-service-action.test.ts src/session/state-machine/tests/initialize-asr-service-action.test.ts.disabled
mv src/session/state-machine/tests/playing-state-actions.test.ts src/session/state-machine/tests/playing-state-actions.test.ts.disabled
mv src/session/state-machine/tests/process-bot-start-action.test.ts src/session/state-machine/tests/process-bot-start-action.test.ts.disabled
mv src/session/state-machine/tests/processing-bot-state-actions.test.ts src/session/state-machine/tests/processing-bot-state-actions.test.ts.disabled
mv src/session/state-machine/tests/processing-responding-state-actions.test.ts src/session/state-machine/tests/processing-responding-state-actions.test.ts.disabled
mv src/session/state-machine/tests/set-conversation-id-action.test.ts src/session/state-machine/tests/set-conversation-id-action.test.ts.disabled
mv src/session/state-machine/tests/set-playback-state-action.test.ts src/session/state-machine/tests/set-playback-state-action.test.ts.disabled

echo "Tests disabled successfully. To re-enable them, run the enable-tests.sh script."
