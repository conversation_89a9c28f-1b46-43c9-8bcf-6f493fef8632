/* global jest */

// Set up environment variables for testing
process.env.ENABLE_PERF_MONITOR = 'true';
process.env.METRICS_LOG_LEVEL = '0';

// Increase timeout for tests
jest.setTimeout(30000); // 30 seconds

// Configure Jest to fail on unhandled promise rejections
process.on('unhandledRejection', reason => {
  // Using process.stderr.write instead of console.error to avoid linting warning
  process.stderr.write(`Unhandled Promise Rejection: ${reason}\n`);
  process.exit(1);
});

// Silence console output during tests
global.console = {
  ...console,
  log: jest.fn(),
  info: jest.fn(),
  debug: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
