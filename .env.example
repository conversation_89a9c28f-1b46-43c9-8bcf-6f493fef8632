# ======================================================================
# SPEECH-TO-TEXT (STT) CONFIGURATION
# ======================================================================
# Choose which speech service to use for STT: 'azure' or 'google'
ASR_SERVICE=<speech-service>

# Google Cloud STT Configuration (when SPEECH_SERVICE=google)
GOOGLE_PROJECT_ID=<google-project-id>
GOOGLE_APPLICATION_CREDENTIALS=<path-to-credentials-file>

# Azure STT Configuration (when SPEECH_SERVICE=azure)
AZURE_SPEECH_KEY=<azure-speech-key>
AZURE_SPEECH_REGION=<azure-region>

# ======================================================================
# TEXT-TO-SPEECH (TTS) CONFIGURATION
# ======================================================================
# Choose which service to use for TTS: 'azure', 'google', or 'elevenlabs'
# If not specified, will use the same as SPEECH_SERVICE
TTS_PROVIDER=<tts-provider>

# ElevenLabs TTS Configuration (when TTS_PROVIDER=elevenlabs)
ELEVENLABS_API_KEY=<elevenlabs-api-key>
ELEVENLABS_VOICE_ID=<elevenlabs-voice-id>

# Google Cloud TTS Configuration (when TTS_PROVIDER=google)
# Uses the same credentials as STT (GOOGLE_PROJECT_ID and GOOGLE_APPLICATION_CREDENTIALS)

# Azure TTS Configuration (when TTS_PROVIDER=azure)
# Uses the same credentials as STT (AZURE_SPEECH_KEY and AZURE_SPEECH_REGION)

# ======================================================================
# LANGUAGE MODEL (LLM) CONFIGURATION
# ======================================================================
# Choose which LLM provider to use: 'azure' or 'litellm'
LLM_PROVIDER_TYPE=<llm-provider>

# LiteLLM Configuration (when LLM_PROVIDER_TYPE=litellm)
LITELLM_API_KEY=<litellm-api-key>
LITELLM_ENDPOINT=<litellm-endpoint>
LITELLM_MODEL=<litellm-model>
LITELLM_INSECURE_SSL=<true/false>

# Azure OpenAI Configuration (when LLM_PROVIDER_TYPE=azure)
AZURE_OPENAI_KEY=<azure-openai-key>
AZURE_OPENAI_ENDPOINT=<azure-openai-endpoint>
AZURE_OPENAI_DEPLOYMENT_NAME=<azure-openai-deployment>

# ======================================================================
# SERVER CONFIGURATION
# ======================================================================
PORT=<port-number>
IS_DEBUG_MODE=<true|false>
DEBUG_AUDIO=<true|false>

# ======================================================================
# MONITORING & PERFORMANCE
# ======================================================================
# Enable performance monitoring and debugging
ENABLE_PERF_MONITOR=<true|false>

# ======================================================================
# DATABASE CONFIGURATION
# ======================================================================
# For local development, set MOCKING_DATABASE=true to use in-memory storage
MOCKING_DATABASE=<true|false>

# Required when MOCKING_DATABASE=false
DATABASE_URL=<database-connection-string>

# ======================================================================
# NETWORK CONFIGURATION
# ======================================================================
# Proxy Configuration
HTTP_PROXY=<http-proxy-url>
NO_PROXY=<comma-separated-list-of-hosts-to-bypass>

# gRPC Configuration for Google APIs
# Force IPv4 to avoid IPv6 connectivity issues
GRPC_DNS_RESOLVER=<resolver-type>

# ======================================================================
# BARGE-IN AND STREAMING CONFIGURATION
# ======================================================================
# Enable barge-in to allow interrupting the system during audio playback
ENABLE_BARGE_IN=<true|false>
BARGE_IN_CONFIDENCE_THRESHOLD=<threshold-value>
INTERIM_TRANSCRIPT_STABILITY_THRESHOLD=<threshold-value>

# Enable stable stream for better pause detection
ENABLE_STABLE_STREAM=<true|false>
PAUSE_THRESHOLD_MS=<milliseconds>

# ASR Streaming Mode Configuration
# Options: 'standard', 'hybrid', or 'continuous'
# - standard: Uses ASR's built-in finalization logic
# - hybrid: Uses both ASR finalization and pause detection
# - continuous: Configures ASR for continuous streaming without finalization
ASR_STREAMING_MODE=<streaming-mode>

# Enable debug logging for barge-in
DEBUG_BARGE_IN=<true|false>

# ======================================================================
# LOGGING CONFIGURATION
# ======================================================================
# Log levels: ERROR, WARN, INFO, DEBUG
LOG_LEVEL=<log-level>
METRICS_LOG_LEVEL=<metrics-log-level>

# Enable/disable specific log categories
LOG_HTTP_REQUESTS=<true|false>
LOG_HTTP_RESPONSES=<true|false>
LOG_OPENAI_REQUESTS=<true|false>
LOG_OPENAI_RESPONSES=<true|false>
LOG_WEBSOCKET_MESSAGES=<true|false>
LOG_ASR_INTERIM=<true|false>

# ASR logging configuration
LOG_ASR_RAW_RESPONSES=<true|false>
LOG_ASR_PARSED_RESULTS=<true|false>
LOG_ASR_UNSTABLE_TRANSCRIPTS=<true|false>

# OpenAI log format: 'minimal', 'standard', or 'detailed'
# This is used when LOG_OPENAI_REQUESTS or LOG_OPENAI_RESPONSES is true
OPENAI_LOG_FORMAT=<log-format>
