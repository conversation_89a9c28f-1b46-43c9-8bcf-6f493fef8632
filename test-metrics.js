// Test script for metrics with aiReply
const { PerformanceMetricsStore } = require('./dist/services/monitoring/performance-metrics-store');

// Enable metrics logging
process.env.METRICS_LOG_LEVEL = '0';
process.env.ENABLE_PERF_MONITOR = 'true';

// Create a test function
async function testMetrics() {
  console.log('Starting metrics test...');

  // Get the metrics store instance
  const metricsStore = PerformanceMetricsStore.getInstance();

  // Initialize a conversation
  const conversationId = 'test-conv-' + Date.now();
  const ani = '123456789';
  metricsStore.initializeConversation(conversationId, ani);
  console.log(`Initialized conversation: ${conversationId}`);

  // Initialize a request
  const requestId = 'test-req-' + Date.now();
  metricsStore.initializeRequest(conversationId, requestId);
  console.log(`Initialized request: ${requestId}`);

  // Start and end phases
  metricsStore.startPhase(conversationId, requestId, 'speechToText');
  await sleep(500); // Simulate 500ms for speech-to-text
  metricsStore.endPhase(conversationId, requestId, 'speechToText');

  metricsStore.startPhase(conversationId, requestId, 'llmProcessing');
  await sleep(1000); // Simulate 1000ms for LLM processing
  metricsStore.endPhase(conversationId, requestId, 'llmProcessing');

  metricsStore.startPhase(conversationId, requestId, 'textToSpeech');
  await sleep(700); // Simulate 700ms for text-to-speech
  metricsStore.endPhase(conversationId, requestId, 'textToSpeech');

  // Finalize the request with user input and AI reply
  const userInput = 'What is the weather today?';
  const aiReply = 'The weather today is sunny with a high of 75 degrees.';
  metricsStore.finalizeRequest(conversationId, requestId, userInput, aiReply);

  // Manually check if the AI reply was set
  const request = metricsStore
    .getConversationMetrics(conversationId)
    .requests.find(req => req.requestId === requestId);
  console.log('\nRequest object:');
  console.log(JSON.stringify(request, null, 2));
  console.log('\nAI Reply directly from request object:');
  console.log(request.aiReply);

  // Log the interaction metrics
  metricsStore.logInteractionMetrics(conversationId, requestId);

  // Finalize the conversation
  metricsStore.finalizeConversation(conversationId);

  // Get and display the conversation metrics
  const metrics = metricsStore.getConversationMetrics(conversationId);
  console.log('\nConversation Metrics:');
  console.log(JSON.stringify(metrics, null, 2));

  // Display the AI reply specifically
  console.log('\nAI Reply from metrics:');
  if (metrics.requests && metrics.requests.length > 0 && metrics.requests[0].aiReply) {
    console.log(metrics.requests[0].aiReply);
  } else {
    console.log('AI reply not found in metrics');
  }

  console.log('\nMetrics test complete.');
}

// Helper function to sleep
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Run the test
testMetrics().catch(error => {
  console.error('Error in metrics test:', error);
});
